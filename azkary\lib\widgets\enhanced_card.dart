import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// بطاقة محسنة مع رسوم متحركة وتأثيرات بصرية
class EnhancedCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Color? color;
  final double? elevation;
  final BorderRadius? borderRadius;
  final bool enableHoverEffect;
  final bool enableTapAnimation;
  final Duration animationDuration;
  final Curve animationCurve;
  final Color? shadowColor;
  final Border? border;
  final Gradient? gradient;

  const EnhancedCard({
    super.key,
    required this.child,
    this.onTap,
    this.margin,
    this.padding = const EdgeInsets.all(16),
    this.color,
    this.elevation,
    this.borderRadius,
    this.enableHoverEffect = true,
    this.enableTapAnimation = true,
    this.animationDuration = const Duration(milliseconds: 200),
    this.animationCurve = Curves.easeInOut,
    this.shadowColor,
    this.border,
    this.gradient,
  });

  @override
  State<EnhancedCard> createState() => _EnhancedCardState();
}

class _EnhancedCardState extends State<EnhancedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: widget.animationCurve,
      ),
    );

    _elevationAnimation = Tween<double>(begin: 0.0, end: 2.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: widget.animationCurve,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.enableTapAnimation) {
      _animationController.forward();
      HapticFeedback.lightImpact();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.enableTapAnimation) {
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (widget.enableTapAnimation) {
      _animationController.reverse();
    }
  }

  void _handleHoverEnter(PointerEnterEvent event) {
    if (widget.enableHoverEffect) {
      setState(() => _isHovered = true);
    }
  }

  void _handleHoverExit(PointerExitEvent event) {
    if (widget.enableHoverEffect) {
      setState(() => _isHovered = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cardTheme = theme.cardTheme;

    // تحديد الألوان والخصائص
    final effectiveColor = widget.color ?? cardTheme.color ?? theme.cardColor;
    final effectiveElevation = widget.elevation ?? cardTheme.elevation ?? 2.0;
    final effectiveBorderRadius =
        widget.borderRadius ??
        (cardTheme.shape as RoundedRectangleBorder?)?.borderRadius ??
        BorderRadius.circular(16);
    final effectiveShadowColor =
        widget.shadowColor ??
        cardTheme.shadowColor ??
        Colors.black.withValues(alpha: 0.1);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.enableTapAnimation ? _scaleAnimation.value : 1.0,
          child: Container(
            margin: widget.margin ?? cardTheme.margin,
            child: MouseRegion(
              onEnter: _handleHoverEnter,
              onExit: _handleHoverExit,
              child: GestureDetector(
                onTapDown: _handleTapDown,
                onTapUp: _handleTapUp,
                onTapCancel: _handleTapCancel,
                onTap: widget.onTap,
                child: AnimatedContainer(
                  duration: widget.animationDuration,
                  curve: widget.animationCurve,
                  decoration: BoxDecoration(
                    color: effectiveColor,
                    gradient: widget.gradient,
                    borderRadius: effectiveBorderRadius,
                    border:
                        widget.border ??
                        ((cardTheme.shape is RoundedRectangleBorder)
                            ? Border.fromBorderSide(
                              (cardTheme.shape as RoundedRectangleBorder).side,
                            )
                            : null),
                    boxShadow: [
                      BoxShadow(
                        color: effectiveShadowColor,
                        blurRadius:
                            effectiveElevation +
                            (_isHovered ? _elevationAnimation.value : 0),
                        offset: Offset(
                          0,
                          (effectiveElevation +
                                  (_isHovered
                                      ? _elevationAnimation.value
                                      : 0)) /
                              2,
                        ),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: effectiveBorderRadius,
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius:
                            effectiveBorderRadius is BorderRadius
                                ? effectiveBorderRadius
                                : BorderRadius.circular(16),
                        onTap: widget.onTap,
                        splashColor: theme.colorScheme.primary.withValues(
                          alpha: 0.1,
                        ),
                        highlightColor: theme.colorScheme.primary.withValues(
                          alpha: 0.05,
                        ),
                        child: Padding(
                          padding: widget.padding ?? EdgeInsets.zero,
                          child: widget.child,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// بطاقة إسلامية مخصصة مع نمط إسلامي
class IslamicCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final String? title;
  final Widget? leading;
  final Widget? trailing;
  final bool showPattern;

  const IslamicCard({
    super.key,
    required this.child,
    this.onTap,
    this.margin,
    this.padding = const EdgeInsets.all(16),
    this.title,
    this.leading,
    this.trailing,
    this.showPattern = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return EnhancedCard(
      onTap: onTap,
      margin: margin,
      padding: EdgeInsets.zero,
      child: Stack(
        children: [
          // النمط الإسلامي في الخلفية
          if (showPattern)
            Positioned.fill(
              child: Opacity(
                opacity: 0.08,
                child: CustomPaint(
                  painter: _IslamicPatternPainter(
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),
            ),

          // المحتوى
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان والأيقونات
              if (title != null || leading != null || trailing != null)
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                  child: Row(
                    children: [
                      if (leading != null) ...[
                        leading!,
                        const SizedBox(width: 12),
                      ],
                      if (title != null)
                        Expanded(
                          child: Text(
                            title!,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                        ),
                      if (trailing != null) trailing!,
                    ],
                  ),
                ),

              // المحتوى الرئيسي
              Padding(padding: padding!, child: child),
            ],
          ),
        ],
      ),
    );
  }
}

/// رسام النمط الإسلامي للبطاقات
class _IslamicPatternPainter extends CustomPainter {
  final Color color;

  _IslamicPatternPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    const patternSize = 40.0;
    final horizontalCount = (size.width / patternSize).ceil();
    final verticalCount = (size.height / patternSize).ceil();

    for (int i = 0; i < horizontalCount; i++) {
      for (int j = 0; j < verticalCount; j++) {
        final centerX = i * patternSize + patternSize / 2;
        final centerY = j * patternSize + patternSize / 2;

        // رسم نجمة صغيرة
        _drawStar(canvas, paint, Offset(centerX, centerY), patternSize * 0.3);
      }
    }
  }

  void _drawStar(Canvas canvas, Paint paint, Offset center, double size) {
    final path = Path();
    const points = 8;

    for (int i = 0; i < points; i++) {
      final x =
          center.dx +
          size *
              0.5 *
              (i % 2 == 0 ? 1 : 0.5) *
              (i == 0 ? 1 : (i % 2 == 0 ? 1 : 0.5)) *
              (i < points / 2 ? 1 : -1);
      final y =
          center.dy +
          size *
              0.5 *
              (i % 2 == 0 ? 1 : 0.5) *
              (i < points / 4 || i >= 3 * points / 4 ? -1 : 1);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
