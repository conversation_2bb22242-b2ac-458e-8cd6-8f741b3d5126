import 'package:flutter/material.dart';

/// فئة تحسين أداء الصفحة الرئيسية
class HomeScreenOptimizer {
  // إعدادات محسنة للصفحة الرئيسية
  static const Duration _mediumAnimation = Duration(milliseconds: 200);
  static const Curve _optimizedCurve = Curves.easeOut;

  /// تحسين تحميل البيانات
  static Future<void> optimizedDataLoading({
    required Future<void> Function() loadFunction,
    required VoidCallback onComplete,
    VoidCallback? onError,
  }) async {
    try {
      // تحميل البيانات مع تأخير صغير لتحسين الأداء
      await Future.delayed(const Duration(milliseconds: 50));
      await loadFunction();
      onComplete();
    } catch (e) {
      if (onError != null) {
        onError();
      }
    }
  }

  /// تحسين بطاقة السؤال اليومي
  static Widget optimizedDailyQuestionCard({
    required Widget child,
    required VoidCallback onTap,
    required BuildContext context,
  }) {
    return AnimatedContainer(
      duration: _mediumAnimation,
      curve: _optimizedCurve,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: _createOptimizedGradient(context),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withAlpha(76),
                width: 1,
              ),
              boxShadow: _createOptimizedShadow(context),
            ),
            child: child,
          ),
        ),
      ),
    );
  }

  /// تحسين بطاقة ذكر اليوم
  static Widget optimizedDailyZikrCard({
    required Widget child,
    required BuildContext context,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: _createOptimizedShadow(context, opacity: 0.1),
      ),
      child: onTap != null
          ? InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(12),
              child: child,
            )
          : child,
    );
  }

  /// تحسين بطاقات تصنيفات الأذكار
  static Widget optimizedCategoryCard({
    required Widget child,
    required VoidCallback onTap,
    required BuildContext context,
    required int index,
  }) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 100 + (index * 15)), // تحسين الأداء
      tween: Tween(begin: 0.0, end: 1.0),
      curve: _optimizedCurve,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 5 * (1 - value)), // تقليل المسافة
          child: Opacity(
            opacity: value,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: onTap,
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: _createOptimizedShadow(context, opacity: 0.08),
                    ),
                    child: child,
                  ),
                ),
              ),
            ),
          ),
        );
      },
      child: child,
    );
  }

  /// تحسين شريط أوقات الصلاة
  static Widget optimizedPrayerTimesBar({
    required Widget child,
    required BuildContext context,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: _createOptimizedShadow(context, opacity: 0.05),
      ),
      child: child,
    );
  }

  /// تحسين قائمة التصنيفات
  static Widget optimizedCategoriesList({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
  }) {
    return ListView.builder(
      controller: controller,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      physics: const ClampingScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      cacheExtent: 200, // تقليل التخزين المؤقت
      addAutomaticKeepAlives: false, // تحسين الذاكرة
      addRepaintBoundaries: true, // تحسين الرسم
    );
  }

  /// تحسين شبكة التصنيفات
  static Widget optimizedCategoriesGrid({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required int crossAxisCount,
    ScrollController? controller,
  }) {
    return GridView.builder(
      controller: controller,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: 1.2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      physics: const ClampingScrollPhysics(),
      padding: const EdgeInsets.all(8),
      cacheExtent: 200,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
    );
  }

  /// إنشاء تدرج محسن
  static LinearGradient _createOptimizedGradient(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;

    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        primaryColor.withAlpha(38), // 0.15 * 255
        Colors.transparent,
      ],
    );
  }

  /// إنشاء ظل محسن
  static List<BoxShadow> _createOptimizedShadow(
    BuildContext context, {
    double opacity = 0.15,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return [
      BoxShadow(
        color: isDark
            ? Colors.black
                .withAlpha((opacity * 255 * 1.5).round().clamp(0, 255))
            : Colors.black.withAlpha((opacity * 255).round().clamp(0, 255)),
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ];
  }

  /// تحسين الأيقونات
  static Widget optimizedIcon({
    required IconData icon,
    double size = 24,
    Color? color,
  }) {
    return Icon(
      icon,
      size: size,
      color: color,
    );
  }

  /// تحسين النصوص
  static Widget optimizedText({
    required String text,
    TextStyle? style,
    int? maxLines,
    TextOverflow? overflow,
    TextAlign? textAlign,
  }) {
    return Text(
      text,
      style: style,
      maxLines: maxLines,
      overflow: overflow ?? TextOverflow.ellipsis,
      textAlign: textAlign,
    );
  }

  /// تحسين الأزرار
  static Widget optimizedButton({
    required VoidCallback? onPressed,
    required Widget child,
    Color? backgroundColor,
    Color? foregroundColor,
    EdgeInsets? padding,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        padding:
            padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 2,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: child,
    );
  }

  /// تحسين الفواصل
  static Widget optimizedDivider({
    double height = 1,
    Color? color,
    double indent = 0,
    double endIndent = 0,
  }) {
    return Divider(
      height: height,
      color: color,
      indent: indent,
      endIndent: endIndent,
    );
  }

  /// تحسين المساحات الفارغة
  static Widget optimizedSpacer({
    double height = 8,
    double width = 8,
  }) {
    return SizedBox(
      height: height,
      width: width,
    );
  }

  /// تحسين التمرير
  static Widget optimizedScrollView({
    required Widget child,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsets? padding,
  }) {
    return SingleChildScrollView(
      controller: controller,
      physics: physics ?? const ClampingScrollPhysics(),
      padding: padding,
      child: child,
    );
  }

  /// تحسين الحاويات
  static Widget optimizedContainer({
    required Widget child,
    EdgeInsets? margin,
    EdgeInsets? padding,
    Color? color,
    Decoration? decoration,
    double? width,
    double? height,
  }) {
    return Container(
      margin: margin,
      padding: padding,
      color: color,
      decoration: decoration,
      width: width,
      height: height,
      child: child,
    );
  }

  /// تحسين الصفوف
  static Widget optimizedRow({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisSize mainAxisSize = MainAxisSize.max,
  }) {
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: children,
    );
  }

  /// تحسين الأعمدة
  static Widget optimizedColumn({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisSize mainAxisSize = MainAxisSize.max,
  }) {
    return Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: children,
    );
  }
}
