import 'package:shared_preferences/shared_preferences.dart';
import 'package:adhan/adhan.dart';
import 'dart:convert';

/// خدمة أوقات الصلاة المحلية (بدون إنترنت)
class OfflinePrayerService {
  static const String _savedLocationKey = 'saved_location';
  static const String _savedCityKey = 'saved_city';
  static const String _calculationMethodKey = 'calculation_method';
  static const String _madhabKey = 'madhab';

  /// المدن المحفوظة مسبقاً مع إحداثياتها
  static const Map<String, Map<String, double>> _predefinedCities = {
    'الرياض': {'lat': 24.7136, 'lng': 46.6753},
    'جدة': {'lat': 21.4858, 'lng': 39.1925},
    'مكة المكرمة': {'lat': 21.3891, 'lng': 39.8579},
    'المدينة المنورة': {'lat': 24.5247, 'lng': 39.5692},
    'الدمام': {'lat': 26.4207, 'lng': 50.0888},
    'الطائف': {'lat': 21.2703, 'lng': 40.4158},
    'تبوك': {'lat': 28.3998, 'lng': 36.5700},
    'بريدة': {'lat': 26.3260, 'lng': 43.9750},
    'خميس مشيط': {'lat': 18.3000, 'lng': 42.7300},
    'حائل': {'lat': 27.5114, 'lng': 41.6900},
    'الجبيل': {'lat': 27.0174, 'lng': 49.6251},
    'ينبع': {'lat': 24.0896, 'lng': 38.0618},
    'الخبر': {'lat': 26.2172, 'lng': 50.1971},
    'الأحساء': {'lat': 25.4295, 'lng': 49.6202},
    'القطيف': {'lat': 26.5205, 'lng': 50.0089},
    'عرعر': {'lat': 30.9753, 'lng': 41.0381},
    'سكاكا': {'lat': 29.9697, 'lng': 40.2064},
    'نجران': {'lat': 17.4924, 'lng': 44.1277},
    'جازان': {'lat': 16.8892, 'lng': 42.5511},
    'أبها': {'lat': 18.2164, 'lng': 42.5047},
    'القصيم': {'lat': 26.3260, 'lng': 43.9750},
    'الباحة': {'lat': 20.0129, 'lng': 41.4687},
    'القريات': {'lat': 31.3321, 'lng': 37.3439},
    'رفحاء': {'lat': 29.6252, 'lng': 43.4906},
    'الدوادمي': {'lat': 24.5077, 'lng': 44.3973},

    // مدن عربية أخرى
    'القاهرة': {'lat': 30.0444, 'lng': 31.2357},
    'الإسكندرية': {'lat': 31.2001, 'lng': 29.9187},
    'دبي': {'lat': 25.2048, 'lng': 55.2708},
    'أبو ظبي': {'lat': 24.2992, 'lng': 54.6972},
    'الكويت': {'lat': 29.3117, 'lng': 47.4818},
    'الدوحة': {'lat': 25.2854, 'lng': 51.5310},
    'المنامة': {'lat': 26.0667, 'lng': 50.5577},
    'مسقط': {'lat': 23.5859, 'lng': 58.4059},
    'عمان': {'lat': 31.9454, 'lng': 35.9284},
    'بيروت': {'lat': 33.8938, 'lng': 35.5018},
    'دمشق': {'lat': 33.5138, 'lng': 36.2765},
    'بغداد': {'lat': 33.3152, 'lng': 44.3661},
    'الرباط': {'lat': 34.0209, 'lng': -6.8416},
    'الدار البيضاء': {'lat': 33.5731, 'lng': -7.5898},
    'تونس': {'lat': 36.8065, 'lng': 10.1815},
    'الجزائر': {'lat': 36.7538, 'lng': 3.0588},
    'طرابلس': {'lat': 32.8872, 'lng': 13.1913},
    'الخرطوم': {'lat': 15.5007, 'lng': 32.5599},
    'صنعاء': {'lat': 15.3694, 'lng': 44.1910},
    'إسطنبول': {'lat': 41.0082, 'lng': 28.9784},
    'أنقرة': {'lat': 39.9334, 'lng': 32.8597},
    'كوالالمبور': {'lat': 3.1390, 'lng': 101.6869},
    'جاكرتا': {'lat': -6.2088, 'lng': 106.8456},
    'إسلام آباد': {'lat': 33.6844, 'lng': 73.0479},
    'كراتشي': {'lat': 24.8607, 'lng': 67.0011},
    'لاهور': {'lat': 31.5204, 'lng': 74.3587},
    'داكا': {'lat': 23.8103, 'lng': 90.4125},
  };

  /// حفظ الموقع المحدد
  static Future<void> saveLocation(
    double latitude,
    double longitude,
    String cityName,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _savedLocationKey,
        json.encode({
          'latitude': latitude,
          'longitude': longitude,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );
      await prefs.setString(_savedCityKey, cityName);
      // تم حفظ الموقع بنجاح
    } catch (e) {
      // خطأ في حفظ الموقع
    }
  }

  /// الحصول على الموقع المحفوظ
  static Future<Map<String, dynamic>?> getSavedLocation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final locationString = prefs.getString(_savedLocationKey);
      final cityName = prefs.getString(_savedCityKey);

      if (locationString != null && cityName != null) {
        final locationData = json.decode(locationString);
        return {
          'latitude': locationData['latitude'],
          'longitude': locationData['longitude'],
          'cityName': cityName,
          'timestamp': locationData['timestamp'],
        };
      }
    } catch (e) {
      // خطأ في استرجاع الموقع المحفوظ
    }
    return null;
  }

  /// الحصول على قائمة المدن المحفوظة مسبقاً
  static List<String> getPredefinedCities() {
    return _predefinedCities.keys.toList()..sort();
  }

  /// الحصول على إحداثيات مدينة محددة
  static Map<String, double>? getCityCoordinates(String cityName) {
    return _predefinedCities[cityName];
  }

  /// حساب أوقات الصلاة لموقع محدد
  static PrayerTimes? calculatePrayerTimes({
    required double latitude,
    required longitude,
    DateTime? date,
    CalculationMethod? method,
    Madhab? madhab,
  }) {
    try {
      final coordinates = Coordinates(latitude, longitude);
      final targetDate = date ?? DateTime.now();
      final dateComponents = DateComponents(
        targetDate.year,
        targetDate.month,
        targetDate.day,
      );

      final params = (method ?? CalculationMethod.egyptian).getParameters();
      params.madhab = madhab ?? Madhab.shafi;

      return PrayerTimes(coordinates, dateComponents, params);
    } catch (e) {
      // خطأ في حساب أوقات الصلاة
      return null;
    }
  }

  /// حفظ طريقة الحساب المفضلة
  static Future<void> saveCalculationMethod(CalculationMethod method) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_calculationMethodKey, method.toString());
      // تم حفظ طريقة الحساب
    } catch (e) {
      // خطأ في حفظ طريقة الحساب
    }
  }

  /// الحصول على طريقة الحساب المحفوظة
  static Future<CalculationMethod> getSavedCalculationMethod() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final methodString = prefs.getString(_calculationMethodKey);

      if (methodString != null) {
        // تحويل النص إلى طريقة الحساب
        switch (methodString) {
          case 'CalculationMethod.egyptian':
            return CalculationMethod.egyptian;
          case 'CalculationMethod.karachi':
            return CalculationMethod.karachi;
          case 'CalculationMethod.dubai':
            return CalculationMethod.dubai;
          case 'CalculationMethod.kuwait':
            return CalculationMethod.kuwait;
          case 'CalculationMethod.qatar':
            return CalculationMethod.qatar;
          case 'CalculationMethod.singapore':
            return CalculationMethod.singapore;
          default:
            return CalculationMethod.egyptian;
        }
      }
    } catch (e) {
      // خطأ في استرجاع طريقة الحساب
    }
    return CalculationMethod.egyptian; // الافتراضي
  }

  /// حفظ المذهب المفضل
  static Future<void> saveMadhab(Madhab madhab) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_madhabKey, madhab.toString());
      // تم حفظ المذهب
    } catch (e) {
      // خطأ في حفظ المذهب
    }
  }

  /// الحصول على المذهب المحفوظ
  static Future<Madhab> getSavedMadhab() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final madhabString = prefs.getString(_madhabKey);

      if (madhabString != null) {
        switch (madhabString) {
          case 'Madhab.shafi':
            return Madhab.shafi;
          case 'Madhab.hanafi':
            return Madhab.hanafi;
          default:
            return Madhab.shafi;
        }
      }
    } catch (e) {
      // خطأ في استرجاع المذهب
    }
    return Madhab.shafi; // الافتراضي
  }

  /// الحصول على أسماء طرق الحساب
  static Map<CalculationMethod, String> getCalculationMethodNames() {
    return {
      CalculationMethod.egyptian: 'الهيئة المصرية العامة للمساحة',
      CalculationMethod.karachi: 'جامعة العلوم الإسلامية - كراتشي',
      CalculationMethod.dubai: 'دبي',
      CalculationMethod.kuwait: 'الكويت',
      CalculationMethod.qatar: 'قطر',
      CalculationMethod.singapore: 'سنغافورة',
    };
  }

  /// الحصول على أسماء المذاهب
  static Map<Madhab, String> getMadhabNames() {
    return {Madhab.shafi: 'الشافعي', Madhab.hanafi: 'الحنفي'};
  }
}
