import 'package:logger/logger.dart';

/// مساعد للتسجيل في التطبيق
class AppLogger {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
    ),
  );

  /// تسجيل معلومات عادية
  static void info(String message) {
    _logger.i(message);
  }

  /// تسجيل معلومات تصحيح
  static void debug(String message) {
    _logger.d(message);
  }

  /// تسجيل تحذيرات
  static void warning(String message) {
    _logger.w(message);
  }

  /// تسجيل أخطاء
  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }
}
