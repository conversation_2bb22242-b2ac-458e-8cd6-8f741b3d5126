import 'package:flutter/material.dart';

/// ويدجت لرسم أيقونة التطبيق بالضبط كما في الصورة المرفقة
class AppIcon extends StatelessWidget {
  final double size;
  final Color backgroundColor;
  final Color foregroundColor;
  
  const AppIcon({
    super.key,
    this.size = 150,
    this.backgroundColor = const Color(0xFF2E7D32), // اللون الأخضر الرئيسي
    this.foregroundColor = Colors.white,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(size / 5),
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withAlpha(76), // 0.3 * 255 = 76
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(size / 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // حمامة السلام
            CustomPaint(
              size: <PERSON><PERSON>(size / 2, size / 3),
              painter: <PERSON><PERSON><PERSON><PERSON>(color: foregroundColor),
            ),
            
            // رمز المسجد
            SizedBox(height: size / 30),
            CustomPaint(
              size: Size(size / 2, size / 5),
              painter: MosquePainter(color: foregroundColor),
            ),
            
            // كلمة أذكار
            SizedBox(height: size / 30),
            Text(
              'أذكار',
              style: TextStyle(
                color: foregroundColor,
                fontSize: size / 6.25,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// رسام حمامة السلام
class DovePainter extends CustomPainter {
  final Color color;
  
  DovePainter({required this.color});
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    final path = Path();
    
    // جسم الحمامة
    path.moveTo(size.width * 0.5, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.7, size.height * 0.1,
      size.width * 0.9, size.height * 0.3,
    );
    path.quadraticBezierTo(
      size.width * 0.95, size.height * 0.5,
      size.width * 0.8, size.height * 0.7,
    );
    path.lineTo(size.width * 0.5, size.height * 0.8);
    path.lineTo(size.width * 0.2, size.height * 0.7);
    path.quadraticBezierTo(
      size.width * 0.05, size.height * 0.5,
      size.width * 0.1, size.height * 0.3,
    );
    path.quadraticBezierTo(
      size.width * 0.3, size.height * 0.1,
      size.width * 0.5, size.height * 0.2,
    );
    
    // الرأس
    final headPath = Path();
    headPath.addOval(Rect.fromCenter(
      center: Offset(size.width * 0.7, size.height * 0.3),
      width: size.width * 0.25,
      height: size.height * 0.25,
    ));
    
    // الجناح الأيمن
    final rightWingPath = Path();
    rightWingPath.moveTo(size.width * 0.6, size.height * 0.4);
    rightWingPath.quadraticBezierTo(
      size.width * 0.8, size.height * 0.3,
      size.width * 0.9, size.height * 0.5,
    );
    rightWingPath.quadraticBezierTo(
      size.width * 0.7, size.height * 0.6,
      size.width * 0.6, size.height * 0.4,
    );
    
    // الجناح الأيسر
    final leftWingPath = Path();
    leftWingPath.moveTo(size.width * 0.4, size.height * 0.4);
    leftWingPath.quadraticBezierTo(
      size.width * 0.2, size.height * 0.3,
      size.width * 0.1, size.height * 0.5,
    );
    leftWingPath.quadraticBezierTo(
      size.width * 0.3, size.height * 0.6,
      size.width * 0.4, size.height * 0.4,
    );
    
    // رسم كل الأجزاء
    canvas.drawPath(path, paint);
    canvas.drawPath(headPath, paint);
    canvas.drawPath(rightWingPath, paint);
    canvas.drawPath(leftWingPath, paint);
    
    // العين
    final eyePaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      Offset(size.width * 0.75, size.height * 0.28),
      size.width * 0.03,
      eyePaint,
    );
    
    // المنقار
    final beakPath = Path();
    beakPath.moveTo(size.width * 0.8, size.height * 0.32);
    beakPath.lineTo(size.width * 0.9, size.height * 0.35);
    beakPath.lineTo(size.width * 0.8, size.height * 0.38);
    beakPath.close();
    
    final beakPaint = Paint()
      ..color = Colors.orange
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(beakPath, beakPaint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// رسام المسجد
class MosquePainter extends CustomPainter {
  final Color color;
  
  MosquePainter({required this.color});
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    final path = Path();
    
    // قاعدة المسجد
    path.moveTo(size.width * 0.1, size.height * 0.8);
    path.lineTo(size.width * 0.9, size.height * 0.8);
    path.lineTo(size.width * 0.9, size.height);
    path.lineTo(size.width * 0.1, size.height);
    path.close();
    
    // القبة الرئيسية
    final mainDomePath = Path();
    mainDomePath.addArc(
      Rect.fromCenter(
        center: Offset(size.width * 0.5, size.height * 0.4),
        width: size.width * 0.4,
        height: size.height * 0.8,
      ),
      3.14, // بداية القوس (π)
      3.14, // نهاية القوس (π)
    );
    mainDomePath.lineTo(size.width * 0.7, size.height * 0.8);
    mainDomePath.lineTo(size.width * 0.3, size.height * 0.8);
    mainDomePath.close();
    
    // المآذن
    final leftMinaretPath = Path();
    leftMinaretPath.moveTo(size.width * 0.2, size.height * 0.8);
    leftMinaretPath.lineTo(size.width * 0.2, size.height * 0.3);
    leftMinaretPath.lineTo(size.width * 0.25, size.height * 0.2);
    leftMinaretPath.lineTo(size.width * 0.3, size.height * 0.3);
    leftMinaretPath.lineTo(size.width * 0.3, size.height * 0.8);
    leftMinaretPath.close();
    
    final rightMinaretPath = Path();
    rightMinaretPath.moveTo(size.width * 0.7, size.height * 0.8);
    rightMinaretPath.lineTo(size.width * 0.7, size.height * 0.3);
    rightMinaretPath.lineTo(size.width * 0.75, size.height * 0.2);
    rightMinaretPath.lineTo(size.width * 0.8, size.height * 0.3);
    rightMinaretPath.lineTo(size.width * 0.8, size.height * 0.8);
    rightMinaretPath.close();
    
    // رسم كل الأجزاء
    canvas.drawPath(path, paint);
    canvas.drawPath(mainDomePath, paint);
    canvas.drawPath(leftMinaretPath, paint);
    canvas.drawPath(rightMinaretPath, paint);
    
    // الهلال فوق القبة
    final crescentPath = Path();
    crescentPath.addArc(
      Rect.fromCenter(
        center: Offset(size.width * 0.5, size.height * 0.15),
        width: size.width * 0.2,
        height: size.height * 0.2,
      ),
      0, // بداية القوس
      3.14 * 2, // نهاية القوس (2π)
    );
    
    canvas.drawPath(crescentPath, paint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
