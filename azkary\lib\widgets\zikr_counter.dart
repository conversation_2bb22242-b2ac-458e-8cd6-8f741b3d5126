import 'package:flutter/material.dart';

class <PERSON>ikrCounter extends StatelessWidget {
  final int currentCount;
  final int totalCount;
  final VoidCallback onIncrement;
  final VoidCallback onReset;

  const ZikrCounter({
    super.key,
    required this.currentCount,
    required this.totalCount,
    required this.onIncrement,
    required this.onReset,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progress = totalCount > 0 ? currentCount / totalCount : 0.0;
    final isCompleted = currentCount >= totalCount;

    return Column(
      children: [
        // Progress indicator
        Stack(
          alignment: Alignment.center,
          children: [
            // Circular progress indicator
            SizedBox(
              width: 200,
              height: 200,
              child: CircularProgressIndicator(
                value: progress,
                strokeWidth: 12,
                backgroundColor: theme.colorScheme.primary.withAlpha(25), // 0.1 opacity ≈ 25 alpha
                color: theme.colorScheme.primary,
              ),
            ),

            // Counter display
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '$currentCount',
                  style: TextStyle(
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                    color: isCompleted
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface,
                  ),
                ),
                Text(
                  'من $totalCount',
                  style: TextStyle(
                    fontSize: 16,
                    color: theme.colorScheme.onSurface.withAlpha(179), // 0.7 opacity ≈ 179 alpha
                  ),
                ),
              ],
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Counter buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Reset button
            ElevatedButton.icon(
              onPressed: onReset,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.error,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),

            const SizedBox(width: 16),

            // Increment button
            ElevatedButton.icon(
              onPressed: isCompleted ? null : onIncrement,
              icon: const Icon(Icons.add),
              label: const Text('تسبيح'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                disabledBackgroundColor: theme.colorScheme.primary.withAlpha(128), // 0.5 opacity = 128 alpha
              ),
            ),
          ],
        ),
      ],
    );
  }
}
