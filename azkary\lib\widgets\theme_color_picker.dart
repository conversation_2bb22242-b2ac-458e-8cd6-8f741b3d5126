import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/theme_model.dart';
import '../services/theme_provider.dart';

/// مكون اختيار لون السمة
class ThemeColorPicker extends StatelessWidget {
  const ThemeColorPicker({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Text(
                'ألوان التطبيق',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Wrap(
                spacing: 12.0,
                runSpacing: 12.0,
                children:
                    themeProvider.availableThemes.map((theme) {
                      final isSelected =
                          theme.colorValue ==
                          themeProvider.currentThemeColor.colorValue;
                      return _buildColorOption(
                        context,
                        theme,
                        isSelected,
                        themeProvider,
                      );
                    }).toList(),
              ),
            ),
          ],
        );
      },
    );
  }

  /// بناء خيار اللون
  Widget _buildColorOption(
    BuildContext context,
    ThemeModel theme,
    bool isSelected,
    ThemeProvider themeProvider,
  ) {
    return GestureDetector(
      onTap: () {
        themeProvider.setThemeColor(theme);
      },
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: theme.primaryColor,
          shape: BoxShape.circle,
          border: Border.all(
            color:
                isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Colors.transparent,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(
                red: 0,
                green: 0,
                blue: 0,
                alpha: 0.1,
              ),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child:
            isSelected
                ? const Icon(Icons.check, color: Colors.white, size: 24)
                : null,
      ),
    );
  }
}
