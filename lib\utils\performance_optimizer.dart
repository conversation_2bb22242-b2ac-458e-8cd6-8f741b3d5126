import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// فئة تحسين الأداء للتطبيق
class PerformanceOptimizer {
  static const Duration _fastTransition = Duration(milliseconds: 200);
  static const Duration _mediumTransition = Duration(milliseconds: 250);
  static const Duration _slowTransition = Duration(milliseconds: 300);

  /// تحسين إعدادات الرسوم المتحركة للأداء
  static void optimizeAnimations() {
    // تقليل مدة الرسوم المتحركة للنظام
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        // تحسين أداء الرسوم المتحركة
        SystemChrome.setEnabledSystemUIMode(
          SystemUiMode.edgeToEdge,
          overlays: [SystemUiOverlay.top],
        );
      } catch (e) {
        // تجاهل الأخطاء في حالة عدم دعم المنصة
      }
    });
  }

  /// تحسين استهلاك الذاكرة
  static void optimizeMemory() {
    // تنظيف الذاكرة بشكل دوري
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // إجبار جمع القمامة
      try {
        // تحسين استخدام الذاكرة
        SystemChannels.platform.invokeMethod('SystemNavigator.pop');
      } catch (e) {
        // تجاهل الأخطاء
      }
    });
  }

  /// إعدادات محسنة للقوائم
  static const ScrollPhysics optimizedScrollPhysics = ClampingScrollPhysics();
  static const double optimizedCacheExtent = 300.0;
  static const EdgeInsets optimizedPadding = EdgeInsets.symmetric(
    horizontal: 12,
    vertical: 6,
  );

  /// منحنيات محسنة للأداء
  static const Curve fastCurve = Curves.easeOut;
  static const Curve mediumCurve = Curves.easeInOut;
  static const Curve slowCurve = Curves.easeInOutCubic;

  /// مدد زمنية محسنة
  static Duration get fastTransition => _fastTransition;
  static Duration get mediumTransition => _mediumTransition;
  static Duration get slowTransition => _slowTransition;

  /// تحسين الظلال للأداء
  static List<BoxShadow> optimizedShadow(
    BuildContext context, {
    double opacity = 0.15,
    double blurRadius = 8.0,
    double spreadRadius = 0.0,
    Offset offset = const Offset(0, 2),
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return [
      BoxShadow(
        color: isDark 
          ? Colors.black.withAlpha((opacity * 255 * 1.5).round().clamp(0, 255))
          : Colors.black.withAlpha((opacity * 255).round().clamp(0, 255)),
        blurRadius: blurRadius,
        spreadRadius: spreadRadius,
        offset: offset,
      ),
    ];
  }

  /// تحسين التدرجات اللونية
  static LinearGradient optimizedGradient(
    Color primaryColor, {
    bool isDark = false,
    double opacity = 0.1,
  }) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        primaryColor.withAlpha((opacity * 255).round().clamp(0, 255)),
        Colors.transparent,
      ],
    );
  }

  /// تحسين الحدود
  static BorderRadius get optimizedBorderRadius => BorderRadius.circular(12);
  static BorderRadius get largeBorderRadius => BorderRadius.circular(20);
  static BorderRadius get smallBorderRadius => BorderRadius.circular(8);

  /// تحسين الألوان مع الشفافية
  static Color optimizedColorWithOpacity(Color color, double opacity) {
    return color.withAlpha((opacity * 255).round().clamp(0, 255));
  }

  /// إعدادات محسنة للصور
  static const double optimizedImageQuality = 0.8;
  static const int optimizedImageWidth = 1080; // دقة 1080p
  static const int optimizedImageHeight = 1920;

  /// تحسين أداء النصوص
  static const TextStyle optimizedTextStyle = TextStyle(
    fontWeight: FontWeight.normal,
    height: 1.4,
  );

  /// تحسين أداء الأيقونات
  static const double optimizedIconSize = 24.0;
  static const double largeIconSize = 32.0;
  static const double smallIconSize = 16.0;

  /// تحسين أداء الحشو
  static const EdgeInsets smallPadding = EdgeInsets.all(8);
  static const EdgeInsets mediumPadding = EdgeInsets.all(16);
  static const EdgeInsets largePadding = EdgeInsets.all(24);

  /// تحسين أداء الهوامش
  static const EdgeInsets smallMargin = EdgeInsets.all(4);
  static const EdgeInsets mediumMargin = EdgeInsets.all(8);
  static const EdgeInsets largeMargin = EdgeInsets.all(16);

  /// تحسين أداء الانتقالات
  static PageRouteBuilder<T> optimizedPageTransition<T>(
    Widget page, {
    Duration? duration,
    Curve? curve,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration ?? _fastTransition,
      reverseTransitionDuration: Duration(
        milliseconds: (duration?.inMilliseconds ?? _fastTransition.inMilliseconds) - 50,
      ),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation.drive(
            Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: curve ?? fastCurve),
            ),
          ),
          child: child,
        );
      },
    );
  }

  /// تحسين أداء الرسوم المتحركة
  static Widget optimizedAnimatedWidget({
    required Widget child,
    required int index,
    Duration? duration,
    Curve? curve,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration ?? Duration(milliseconds: 150 + (index * 25)),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: curve ?? fastCurve,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 5 * (1 - value)), // تقليل المسافة
          child: Opacity(opacity: value, child: child),
        );
      },
      child: child,
    );
  }

  /// تحسين أداء البطاقات
  static Widget optimizedCard({
    required Widget child,
    EdgeInsets? padding,
    EdgeInsets? margin,
    BorderRadius? borderRadius,
    List<BoxShadow>? boxShadow,
    Color? backgroundColor,
  }) {
    return Container(
      margin: margin ?? mediumMargin,
      padding: padding ?? mediumPadding,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius ?? optimizedBorderRadius,
        boxShadow: boxShadow,
      ),
      child: child,
    );
  }

  /// تحسين أداء القوائم
  static Widget optimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    double? cacheExtent,
  }) {
    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      physics: physics ?? optimizedScrollPhysics,
      padding: padding ?? optimizedPadding,
      cacheExtent: cacheExtent ?? optimizedCacheExtent,
    );
  }

  /// تحسين أداء الشبكات
  static Widget optimizedGridView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required int crossAxisCount,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    double? cacheExtent,
    double childAspectRatio = 1.0,
  }) {
    return GridView.builder(
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      physics: physics ?? optimizedScrollPhysics,
      padding: padding ?? optimizedPadding,
      cacheExtent: cacheExtent ?? optimizedCacheExtent,
    );
  }

  /// تحسين أداء الصور
  static Widget optimizedImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
  }) {
    return Image.asset(
      imagePath,
      width: width,
      height: height,
      fit: fit,
      cacheWidth: width?.round(),
      cacheHeight: height?.round(),
      filterQuality: FilterQuality.medium, // توازن بين الجودة والأداء
    );
  }

  /// تحسين أداء الأيقونات
  static Widget optimizedIcon({
    required IconData icon,
    double? size,
    Color? color,
  }) {
    return Icon(
      icon,
      size: size ?? optimizedIconSize,
      color: color,
    );
  }
}
