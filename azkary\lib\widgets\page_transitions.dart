import 'package:flutter/material.dart';

/// أنواع الانتقالات بين الصفحات
enum PageTransitionType {
  fade,
  rightToLeft,
  leftToRight,
  upToDown,
  downToUp,
  scale,
  rotate,
  size,
  rightToLeftWithFade,
  leftToRightWithFade,
}

/// مكون الانتقال بين الصفحات
class PageTransition<T> extends PageRouteBuilder<T> {
  final Widget child;
  final PageTransitionType type;
  final Curve curve;
  final Alignment alignment;
  final Duration duration;

  PageTransition({
    required this.child,
    this.type = PageTransitionType.rightToLeft,
    this.curve = Curves.easeOut, // منحنى أسرع للأداء
    this.alignment = Alignment.center,
    this.duration = const Duration(milliseconds: 200), // تحسين الأداء
    super.settings,
  }) : super(
         pageBuilder: (context, animation, secondaryAnimation) => child,
         transitionDuration: duration,
         transitionsBuilder: (context, animation, secondaryAnimation, child) {
           switch (type) {
             case PageTransitionType.fade:
               return FadeTransition(opacity: animation, child: child);
             case PageTransitionType.rightToLeft:
               return SlideTransition(
                 position: Tween<Offset>(
                   begin: const Offset(1, 0),
                   end: Offset.zero,
                 ).animate(CurvedAnimation(parent: animation, curve: curve)),
                 child: child,
               );
             case PageTransitionType.leftToRight:
               return SlideTransition(
                 position: Tween<Offset>(
                   begin: const Offset(-1, 0),
                   end: Offset.zero,
                 ).animate(CurvedAnimation(parent: animation, curve: curve)),
                 child: child,
               );
             case PageTransitionType.upToDown:
               return SlideTransition(
                 position: Tween<Offset>(
                   begin: const Offset(0, -1),
                   end: Offset.zero,
                 ).animate(CurvedAnimation(parent: animation, curve: curve)),
                 child: child,
               );
             case PageTransitionType.downToUp:
               return SlideTransition(
                 position: Tween<Offset>(
                   begin: const Offset(0, 1),
                   end: Offset.zero,
                 ).animate(CurvedAnimation(parent: animation, curve: curve)),
                 child: child,
               );
             case PageTransitionType.scale:
               return ScaleTransition(
                 alignment: alignment,
                 scale: CurvedAnimation(parent: animation, curve: curve),
                 child: child,
               );
             case PageTransitionType.rotate:
               return RotationTransition(
                 turns: animation,
                 child: ScaleTransition(
                   scale: animation,
                   child: FadeTransition(opacity: animation, child: child),
                 ),
               );
             case PageTransitionType.size:
               return Align(
                 alignment: alignment,
                 child: SizeTransition(
                   sizeFactor: CurvedAnimation(parent: animation, curve: curve),
                   child: child,
                 ),
               );
             case PageTransitionType.rightToLeftWithFade:
               return SlideTransition(
                 position: Tween<Offset>(
                   begin: const Offset(1, 0),
                   end: Offset.zero,
                 ).animate(CurvedAnimation(parent: animation, curve: curve)),
                 child: FadeTransition(opacity: animation, child: child),
               );
             case PageTransitionType.leftToRightWithFade:
               return SlideTransition(
                 position: Tween<Offset>(
                   begin: const Offset(-1, 0),
                   end: Offset.zero,
                 ).animate(CurvedAnimation(parent: animation, curve: curve)),
                 child: FadeTransition(opacity: animation, child: child),
               );
           }
         },
       );
}

/// دالة مساعدة للانتقال إلى صفحة جديدة
Future<T?> navigateTo<T>(
  BuildContext context,
  Widget page, {
  PageTransitionType type = PageTransitionType.rightToLeft,
  Curve curve = Curves.easeInOut,
  Alignment alignment = Alignment.center,
  Duration duration = const Duration(milliseconds: 300),
  bool fullscreenDialog = false,
  bool maintainState = true,
}) {
  return Navigator.of(context).push<T>(
    PageTransition<T>(
      child: page,
      type: type,
      curve: curve,
      alignment: alignment,
      duration: duration,
      settings: RouteSettings(name: page.runtimeType.toString()),
    ),
  );
}

/// دالة مساعدة للانتقال إلى صفحة جديدة واستبدال الصفحة الحالية
Future<T?> navigateToReplacement<T>(
  BuildContext context,
  Widget page, {
  PageTransitionType type = PageTransitionType.rightToLeft,
  Curve curve = Curves.easeInOut,
  Alignment alignment = Alignment.center,
  Duration duration = const Duration(milliseconds: 300),
  bool fullscreenDialog = false,
  bool maintainState = true,
}) {
  return Navigator.of(context).pushReplacement(
    PageTransition<T>(
      child: page,
      type: type,
      curve: curve,
      alignment: alignment,
      duration: duration,
      settings: RouteSettings(name: page.runtimeType.toString()),
    ),
  );
}

/// دالة مساعدة للانتقال إلى صفحة جديدة وإزالة كل الصفحات السابقة
Future<T?> navigateToAndRemoveUntil<T>(
  BuildContext context,
  Widget page, {
  PageTransitionType type = PageTransitionType.rightToLeft,
  Curve curve = Curves.easeInOut,
  Alignment alignment = Alignment.center,
  Duration duration = const Duration(milliseconds: 300),
  bool fullscreenDialog = false,
  bool maintainState = true,
}) {
  return Navigator.of(context).pushAndRemoveUntil(
    PageTransition<T>(
      child: page,
      type: type,
      curve: curve,
      alignment: alignment,
      duration: duration,
      settings: RouteSettings(name: page.runtimeType.toString()),
    ),
    (route) => false,
  );
}
