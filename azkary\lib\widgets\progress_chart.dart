import 'package:flutter/material.dart';
import '../models/statistics_model.dart';

/// ويدجت مخطط التقدم
class ProgressChart extends StatelessWidget {
  final WeeklyStatistics? weeklyStats;
  final MonthlyStatistics? monthlyStats;

  const ProgressChart({super.key, this.weeklyStats, this.monthlyStats});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مخطط التقدم الأسبوعي',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            if (weeklyStats != null && weeklyStats!.dailyStats.isNotEmpty)
              _buildWeeklyChart(theme)
            else
              _buildEmptyState(theme),

            const SizedBox(height: 24),

            // إحصائيات سريعة
            _buildQuickStats(theme),
          ],
        ),
      ),
    );
  }

  /// بناء المخطط الأسبوعي
  Widget _buildWeeklyChart(ThemeData theme) {
    final dailyStats = weeklyStats!.dailyStats;

    return Column(
      children: [
        // المخطط البياني
        SizedBox(
          height: 120,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children:
                dailyStats.asMap().entries.map((entry) {
                  final index = entry.key;
                  final stat = entry.value;
                  final height = stat.completionPercentage * 100;

                  return Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          // النسبة المئوية
                          Text(
                            '${(stat.completionPercentage * 100).toInt()}%',
                            style: TextStyle(
                              fontSize: 10,
                              color: theme.colorScheme.onSurface.withAlpha(179),
                            ),
                          ),
                          const SizedBox(height: 4),

                          // العمود
                          Container(
                            height: height,
                            decoration: BoxDecoration(
                              color: _getBarColor(
                                stat.completionPercentage,
                                theme,
                              ),
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(4),
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),

                          // اسم اليوم
                          Text(
                            _getDayName(index),
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.outline.withAlpha(51)),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bar_chart,
              size: 32,
              color: theme.colorScheme.onSurface.withAlpha(128),
            ),
            const SizedBox(height: 8),
            Text(
              'لا توجد بيانات للعرض',
              style: TextStyle(
                color: theme.colorScheme.onSurface.withAlpha(128),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats(ThemeData theme) {
    if (weeklyStats == null && monthlyStats == null) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات سريعة',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            if (weeklyStats != null) ...[
              Expanded(
                child: _buildStatCard(
                  'هذا الأسبوع',
                  '${weeklyStats!.completedDays}/7 أيام',
                  '${(weeklyStats!.averageCompletion * 100).toInt()}% متوسط',
                  Icons.calendar_view_week,
                  theme.colorScheme.primary,
                  theme,
                ),
              ),
              const SizedBox(width: 12),
            ],

            if (monthlyStats != null)
              Expanded(
                child: _buildStatCard(
                  'هذا الشهر',
                  '${monthlyStats!.completedDays} يوم مكتمل',
                  '${monthlyStats!.totalCompletedAzkar} ذكر',
                  Icons.calendar_view_month,
                  theme.colorScheme.secondary,
                  theme,
                ),
              ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
    String title,
    String value1,
    String value2,
    IconData icon,
    Color color,
    ThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha(13),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withAlpha(51), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value1,
            style: TextStyle(fontSize: 12, color: theme.colorScheme.onSurface),
          ),
          Text(
            value2,
            style: TextStyle(
              fontSize: 12,
              color: theme.colorScheme.onSurface.withAlpha(179),
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على لون العمود حسب نسبة الإكمال
  Color _getBarColor(double completion, ThemeData theme) {
    if (completion >= 1.0) {
      return Colors.green;
    } else if (completion >= 0.75) {
      return theme.colorScheme.primary;
    } else if (completion >= 0.5) {
      return Colors.orange;
    } else if (completion > 0) {
      return Colors.red.withAlpha(179);
    } else {
      return Colors.grey.withAlpha(128);
    }
  }

  /// الحصول على اسم اليوم
  String _getDayName(int index) {
    const days = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];
    return days[index % 7];
  }
}
