import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/azkar_model.dart';
import '../services/azkar_provider.dart';
import '../widgets/zikr_list_item.dart';
import '../widgets/azkar_settings_bottom_sheet.dart';

import '../utils/logger.dart';

class CustomAzkarScreen extends StatefulWidget {
  const CustomAzkarScreen({super.key});

  @override
  State<CustomAzkarScreen> createState() => _CustomAzkarScreenState();
}

class _CustomAzkarScreenState extends State<CustomAzkarScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final TextEditingController _textController = TextEditingController();
  final TextEditingController _countController = TextEditingController();
  final TextEditingController _fadlController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _textController.dispose();
    _countController.dispose();
    _fadlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'أذكاري الخاصة',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            tooltip: 'إعدادات الأذكار',
            onPressed: () {
              showAzkarSettingsBottomSheet(
                context: context,
                onSettingsChanged: () {
                  setState(() {});
                },
              );
            },
          ),
        ],
      ),
      body: Consumer<AzkarProvider>(
        builder: (context, provider, child) {
          final customAzkar = provider.customAzkar;

          if (customAzkar.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.bookmark_border,
                    size: 64,
                    color: theme.colorScheme.primary.withAlpha(128),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد أذكار خاصة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface.withAlpha(179),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'أضف أذكارك الخاصة هنا',
                    style: TextStyle(
                      fontSize: 14,
                      color: theme.colorScheme.onSurface.withAlpha(128),
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () => _showAddZikrDialog(context),
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة ذكر جديد'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }

          return Stack(
            children: [
              ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: customAzkar.length,
                itemBuilder: (context, index) {
                  final zikr = customAzkar[index];
                  return Dismissible(
                    key: Key('custom_zikr_${zikr.id}'),
                    direction: DismissDirection.endToStart,
                    background: Container(
                      alignment: Alignment.centerRight,
                      padding: const EdgeInsets.only(right: 20),
                      color: Colors.red,
                      child: const Icon(Icons.delete, color: Colors.white),
                    ),
                    confirmDismiss: (direction) async {
                      return await showDialog(
                        context: context,
                        builder:
                            (context) => AlertDialog(
                              title: const Text('حذف الذكر'),
                              content: const Text(
                                'هل أنت متأكد من حذف هذا الذكر؟',
                              ),
                              actions: [
                                TextButton(
                                  onPressed:
                                      () => Navigator.of(context).pop(false),
                                  child: const Text('إلغاء'),
                                ),
                                TextButton(
                                  onPressed:
                                      () => Navigator.of(context).pop(true),
                                  child: const Text('حذف'),
                                ),
                              ],
                            ),
                      );
                    },
                    onDismissed: (direction) {
                      provider.deleteCustomZikr(zikr.id);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تم حذف الذكر'),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    },
                    child: ZikrListItem(
                      zikr: zikr,
                      onTap: () => _showEditZikrDialog(context, zikr),
                      onFavoriteToggle:
                          () => provider.toggleCustomZikrFavorite(zikr),
                      onCounterIncrement:
                          (zikr, count) =>
                              provider.updateCustomZikrCount(zikr, count),
                    ),
                  );
                },
              ),
              Positioned(
                bottom: 16,
                right: 16,
                child: FloatingActionButton(
                  onPressed: () => _showAddZikrDialog(context),
                  elevation: 4,
                  child: const Icon(Icons.add),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // دالة مساعدة لعرض رسائل للمستخدم
  void _showMessage(String message, bool isSuccess) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor:
              isSuccess
                  ? const Color(0xFF43A047)
                  : const Color(0xFFFF0000), // استخدام الألوان المحسنة
        ),
      );
    }
  }

  void _showAddZikrDialog(BuildContext context) {
    // إعادة ضبط حقول الإدخال
    _textController.clear();
    _countController.text = '1';
    _fadlController.clear();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (dialogContext) => _buildZikrForm(
            dialogContext,
            'إضافة ذكر جديد',
            onSave: () {
              if (_validateForm()) {
                // الحصول على مزود الأذكار
                final provider = Provider.of<AzkarProvider>(
                  dialogContext,
                  listen: false,
                );

                // إنشاء كائن الذكر الجديد
                final newZikr = Zikr(
                  id: 0, // سيتم تعيينه تلقائياً
                  text: _textController.text,
                  count: int.parse(_countController.text),
                  source: 'أذكاري الخاصة', // قيمة افتراضية
                  category: 'أذكاري الخاصة',
                  isFavorite: false,
                  currentCount: 0,
                  isCustom: true, // تعيين علامة أنه ذكر مخصص
                  fadl:
                      _fadlController.text.isNotEmpty
                          ? _fadlController.text
                          : null,
                );

                // إغلاق النموذج قبل العملية غير المتزامنة
                Navigator.pop(dialogContext);

                provider
                    .addCustomZikr(newZikr)
                    .then((success) {
                      if (success) {
                        _showMessage('تم إضافة الذكر بنجاح', true);
                      } else {
                        _showMessage('حدث خطأ أثناء إضافة الذكر', false);
                      }
                    })
                    .catchError((error) {
                      _showMessage('حدث خطأ أثناء إضافة الذكر: $error', false);
                    });
              }
            },
          ),
    );
  }

  void _showEditZikrDialog(BuildContext context, Zikr zikr) {
    // تعبئة حقول الإدخال بقيم الذكر الحالي
    _textController.text = zikr.text;
    _countController.text = zikr.count.toString();
    _fadlController.text = zikr.fadl ?? '';

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (dialogContext) => _buildZikrForm(
            dialogContext,
            'تعديل الذكر',
            onSave: () {
              if (_validateForm()) {
                // الحصول على مزود الأذكار
                final provider = Provider.of<AzkarProvider>(
                  dialogContext,
                  listen: false,
                );

                // إنشاء كائن الذكر المحدث
                final updatedZikr = Zikr(
                  id: zikr.id,
                  text: _textController.text,
                  count: int.parse(_countController.text),
                  source: 'أذكاري الخاصة', // قيمة افتراضية
                  category: zikr.category,
                  fadl:
                      _fadlController.text.isNotEmpty
                          ? _fadlController.text
                          : null,
                  isFavorite: zikr.isFavorite,
                  currentCount: zikr.currentCount,
                  isCustom: true, // تعيين علامة أنه ذكر مخصص
                );

                // إغلاق النموذج قبل العملية غير المتزامنة
                Navigator.pop(dialogContext);

                provider
                    .updateCustomZikr(updatedZikr)
                    .then((success) {
                      if (success) {
                        _showMessage('تم تحديث الذكر بنجاح', true);
                      } else {
                        _showMessage('حدث خطأ أثناء تحديث الذكر', false);
                      }
                    })
                    .catchError((error) {
                      _showMessage('حدث خطأ أثناء تحديث الذكر: $error', false);
                    });
              }
            },
          ),
    );
  }

  Widget _buildZikrForm(
    BuildContext context,
    String title, {
    required VoidCallback onSave,
  }) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 16,
        right: 16,
        top: 16,
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _textController,
              decoration: const InputDecoration(
                labelText: 'نص الذكر *',
                border: OutlineInputBorder(),
                hintText: 'أدخل نص الذكر',
              ),
              maxLines: 3,
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.right,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _countController,
              decoration: const InputDecoration(
                labelText: 'عدد التكرار * (الحد الأقصى: 200)',
                border: OutlineInputBorder(),
                hintText: 'عدد مرات التكرار (1-200)',
              ),
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _fadlController,
              decoration: const InputDecoration(
                labelText: 'فضل الذكر (اختياري)',
                border: OutlineInputBorder(),
                hintText: 'أدخل فضل الذكر',
              ),
              maxLines: 2,
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.right,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(onPressed: onSave, child: const Text('حفظ')),
              ],
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  bool _validateForm() {
    if (_textController.text.trim().isEmpty) {
      _showValidationError('يرجى إدخال نص الذكر');
      AppLogger.warning('محاولة إضافة ذكر خاص بدون نص');
      return false;
    }

    if (_countController.text.trim().isEmpty) {
      _showValidationError('يرجى إدخال عدد التكرار');
      AppLogger.warning('محاولة إضافة ذكر خاص بدون عدد تكرار');
      return false;
    }

    final count = int.tryParse(_countController.text);
    if (count == null || count < 1) {
      _showValidationError('يرجى إدخال عدد تكرار صحيح');
      AppLogger.warning(
        'محاولة إضافة ذكر خاص بعدد تكرار غير صالح: ${_countController.text}',
      );
      return false;
    }

    // التحقق من أن عدد التكرار لا يتجاوز 200
    if (count > 200) {
      _showValidationError('الحد الأقصى لعدد التكرار هو 200');
      AppLogger.warning(
        'محاولة إضافة ذكر خاص بعدد تكرار أكبر من الحد المسموح: $count',
      );
      return false;
    }

    return true;
  }

  void _showValidationError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
