# تطبيق أذكاري

تطبيق أذكار إسلامي يومي مبني باستخدام Flutter/Dart.

## هيكل المشروع

هذا المشروع يحتوي على هيكل خاص حيث يوجد مجلد `azkary` داخل المجلد الرئيسي. المجلد الداخلي هو المشروع الفعلي.

```
azkary/                  # المجلد الرئيسي للمستودع
├── .vscode/             # إعدادات VS Code
│   ├── launch.json      # تكوينات التشغيل
│   └── settings.json    # إعدادات المحرر
├── azkary/              # مشروع Flutter الفعلي
│   ├── lib/             # كود المصدر
│   ├── assets/          # الموارد (صور، أصوات، بيانات)
│   ├── android/         # ملفات خاصة بنظام Android
│   ├── ios/             # ملفات خاصة بنظام iOS
│   └── pubspec.yaml     # تبعيات المشروع
├── run_app.bat          # سكريبت لتشغيل التطبيق
├── build_app.bat        # سكريبت لبناء التطبيق
└── clean_build.bat      # سكريبت لتنظيف وإعادة بناء التطبيق
```

## كيفية تشغيل المشروع

### الطريقة 1: استخدام السكريبتات

يمكنك استخدام السكريبتات التالية لتسهيل العمل مع المشروع:

- `run_app.bat`: لتشغيل التطبيق في المتصفح
- `build_app.bat`: لبناء ملف APK للتطبيق
- `clean_build.bat`: لتنظيف وإعادة بناء المشروع

### الطريقة 2: استخدام VS Code

1. افتح المجلد الرئيسي في VS Code
2. اضغط على F5 أو استخدم قائمة Run & Debug
3. اختر أحد تكوينات التشغيل المتاحة:
   - Azkary (Flutter)
   - Azkary (Flutter, profile mode)
   - Azkary (Flutter, release mode)
   - Azkary (Chrome)

### الطريقة 3: استخدام سطر الأوامر

```bash
# انتقل إلى مجلد المشروع الفعلي
cd azkary

# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق
flutter run

# بناء التطبيق للأندرويد
flutter build apk --release
```

## ملاحظة هامة حول المسارات

عند العمل مع هذا المشروع، تأكد دائمًا من أنك تعمل في المجلد الصحيح:

- استخدم `azkary/lib/...` للإشارة إلى ملفات المصدر
- استخدم `azkary/assets/...` للإشارة إلى الموارد

تم تكوين VS Code للتعرف تلقائيًا على المجلد الصحيح للمشروع، لكن عند استخدام سطر الأوامر، تأكد من الانتقال إلى المجلد `azkary` أولاً.

## حل مشاكل المسارات

إذا واجهت مشاكل في المسارات، جرب الخطوات التالية:

1. استخدم السكريبتات المتوفرة بدلاً من كتابة الأوامر يدويًا
2. تأكد من أنك في المجلد الصحيح قبل تنفيذ أي أمر
3. أعد فتح VS Code إذا كانت الإعدادات لا تطبق بشكل صحيح
