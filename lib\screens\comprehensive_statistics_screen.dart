import 'package:flutter/material.dart';
import '../services/comprehensive_statistics_service.dart';
import '../models/comprehensive_statistics.dart';

/// شاشة الإحصائيات الشاملة المحسنة
class ComprehensiveStatisticsScreen extends StatefulWidget {
  const ComprehensiveStatisticsScreen({super.key});

  @override
  State<ComprehensiveStatisticsScreen> createState() =>
      _ComprehensiveStatisticsScreenState();
}

class _ComprehensiveStatisticsScreenState
    extends State<ComprehensiveStatisticsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ComprehensiveStatisticsService _statsService =
      ComprehensiveStatisticsService();
  ComprehensiveStatistics? _todayStats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadStatistics();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadStatistics() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _todayStats = _statsService.todayStats;
    } catch (e) {
      debugPrint('خطأ في تحميل الإحصائيات: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الإحصائيات الشاملة'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
      ),
      body: Column(
        children: [
          // شريط التبويبات
          Container(
            color: theme.colorScheme.surface,
            child: TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'اليوم'),
                Tab(text: 'الأذكار'),
                Tab(text: 'الأسئلة'),
                Tab(text: 'التسبيح'),
              ],
              labelColor: theme.colorScheme.primary,
              unselectedLabelColor:
                  theme.colorScheme.onSurface.withValues(alpha: 0.6),
              indicatorColor: theme.colorScheme.primary,
            ),
          ),

          // محتوى التبويبات
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildTodayTab(),
                      _buildAzkarTab(),
                      _buildQuestionsTab(),
                      _buildTasbihTab(),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  /// تبويب إحصائيات اليوم
  Widget _buildTodayTab() {
    if (_todayStats == null) {
      return const Center(
        child: Text('لا توجد إحصائيات لليوم'),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadStatistics,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة ملخص اليوم
            _buildSummaryCard(),
            const SizedBox(height: 16),

            // بطاقة الاستخدام العام
            _buildUsageCard(),
            const SizedBox(height: 16),

            // بطاقة السلسلة المتتالية
            _buildStreakCard(),
          ],
        ),
      ),
    );
  }

  /// تبويب إحصائيات الأذكار
  Widget _buildAzkarTab() {
    if (_todayStats == null) {
      return const Center(
        child: Text('لا توجد إحصائيات للأذكار'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إحصائيات عامة للأذكار
          _buildAzkarSummaryCard(),
          const SizedBox(height: 16),

          // إحصائيات كل فئة
          if (_todayStats!.azkarStats.isNotEmpty) ...[
            Text(
              'إحصائيات الفئات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),
            ..._todayStats!.azkarStats.entries.map(
              (entry) => _buildCategoryCard(entry.key, entry.value),
            ),
          ],
        ],
      ),
    );
  }

  /// تبويب إحصائيات الأسئلة
  Widget _buildQuestionsTab() {
    if (_todayStats == null) {
      return const Center(
        child: Text('لا توجد إحصائيات للأسئلة'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildQuestionsCard(),
        ],
      ),
    );
  }

  /// تبويب إحصائيات التسبيح
  Widget _buildTasbihTab() {
    if (_todayStats == null) {
      return const Center(
        child: Text('لا توجد إحصائيات للتسبيح'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTasbihCard(),
        ],
      ),
    );
  }

  /// بطاقة ملخص اليوم
  Widget _buildSummaryCard() {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.today,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'ملخص اليوم',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'الأذكار المكتملة',
                    '${_todayStats!.totalAzkarCompleted}',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الأسئلة المجابة',
                    '${_todayStats!.questionsAnswered}',
                    Icons.quiz,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'التسبيحات',
                    '${_todayStats!.tasbihCount}',
                    Icons.circle,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'فتح التطبيق',
                    '${_todayStats!.appOpenCount}',
                    Icons.open_in_new,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بطاقة الاستخدام العام
  Widget _buildUsageCard() {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  color: theme.colorScheme.secondary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'الاستخدام العام',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.secondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatItem(
              'إجمالي وقت الاستخدام',
              '${_todayStats!.totalUsageTime.inMinutes} دقيقة',
              Icons.timer,
              theme.colorScheme.secondary,
            ),
            const SizedBox(height: 8),
            _buildStatItem(
              'وقت الأذكار',
              '${_todayStats!.totalAzkarTime.inMinutes} دقيقة',
              Icons.book,
              theme.colorScheme.secondary,
            ),
          ],
        ),
      ),
    );
  }

  /// بطاقة السلسلة المتتالية
  Widget _buildStreakCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.local_fire_department,
              color: Colors.orange,
              size: 32,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'السلسلة المتتالية',
                    style: TextStyle(
                      fontSize: 16,
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withValues(alpha: 0.7),
                    ),
                  ),
                  Text(
                    '${_todayStats!.streakDays} يوم',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
            if (_todayStats!.isCompletedDay)
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'مكتمل',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// بطاقة ملخص الأذكار
  Widget _buildAzkarSummaryCard() {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الأذكار',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'الأذكار المكتملة',
                    '${_todayStats!.totalAzkarCompleted}',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الجلسات',
                    '${_todayStats!.totalAzkarSessions}',
                    Icons.event,
                    Colors.blue,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بطاقة فئة الأذكار
  Widget _buildCategoryCard(String categoryName, CategoryStatistics stats) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              categoryName,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Text('${stats.completedAzkar}/${stats.totalAzkar}'),
                ),
                Text(
                  '${stats.completionPercentage.toInt()}%',
                  style: TextStyle(
                    color: stats.isCompleted ? Colors.green : Colors.orange,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            LinearProgressIndicator(
              value: stats.completionPercentage / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                stats.isCompleted ? Colors.green : Colors.orange,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بطاقة الأسئلة
  Widget _buildQuestionsCard() {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات الأسئلة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'الأسئلة المجابة',
                    '${_todayStats!.questionsAnswered}',
                    Icons.quiz,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الإجابات الصحيحة',
                    '${_todayStats!.correctAnswers}',
                    Icons.check,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'الإجابات الخاطئة',
                    '${_todayStats!.wrongAnswers}',
                    Icons.close,
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'نسبة الدقة',
                    '${_todayStats!.questionsAccuracy.toInt()}%',
                    Icons.percent,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بطاقة التسبيح
  Widget _buildTasbihCard() {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات التسبيح',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'عدد التسبيحات',
                    '${_todayStats!.tasbihCount}',
                    Icons.circle,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الجلسات',
                    '${_todayStats!.tasbihSessions}',
                    Icons.event,
                    Colors.blue,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// عنصر إحصائية
  Widget _buildStatItem(
      String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color:
                Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
