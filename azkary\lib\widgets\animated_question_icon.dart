import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../utils/color_extensions.dart';

/// أيقونة السؤال اليومي المتحركة واللامعة
class AnimatedQuestionIcon extends StatefulWidget {
  final bool isAnswered;
  final VoidCallback? onTap;
  final double size;
  final bool showMiniVersion;

  const AnimatedQuestionIcon({
    super.key,
    required this.isAnswered,
    this.onTap,
    this.size = 60,
    this.showMiniVersion = false,
  });

  @override
  State<AnimatedQuestionIcon> createState() => _AnimatedQuestionIconState();
}

class _AnimatedQuestionIconState extends State<AnimatedQuestionIcon>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _shimmerController;
  late AnimationController _bounceController;

  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _shimmerAnimation;
  late Animation<double> _bounceAnimation;

  @override
  void initState() {
    super.initState();

    // تحكم في الدوران
    _rotationController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(begin: 0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    // تحكم في النبض
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // تحكم في اللمعان
    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _shimmerAnimation = Tween<double>(begin: -1.0, end: 1.0).animate(
      CurvedAnimation(parent: _shimmerController, curve: Curves.easeInOut),
    );

    // تحكم في الارتداد
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _bounceAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _bounceController, curve: Curves.elasticOut),
    );

    // بدء التأثيرات إذا لم يتم الإجابة
    if (!widget.isAnswered) {
      _startAnimations();
    }

    // بدء تأثير الارتداد
    _bounceController.forward();
  }

  void _startAnimations() {
    _rotationController.repeat();
    _pulseController.repeat(reverse: true);
    _shimmerController.repeat(reverse: true);
  }

  void _stopAnimations() {
    _rotationController.stop();
    _pulseController.stop();
    _shimmerController.stop();
  }

  @override
  void didUpdateWidget(AnimatedQuestionIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isAnswered != oldWidget.isAnswered) {
      if (widget.isAnswered) {
        _stopAnimations();
      } else {
        _startAnimations();
      }
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    _shimmerController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (widget.showMiniVersion) {
      return _buildMiniIcon(theme);
    }

    return _buildFullIcon(theme);
  }

  Widget _buildMiniIcon(ThemeData theme) {
    return AnimatedBuilder(
      animation: _bounceAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _bounceAnimation.value,
          child: GestureDetector(
            onTap: widget.onTap,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors:
                      widget.isAnswered
                          ? [Colors.green, Colors.green.shade300]
                          : [
                            theme.colorScheme.primary,
                            AppColors.primaryWithOpacity(
                              theme.colorScheme.primary,
                              0.7,
                            ),
                          ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color:
                        widget.isAnswered
                            ? AppColors.greenWithOpacity(0.3)
                            : AppColors.primaryWithOpacity(
                              theme.colorScheme.primary,
                              0.3,
                            ),
                    blurRadius: 8,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Icon(
                widget.isAnswered ? Icons.check_circle : Icons.quiz,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFullIcon(ThemeData theme) {
    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _rotationAnimation,
          _pulseAnimation,
          _shimmerAnimation,
          _bounceAnimation,
        ]),
        builder: (context, child) {
          return Transform.scale(
            scale:
                _bounceAnimation.value *
                (widget.isAnswered ? 1.0 : _pulseAnimation.value),
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  colors:
                      widget.isAnswered
                          ? [
                              Colors.green,
                              Colors.green.shade300,
                              Colors.green.shade100,
                            ]
                          : [
                              theme.colorScheme.primary,
                              AppColors.primaryWithOpacity(
                                theme.colorScheme.primary,
                                0.8,
                              ),
                              AppColors.primaryWithOpacity(
                                theme.colorScheme.primary,
                                0.4,
                              ),
                            ],
                ),
                boxShadow: [
                  BoxShadow(
                    color:
                        widget.isAnswered
                            ? AppColors.greenWithOpacity(0.4)
                            : AppColors.primaryWithOpacity(
                                theme.colorScheme.primary,
                                0.4,
                              ),
                    blurRadius: widget.isAnswered ? 15 : 20,
                    spreadRadius: widget.isAnswered ? 3 : 5,
                  ),
                  if (!widget.isAnswered)
                    BoxShadow(
                      color: AppColors.whiteWithOpacity(0.3),
                      blurRadius: 30,
                      spreadRadius: 10,
                    ),
                ],
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // تأثير اللمعان
                  if (!widget.isAnswered)
                    ClipOval(
                      child: Container(
                        width: widget.size,
                        height: widget.size,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment(
                              -1.0 + _shimmerAnimation.value,
                              -1.0,
                            ),
                            end: Alignment(
                              1.0 + _shimmerAnimation.value,
                              1.0,
                            ),
                            colors: [
                              Colors.transparent,
                              AppColors.whiteWithOpacity(0.4),
                              Colors.transparent,
                            ],
                            stops: const [0.0, 0.5, 1.0],
                          ),
                        ),
                      ),
                    ),

                  // الأيقونة
                  Icon(
                    widget.isAnswered ? Icons.check_circle : Icons.quiz,
                    color: Colors.white,
                    size: widget.size * 0.5,
                  ),

                  // نقاط لامعة حول الأيقونة
                  if (!widget.isAnswered)
                    ...List.generate(8, (index) {
                      final angle = (index * math.pi * 2) / 8;
                      final radius = widget.size * 0.6;
                      return Transform.translate(
                        offset: Offset(
                          math.cos(angle + _rotationAnimation.value) * radius,
                          math.sin(angle + _rotationAnimation.value) * radius,
                        ),
                        child: Container(
                          width: 4,
                          height: 4,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.whiteWithOpacity(
                              0.7 +
                                  0.3 *
                                      math.sin(
                                        _shimmerAnimation.value * math.pi,
                                      ),
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.whiteWithOpacity(0.5),
                                blurRadius: 4,
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

/// تأثير اختفاء البطاقة مع تحويلها لأيقونة
class QuestionCardTransition extends StatefulWidget {
  final Widget child;
  final bool isAnswered;
  final Duration duration;

  const QuestionCardTransition({
    super.key,
    required this.child,
    required this.isAnswered,
    this.duration = const Duration(milliseconds: 800),
  });

  @override
  State<QuestionCardTransition> createState() => _QuestionCardTransitionState();
}

class _QuestionCardTransitionState extends State<QuestionCardTransition>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(duration: widget.duration, vsync: this);

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.6, curve: Curves.easeInBack),
      ),
    );

    _opacityAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0, -1),
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.3, 1.0, curve: Curves.easeInOut),
      ),
    );
  }

  @override
  void didUpdateWidget(QuestionCardTransition oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isAnswered && !oldWidget.isAnswered) {
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.translate(
            offset: Offset(
              _slideAnimation.value.dx * MediaQuery.of(context).size.width,
              _slideAnimation.value.dy * 100,
            ),
            child: Opacity(
              opacity: _opacityAnimation.value,
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}


