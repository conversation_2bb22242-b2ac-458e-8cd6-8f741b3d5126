/// نموذج لطرق عرض القرآن الكريم
enum QuranViewMode {
  /// عرض السور كقائمة
  list,

  /// عرض السور كصفحات
  pages,

  /// عرض السور كمصحف
  mushaf,

  /// عرض السور بوضع التمرير العمودي
  verticalScroll,
}

/// امتداد لتحويل طريقة العرض إلى نص
extension QuranViewModeExtension on QuranViewMode {
  /// الحصول على اسم طريقة العرض بالعربية
  String get arabicName {
    switch (this) {
      case QuranViewMode.list:
        return 'قائمة';
      case QuranViewMode.pages:
        return 'صفحات';
      case QuranViewMode.mushaf:
        return 'مصحف';
      case QuranViewMode.verticalScroll:
        return 'تمرير عمودي';
    }
  }

  /// الحصول على وصف طريقة العرض بالعربية
  String get description {
    switch (this) {
      case QuranViewMode.list:
        return 'عرض السور كقائمة مع إمكانية التمرير';
      case QuranViewMode.pages:
        return 'عرض السور كصفحات منفصلة';
      case QuranViewMode.mushaf:
        return 'عرض السور كمصحف متكامل';
      case QuranViewMode.verticalScroll:
        return 'عرض السور بشكل عمودي متواصل مع إظهار علامات الأجزاء والأحزاب';
    }
  }
}
