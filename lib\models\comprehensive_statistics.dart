import 'dart:convert';

/// نموذج الإحصائيات الشاملة للتطبيق
class ComprehensiveStatistics {
  final String date; // تاريخ الإحصائيات (YYYY-MM-DD)
  final DateTime createdAt;
  final DateTime updatedAt;

  // إحصائيات الأذكار
  final Map<String, CategoryStatistics> azkarStats;
  final int totalAzkarCompleted;
  final int totalAzkarSessions;
  final Duration totalAzkarTime;

  // إحصائيات الأسئلة الإسلامية
  final int questionsAnswered;
  final int correctAnswers;
  final int wrongAnswers;
  final double questionsAccuracy;

  // إحصائيات التسبيح
  final int tasbihCount;
  final int tasbihSessions;
  final Duration tasbihTime;

  // إحصائيات الاستخدام العامة
  final int appOpenCount;
  final Duration totalUsageTime;
  final int streakDays;
  final bool isCompletedDay;

  // إحصائيات القرآن
  final int quranReadingTime; // بالدقائق
  final int versesRead;
  final int chaptersRead;

  // إحصائيات المفضلة
  final int favoritesAdded;
  final int favoritesRemoved;

  ComprehensiveStatistics({
    required this.date,
    required this.createdAt,
    required this.updatedAt,
    this.azkarStats = const {},
    this.totalAzkarCompleted = 0,
    this.totalAzkarSessions = 0,
    this.totalAzkarTime = Duration.zero,
    this.questionsAnswered = 0,
    this.correctAnswers = 0,
    this.wrongAnswers = 0,
    this.questionsAccuracy = 0.0,
    this.tasbihCount = 0,
    this.tasbihSessions = 0,
    this.tasbihTime = Duration.zero,
    this.appOpenCount = 0,
    this.totalUsageTime = Duration.zero,
    this.streakDays = 0,
    this.isCompletedDay = false,
    this.quranReadingTime = 0,
    this.versesRead = 0,
    this.chaptersRead = 0,
    this.favoritesAdded = 0,
    this.favoritesRemoved = 0,
  });

  /// إنشاء نسخة محدثة من الإحصائيات
  ComprehensiveStatistics copyWith({
    String? date,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, CategoryStatistics>? azkarStats,
    int? totalAzkarCompleted,
    int? totalAzkarSessions,
    Duration? totalAzkarTime,
    int? questionsAnswered,
    int? correctAnswers,
    int? wrongAnswers,
    double? questionsAccuracy,
    int? tasbihCount,
    int? tasbihSessions,
    Duration? tasbihTime,
    int? appOpenCount,
    Duration? totalUsageTime,
    int? streakDays,
    bool? isCompletedDay,
    int? quranReadingTime,
    int? versesRead,
    int? chaptersRead,
    int? favoritesAdded,
    int? favoritesRemoved,
  }) {
    return ComprehensiveStatistics(
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      azkarStats: azkarStats ?? this.azkarStats,
      totalAzkarCompleted: totalAzkarCompleted ?? this.totalAzkarCompleted,
      totalAzkarSessions: totalAzkarSessions ?? this.totalAzkarSessions,
      totalAzkarTime: totalAzkarTime ?? this.totalAzkarTime,
      questionsAnswered: questionsAnswered ?? this.questionsAnswered,
      correctAnswers: correctAnswers ?? this.correctAnswers,
      wrongAnswers: wrongAnswers ?? this.wrongAnswers,
      questionsAccuracy: questionsAccuracy ?? this.questionsAccuracy,
      tasbihCount: tasbihCount ?? this.tasbihCount,
      tasbihSessions: tasbihSessions ?? this.tasbihSessions,
      tasbihTime: tasbihTime ?? this.tasbihTime,
      appOpenCount: appOpenCount ?? this.appOpenCount,
      totalUsageTime: totalUsageTime ?? this.totalUsageTime,
      streakDays: streakDays ?? this.streakDays,
      isCompletedDay: isCompletedDay ?? this.isCompletedDay,
      quranReadingTime: quranReadingTime ?? this.quranReadingTime,
      versesRead: versesRead ?? this.versesRead,
      chaptersRead: chaptersRead ?? this.chaptersRead,
      favoritesAdded: favoritesAdded ?? this.favoritesAdded,
      favoritesRemoved: favoritesRemoved ?? this.favoritesRemoved,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'azkarStats': azkarStats.map((key, value) => MapEntry(key, value.toJson())),
      'totalAzkarCompleted': totalAzkarCompleted,
      'totalAzkarSessions': totalAzkarSessions,
      'totalAzkarTime': totalAzkarTime.inSeconds,
      'questionsAnswered': questionsAnswered,
      'correctAnswers': correctAnswers,
      'wrongAnswers': wrongAnswers,
      'questionsAccuracy': questionsAccuracy,
      'tasbihCount': tasbihCount,
      'tasbihSessions': tasbihSessions,
      'tasbihTime': tasbihTime.inSeconds,
      'appOpenCount': appOpenCount,
      'totalUsageTime': totalUsageTime.inSeconds,
      'streakDays': streakDays,
      'isCompletedDay': isCompletedDay,
      'quranReadingTime': quranReadingTime,
      'versesRead': versesRead,
      'chaptersRead': chaptersRead,
      'favoritesAdded': favoritesAdded,
      'favoritesRemoved': favoritesRemoved,
    };
  }

  /// إنشاء من JSON
  factory ComprehensiveStatistics.fromJson(Map<String, dynamic> json) {
    return ComprehensiveStatistics(
      date: json['date'] ?? '',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      azkarStats: (json['azkarStats'] as Map<String, dynamic>? ?? {})
          .map((key, value) => MapEntry(key, CategoryStatistics.fromJson(value))),
      totalAzkarCompleted: json['totalAzkarCompleted'] ?? 0,
      totalAzkarSessions: json['totalAzkarSessions'] ?? 0,
      totalAzkarTime: Duration(seconds: json['totalAzkarTime'] ?? 0),
      questionsAnswered: json['questionsAnswered'] ?? 0,
      correctAnswers: json['correctAnswers'] ?? 0,
      wrongAnswers: json['wrongAnswers'] ?? 0,
      questionsAccuracy: (json['questionsAccuracy'] ?? 0.0).toDouble(),
      tasbihCount: json['tasbihCount'] ?? 0,
      tasbihSessions: json['tasbihSessions'] ?? 0,
      tasbihTime: Duration(seconds: json['tasbihTime'] ?? 0),
      appOpenCount: json['appOpenCount'] ?? 0,
      totalUsageTime: Duration(seconds: json['totalUsageTime'] ?? 0),
      streakDays: json['streakDays'] ?? 0,
      isCompletedDay: json['isCompletedDay'] ?? false,
      quranReadingTime: json['quranReadingTime'] ?? 0,
      versesRead: json['versesRead'] ?? 0,
      chaptersRead: json['chaptersRead'] ?? 0,
      favoritesAdded: json['favoritesAdded'] ?? 0,
      favoritesRemoved: json['favoritesRemoved'] ?? 0,
    );
  }

  /// تحويل إلى نص JSON
  String toJsonString() => jsonEncode(toJson());

  /// إنشاء من نص JSON
  factory ComprehensiveStatistics.fromJsonString(String jsonString) {
    return ComprehensiveStatistics.fromJson(jsonDecode(jsonString));
  }

  @override
  String toString() {
    return 'ComprehensiveStatistics(date: $date, totalAzkarCompleted: $totalAzkarCompleted, questionsAnswered: $questionsAnswered, tasbihCount: $tasbihCount, appOpenCount: $appOpenCount)';
  }
}

/// إحصائيات فئة الأذكار
class CategoryStatistics {
  final String categoryName;
  final int completedAzkar;
  final int totalAzkar;
  final int sessions;
  final Duration timeSpent;
  final bool isCompleted;
  final DateTime? lastCompletedAt;

  CategoryStatistics({
    required this.categoryName,
    this.completedAzkar = 0,
    this.totalAzkar = 0,
    this.sessions = 0,
    this.timeSpent = Duration.zero,
    this.isCompleted = false,
    this.lastCompletedAt,
  });

  /// نسبة الإكمال
  double get completionPercentage {
    if (totalAzkar == 0) return 0.0;
    return (completedAzkar / totalAzkar) * 100;
  }

  /// إنشاء نسخة محدثة
  CategoryStatistics copyWith({
    String? categoryName,
    int? completedAzkar,
    int? totalAzkar,
    int? sessions,
    Duration? timeSpent,
    bool? isCompleted,
    DateTime? lastCompletedAt,
  }) {
    return CategoryStatistics(
      categoryName: categoryName ?? this.categoryName,
      completedAzkar: completedAzkar ?? this.completedAzkar,
      totalAzkar: totalAzkar ?? this.totalAzkar,
      sessions: sessions ?? this.sessions,
      timeSpent: timeSpent ?? this.timeSpent,
      isCompleted: isCompleted ?? this.isCompleted,
      lastCompletedAt: lastCompletedAt ?? this.lastCompletedAt,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'categoryName': categoryName,
      'completedAzkar': completedAzkar,
      'totalAzkar': totalAzkar,
      'sessions': sessions,
      'timeSpent': timeSpent.inSeconds,
      'isCompleted': isCompleted,
      'lastCompletedAt': lastCompletedAt?.toIso8601String(),
    };
  }

  /// إنشاء من JSON
  factory CategoryStatistics.fromJson(Map<String, dynamic> json) {
    return CategoryStatistics(
      categoryName: json['categoryName'] ?? '',
      completedAzkar: json['completedAzkar'] ?? 0,
      totalAzkar: json['totalAzkar'] ?? 0,
      sessions: json['sessions'] ?? 0,
      timeSpent: Duration(seconds: json['timeSpent'] ?? 0),
      isCompleted: json['isCompleted'] ?? false,
      lastCompletedAt: json['lastCompletedAt'] != null
          ? DateTime.parse(json['lastCompletedAt'])
          : null,
    );
  }
}
