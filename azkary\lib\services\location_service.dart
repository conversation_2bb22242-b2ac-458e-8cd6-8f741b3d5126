import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/logger.dart';

/// خدمة الموقع
class LocationService {
  /// الحصول على الموقع الحالي
  static Future<Position?> getCurrentPosition() async {
    try {
      // التحقق من صلاحيات الموقع
      final permissionStatus = await Permission.location.request();

      if (permissionStatus.isGranted) {
        // التحقق من تفعيل خدمة الموقع
        final serviceEnabled = await Geolocator.isLocationServiceEnabled();

        if (!serviceEnabled) {
          // محاولة تفعيل خدمة الموقع
          await Geolocator.openLocationSettings();
          return null;
        }

        // الحصول على الموقع الحالي
        return await Geolocator.getCurrentPosition(
          locationSettings: const LocationSettings(
            accuracy: LocationAccuracy.high,
            timeLimit: Duration(seconds: 10),
          ),
        );
      } else {
        // طلب الصلاحية مرة أخرى
        await openAppSettings();
        return null;
      }
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الموقع: $e');
      return null;
    }
  }

  /// الحصول على المسافة بين موقعين
  static double getDistanceBetween(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// الحصول على الاتجاه بين موقعين
  static double getBearingBetween(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.bearingBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }
}
