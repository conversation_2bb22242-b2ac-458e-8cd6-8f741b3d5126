import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/azkar_model.dart';
import '../screens/zikr_detail_screen.dart';
import '../services/azkar_provider.dart';

class AzkarSearchDelegate extends SearchDelegate<String> {
  @override
  String get searchFieldLabel => 'ابحث عن ذكر...';

  @override
  TextStyle? get searchFieldStyle => const TextStyle(
        fontFamily: 'Tajawal',
        fontSize: 16,
      );

  @override
  ThemeData appBarTheme(BuildContext context) {
    final theme = Theme.of(context);
    return theme.copyWith(
      appBarTheme: AppBarTheme(
        backgroundColor: theme.scaffoldBackgroundColor,
        elevation: 0,
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: InputBorder.none,
        hintStyle: TextStyle(
          color: theme.colorScheme.onSurface.withAlpha(128), // 0.5 opacity = 128 alpha
        ),
      ),
    );
  }

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      if (query.isNotEmpty)
        IconButton(
          icon: const Icon(Icons.clear),
          onPressed: () {
            query = '';
          },
        ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults(context);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults(context);
  }

  Widget _buildSearchResults(BuildContext context) {
    if (query.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: 64,
              color: Theme.of(context).colorScheme.primary.withAlpha(128), // 0.5 opacity = 128 alpha
            ),
            const SizedBox(height: 16),
            const Text(
              'ابحث عن أي ذكر',
              style: TextStyle(
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'يمكنك البحث بالنص أو المصدر',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return FutureBuilder<List<Zikr>>(
      future: Provider.of<AzkarProvider>(context, listen: false)
          .searchAzkar(query),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.search_off,
                  size: 64,
                  color: Theme.of(context).colorScheme.error.withAlpha(128), // 0.5 opacity = 128 alpha
                ),
                const SizedBox(height: 16),
                const Text(
                  'لا توجد نتائج',
                  style: TextStyle(
                    fontSize: 18,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'لا توجد نتائج تطابق "$query"',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        final results = snapshot.data!;
        return ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: results.length,
          itemBuilder: (context, index) {
            final zikr = results[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: ListTile(
                contentPadding: const EdgeInsets.all(16),
                title: Text(
                  zikr.text,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                subtitle: Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    'المصدر: ${zikr.source} | التصنيف: ${zikr.category}',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ZikrDetailScreen(zikr: zikr),
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
