import 'package:flutter/material.dart';
import 'package:adhan/adhan.dart';
import 'dart:async';
import 'offline_prayer_service.dart';
import '../utils/logger.dart';

/// مزود أوقات الصلاة
class PrayerProvider extends ChangeNotifier {
  PrayerTimes? _prayerTimes;
  String _locationName = 'جاري تحديد الموقع...';
  String _nextPrayerName = '';
  DateTime? _nextPrayerTime;
  Duration _remainingTime = Duration.zero;
  bool _isLoading = true;
  String _errorMessage = '';
  Timer? _timer;

  // Getters
  PrayerTimes? get prayerTimes => _prayerTimes;
  String get locationName => _locationName;
  String get nextPrayerName => _nextPrayerName;
  DateTime? get nextPrayerTime => _nextPrayerTime;
  Duration get remainingTime => _remainingTime;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;

  /// تهيئة مزود أوقات الصلاة
  Future<void> initialize() async {
    await _loadPrayerTimes();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  /// تحميل أوقات الصلاة
  Future<void> _loadPrayerTimes() async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      // محاولة استخدام الموقع المحفوظ
      final savedLocation = await OfflinePrayerService.getSavedLocation();
      if (savedLocation != null) {
        final latitude = savedLocation['latitude'];
        final longitude = savedLocation['longitude'];
        final cityName = savedLocation['cityName'];

        await _calculatePrayerTimesFromCoordinates(
          latitude,
          longitude,
          cityName,
        );
        return;
      }

      // إذا لم يكن هناك موقع محفوظ، استخدم الرياض كافتراضي
      final riyadhCoords = OfflinePrayerService.getCityCoordinates('الرياض');
      if (riyadhCoords != null) {
        await _calculatePrayerTimesFromCoordinates(
          riyadhCoords['lat']!,
          riyadhCoords['lng']!,
          'الرياض',
        );
      }
    } catch (e) {
      _errorMessage = 'حدث خطأ أثناء تحميل أوقات الصلاة: $e';
      _isLoading = false;
      AppLogger.error('خطأ في تحميل أوقات الصلاة: $e');
      notifyListeners();
    }
  }

  /// حساب أوقات الصلاة من الإحداثيات
  Future<void> _calculatePrayerTimesFromCoordinates(
    double latitude,
    double longitude,
    String cityName,
  ) async {
    try {
      final calculationMethod =
          await OfflinePrayerService.getSavedCalculationMethod();
      final madhab = await OfflinePrayerService.getSavedMadhab();

      final prayerTimes = OfflinePrayerService.calculatePrayerTimes(
        latitude: latitude,
        longitude: longitude,
        method: calculationMethod,
        madhab: madhab,
      );

      if (prayerTimes != null) {
        _prayerTimes = prayerTimes;
        _locationName = cityName;
        _isLoading = false;
        _errorMessage = '';

        // تحديد الصلاة التالية وبدء المؤقت
        _updateNextPrayer();
        _startTimer();

        notifyListeners();
      } else {
        _errorMessage = 'حدث خطأ أثناء حساب أوقات الصلاة';
        _isLoading = false;
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'حدث خطأ أثناء حساب أوقات الصلاة: $e';
      _isLoading = false;
      AppLogger.error('خطأ في حساب أوقات الصلاة: $e');
      notifyListeners();
    }
  }

  /// بدء المؤقت لتحديث العد التنازلي
  void _startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _calculateRemainingTime();

      // إذا انتهى الوقت أو أصبح سالباً، حدث الصلاة التالية
      if (_remainingTime.isNegative || _remainingTime.inSeconds <= 0) {
        _updateNextPrayer();
      }

      notifyListeners();
    });
  }

  /// تحديث الصلاة التالية
  void _updateNextPrayer() {
    if (_prayerTimes == null) return;

    final now = DateTime.now();
    final prayers = [
      {'name': 'الفجر', 'time': _prayerTimes!.fajr},
      {'name': 'الشروق', 'time': _prayerTimes!.sunrise},
      {'name': 'الظهر', 'time': _prayerTimes!.dhuhr},
      {'name': 'العصر', 'time': _prayerTimes!.asr},
      {'name': 'المغرب', 'time': _prayerTimes!.maghrib},
      {'name': 'العشاء', 'time': _prayerTimes!.isha},
    ];

    // البحث عن الصلاة التالية
    for (int i = 0; i < prayers.length; i++) {
      if (now.isBefore(prayers[i]['time'] as DateTime)) {
        _nextPrayerName = prayers[i]['name'] as String;
        _nextPrayerTime = prayers[i]['time'] as DateTime;
        _calculateRemainingTime();
        return;
      }
    }

    // إذا لم يتم العثور على صلاة تالية، فإن الصلاة التالية هي فجر الغد
    _nextPrayerName = 'الفجر';
    _nextPrayerTime = _getNextDayFajr();
    _calculateRemainingTime();
  }

  /// حساب الوقت المتبقي للصلاة التالية
  void _calculateRemainingTime() {
    if (_nextPrayerTime == null) return;

    final now = DateTime.now();
    _remainingTime = _nextPrayerTime!.difference(now);
  }

  /// الحصول على وقت صلاة الفجر لليوم التالي
  DateTime _getNextDayFajr() {
    if (_prayerTimes == null) {
      return DateTime.now().add(const Duration(days: 1));
    }

    try {
      final tomorrow = DateTime.now().add(const Duration(days: 1));
      final tomorrowComponents = DateComponents(
        tomorrow.year,
        tomorrow.month,
        tomorrow.day,
      );

      // استخدام نفس الإحداثيات والإعدادات المحفوظة
      double latitude = 24.7136; // الرياض كافتراضي
      double longitude = 46.6753;

      final coordinates = Coordinates(latitude, longitude);
      final params = CalculationMethod.egyptian.getParameters();
      params.madhab = Madhab.shafi;

      final nextDayPrayers = PrayerTimes(
        coordinates,
        tomorrowComponents,
        params,
      );
      return nextDayPrayers.fajr;
    } catch (e) {
      AppLogger.error('خطأ في حساب فجر اليوم التالي: $e');
      // في حالة الخطأ، أضف 24 ساعة للوقت الحالي
      return DateTime.now().add(const Duration(hours: 24));
    }
  }

  /// تحديث أوقات الصلاة يدوياً
  Future<void> refresh() async {
    await _loadPrayerTimes();
  }

  /// تنسيق الوقت المتبقي
  String getFormattedRemainingTime() {
    if (_remainingTime.isNegative || _remainingTime.inSeconds <= 0) {
      return '00:00:00';
    }

    final hours = _remainingTime.inHours;
    final minutes = _remainingTime.inMinutes.remainder(60);
    final seconds = _remainingTime.inSeconds.remainder(60);

    // إذا كان أقل من ساعة، اعرض فقط الدقائق والثواني
    if (hours == 0) {
      return '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    }

    return '${hours.toString().padLeft(2, '0')}:'
        '${minutes.toString().padLeft(2, '0')}:'
        '${seconds.toString().padLeft(2, '0')}';
  }

  /// تنسيق وقت الصلاة
  String getFormattedPrayerTime(DateTime time) {
    final hour = time.hour;
    final minute = time.minute;
    final period = hour >= 12 ? 'م' : 'ص';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

    return '${displayHour.toString().padLeft(2, '0')}:'
        '${minute.toString().padLeft(2, '0')} $period';
  }
}
