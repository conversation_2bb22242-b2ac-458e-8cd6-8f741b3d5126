import 'package:flutter/material.dart';

// تم نقل هذا المكون إلى أسفل الملف

/// زر متحرك عند النقر
class AnimatedPressableButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final VoidCallback? onLongPress;
  final Color? color;
  final Color? splashColor;
  final Color? highlightColor;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final double scale;
  final Duration duration;
  final bool enabled;

  const AnimatedPressableButton({
    super.key,
    required this.child,
    this.onPressed,
    this.onLongPress,
    this.color,
    this.splashColor,
    this.highlightColor,
    this.padding,
    this.borderRadius,
    this.scale = 0.95,
    this.duration = const Duration(milliseconds: 150),
    this.enabled = true,
  });

  @override
  State<AnimatedPressableButton> createState() =>
      _AnimatedPressableButtonState();
}

class _AnimatedPressableButtonState extends State<AnimatedPressableButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: widget.duration);
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.scale,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!widget.enabled) return;
    setState(() {
      // Start animation
    });
    _controller.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    if (!widget.enabled) return;
    setState(() {
      // End animation
    });
    _controller.reverse();
  }

  void _handleTapCancel() {
    if (!widget.enabled) return;
    setState(() {
      // Cancel animation
    });
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final borderRadius = widget.borderRadius ?? BorderRadius.circular(12);

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(scale: _scaleAnimation.value, child: child);
      },
      child: GestureDetector(
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        onTap: widget.enabled ? widget.onPressed : null,
        onLongPress: widget.enabled ? widget.onLongPress : null,
        child: Material(
          color: widget.color ?? Colors.transparent,
          borderRadius: borderRadius,
          child: Ink(
            decoration: BoxDecoration(
              borderRadius: borderRadius,
              color:
                  widget.enabled
                      ? widget.color ?? theme.colorScheme.primary
                      : theme.disabledColor,
            ),
            child: InkWell(
              borderRadius: borderRadius,
              splashColor: widget.splashColor,
              highlightColor: widget.highlightColor,
              onTap: widget.enabled ? widget.onPressed : null,
              onLongPress: widget.enabled ? widget.onLongPress : null,
              child: Padding(
                padding: widget.padding ?? const EdgeInsets.all(12),
                child: widget.child,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// مكون متحرك عند الظهور
class AnimatedAppearance extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Duration delay;
  final Curve curve;
  final Offset offset;
  final bool animate;

  const AnimatedAppearance({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 500),
    this.delay = Duration.zero,
    this.curve = Curves.easeOut,
    this.offset = const Offset(0, 50),
    this.animate = true,
  });

  @override
  State<AnimatedAppearance> createState() => _AnimatedAppearanceState();
}

class _AnimatedAppearanceState extends State<AnimatedAppearance>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: widget.duration);
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));
    _slideAnimation = Tween<Offset>(
      begin: widget.offset,
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    if (widget.animate) {
      Future.delayed(widget.delay, () {
        if (mounted) {
          _controller.forward();
        }
      });
    } else {
      _controller.value = 1.0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(position: _slideAnimation, child: widget.child),
    );
  }
}

/// مكون متحرك عند التحديث
class AnimatedCounter extends StatelessWidget {
  final int count;
  final TextStyle? style;
  final Duration duration;
  final Curve curve;

  const AnimatedCounter({
    super.key,
    required this.count,
    this.style,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
  });

  @override
  Widget build(BuildContext context) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0, end: count.toDouble()),
      duration: duration,
      curve: curve,
      builder: (context, value, child) {
        return Text(value.toInt().toString(), style: style);
      },
    );
  }
}

/// مكون متحرك للقائمة
class AnimatedListItem extends StatelessWidget {
  final Widget child;
  final int index;
  final Duration duration;
  final Duration delay;
  final Curve curve;
  final bool animate;

  const AnimatedListItem({
    super.key,
    required this.child,
    required this.index,
    this.duration = const Duration(milliseconds: 300), // تقليل مدة الحركة
    this.delay = const Duration(milliseconds: 30), // تقليل التأخير بين العناصر
    this.curve = Curves.easeOut,
    this.animate = true,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد الحد الأقصى للتأخير (10 عناصر فقط)
    final int cappedIndex = index > 10 ? 10 : index;

    // استخدام RepaintBoundary لتحسين الأداء
    return RepaintBoundary(
      child: AnimatedAppearance(
        duration: duration,
        // تحديد الحد الأقصى للتأخير لتجنب التأخير الطويل للعناصر البعيدة
        delay: Duration(milliseconds: delay.inMilliseconds * cappedIndex),
        curve: curve,
        animate: animate,
        child: child,
      ),
    );
  }
}

/// مكون متحرك من الأسفل إلى الأعلى باستخدام متحكم حركة
class AnimatedSlideFromBottom extends StatelessWidget {
  final Widget child;
  final AnimationController controller;
  final Duration delay;
  final Curve curve;
  final double offset;

  const AnimatedSlideFromBottom({
    super.key,
    required this.child,
    required this.controller,
    this.delay = Duration.zero,
    this.curve = Curves.easeOutCubic,
    this.offset = 50.0,
  });

  @override
  Widget build(BuildContext context) {
    // إنشاء حركة التلاشي
    final fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: controller,
        curve: Interval(0.0, 0.6, curve: curve),
      ),
    );

    // إنشاء حركة الانزلاق
    final slideAnimation = Tween<Offset>(
      begin: Offset(0, offset / 100),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: controller,
        curve: Interval(
          delay.inMilliseconds / 1000,
          (delay.inMilliseconds / 1000) + 0.6,
          curve: curve,
        ),
      ),
    );

    return FadeTransition(
      opacity: fadeAnimation,
      child: SlideTransition(position: slideAnimation, child: child),
    );
  }
}
