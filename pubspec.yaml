name: azkary
description: "تطبيق أذكاري - تطبيق للأذكار اليومية"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.2.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  provider: ^6.1.1
  sqflite: ^2.3.0
  path: ^1.8.3
  shared_preferences: ^2.2.2
  intl: ^0.19.0
  flutter_svg: ^2.0.9
  flutter_local_notifications: ^19.2.0
  page_transition: ^2.1.0
  audioplayers: ^5.2.1
  share_plus: ^7.2.1
  flutter_qiblah: ^3.1.0+1
  adhan: ^2.0.0-nullsafety.2
  geolocator: ^13.0.4
  permission_handler: ^11.3.0
  logger: ^2.5.0
  http: ^1.1.0
  flutter_tts: ^4.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  flutter_launcher_icons: ^0.13.1

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "azkary/assets/images/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "azkary/assets/images/app_icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "azkary/assets/images/app_icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "azkary/assets/images/app_icon.png"

flutter:
  uses-material-design: true

  assets:
    - azkary/assets/images/
    - assets/sounds/
    - assets/icons/
    - assets/data/

  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
    - family: Amiri
      fonts:
        - asset: assets/fonts/Amiri-Regular.ttf
        - asset: assets/fonts/Amiri-Bold.ttf
          weight: 700
        - asset: assets/fonts/Amiri-Slanted.ttf
          style: italic
        - asset: assets/fonts/Amiri-BoldSlanted.ttf
          weight: 700
          style: italic
    - family: Uthmani
      fonts:
        - asset: assets/fonts/UthmanicHafs1Ver18.ttf
