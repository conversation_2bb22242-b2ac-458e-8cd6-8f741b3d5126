import 'package:flutter/material.dart';
import 'dart:math' as math;

/// مكون لرسم زخارف إسلامية
class IslamicPattern extends StatelessWidget {
  final Color? color;
  final double opacity;

  const IslamicPattern({super.key, this.color, this.opacity = 0.1});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // تحديد اللون والشفافية بناءً على الوضع
    Color patternColor = color ?? theme.colorScheme.primary;
    double finalOpacity = opacity;

    // تعديل الشفافية حسب الوضع
    if (isDarkMode) {
      // في الأوضاع الداكنة، نقلل الشفافية لتجنب التداخل
      finalOpacity = opacity * 0.7;
      // في الوضع المعتم (تويتر)، نستخدم لون أفتح قليلاً
      if (theme.scaffoldBackgroundColor == const Color(0xFF15202B)) {
        patternColor = patternColor.withAlpha(100);
        finalOpacity = opacity * 0.6; // شفافية أقل للوضع المعتم
      }
    }

    return CustomPaint(
      painter: IslamicPatternPainter(
        color: patternColor,
        opacity: finalOpacity,
      ),
      size: Size.infinite,
    );
  }
}

/// رسام الزخارف الإسلامية
class IslamicPatternPainter extends CustomPainter {
  final Color color;
  final double opacity;

  IslamicPatternPainter({required this.color, required this.opacity});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color.withAlpha((opacity * 255).round())
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    final patternSize = 80.0;
    final horizontalCount = (size.width / patternSize).ceil() + 1;
    final verticalCount = (size.height / patternSize).ceil() + 1;

    for (int i = -1; i < horizontalCount; i++) {
      for (int j = -1; j < verticalCount; j++) {
        final centerX = i * patternSize;
        final centerY = j * patternSize;

        // رسم النمط الإسلامي
        _drawIslamicPattern(
          canvas,
          paint,
          Offset(centerX, centerY),
          patternSize,
        );
      }
    }
  }

  void _drawIslamicPattern(
    Canvas canvas,
    Paint paint,
    Offset center,
    double size,
  ) {
    // رسم نمط إسلامي محسن ومتنوع

    // النمط الأساسي - نجمة ثمانية مع دوائر متداخلة
    _drawEightPointedStar(canvas, paint, center, size * 0.35);

    // دائرة خارجية رئيسية
    canvas.drawCircle(center, size * 0.4, paint);

    // دائرة داخلية صغيرة
    canvas.drawCircle(center, size * 0.15, paint);

    // مثمن خارجي
    _drawOctagon(canvas, paint, center, size * 0.45);

    // مربع مدور في المنتصف
    _drawRotatedSquare(canvas, paint, center, size * 0.25);

    // أشعة زخرفية
    _drawDecorativeRays(canvas, paint, center, size * 0.5);

    // نقاط زخرفية في الزوايا
    _drawCornerDots(canvas, paint, center, size * 0.3);
  }

  void _drawEightPointedStar(
    Canvas canvas,
    Paint paint,
    Offset center,
    double radius,
  ) {
    final path = Path();

    for (int i = 0; i < 8; i++) {
      final angle = i * math.pi / 4;
      final outerX = center.dx + radius * math.cos(angle);
      final outerY = center.dy + radius * math.sin(angle);

      final innerAngle = angle + math.pi / 8;
      final innerRadius = radius * 0.4;
      final innerX = center.dx + innerRadius * math.cos(innerAngle);
      final innerY = center.dy + innerRadius * math.sin(innerAngle);

      if (i == 0) {
        path.moveTo(outerX, outerY);
      } else {
        path.lineTo(outerX, outerY);
      }

      path.lineTo(innerX, innerY);
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  void _drawOctagon(Canvas canvas, Paint paint, Offset center, double radius) {
    final path = Path();

    for (int i = 0; i < 8; i++) {
      final angle = i * math.pi / 4 + math.pi / 8;
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  // رسم مربع مدور
  void _drawRotatedSquare(
    Canvas canvas,
    Paint paint,
    Offset center,
    double size,
  ) {
    final path = Path();
    final halfSize = size / 2;

    // إنشاء مربع مدور بزاوية 45 درجة
    final points = [
      Offset(center.dx, center.dy - halfSize), // أعلى
      Offset(center.dx + halfSize, center.dy), // يمين
      Offset(center.dx, center.dy + halfSize), // أسفل
      Offset(center.dx - halfSize, center.dy), // يسار
    ];

    path.moveTo(points[0].dx, points[0].dy);
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  // رسم أشعة زخرفية
  void _drawDecorativeRays(
    Canvas canvas,
    Paint paint,
    Offset center,
    double radius,
  ) {
    for (int i = 0; i < 16; i++) {
      final angle = i * math.pi / 8;
      final startRadius = radius * 0.6;
      final endRadius = radius * 0.9;

      final startX = center.dx + startRadius * math.cos(angle);
      final startY = center.dy + startRadius * math.sin(angle);
      final endX = center.dx + endRadius * math.cos(angle);
      final endY = center.dy + endRadius * math.sin(angle);

      // رسم خط رفيع للأشعة
      final rayPaint =
          Paint()
            ..color = paint.color.withAlpha((paint.color.a * 0.5).round())
            ..style = PaintingStyle.stroke
            ..strokeWidth = 0.5;

      canvas.drawLine(Offset(startX, startY), Offset(endX, endY), rayPaint);
    }
  }

  // رسم نقاط زخرفية في الزوايا
  void _drawCornerDots(
    Canvas canvas,
    Paint paint,
    Offset center,
    double radius,
  ) {
    final dotPaint =
        Paint()
          ..color = paint.color
          ..style = PaintingStyle.fill;

    for (int i = 0; i < 8; i++) {
      final angle = i * math.pi / 4 + math.pi / 8; // إزاحة للزوايا
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);

      canvas.drawCircle(Offset(x, y), 1.5, dotPaint);
    }
  }

  @override
  bool shouldRepaint(IslamicPatternPainter oldDelegate) {
    return oldDelegate.color != color || oldDelegate.opacity != opacity;
  }
}
