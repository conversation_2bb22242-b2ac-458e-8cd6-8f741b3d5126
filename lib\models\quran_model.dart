/// نموذج بيانات للسورة القرآنية
class Surah {
  final int number;
  final String name;
  final String englishName;
  final String englishNameTranslation;
  final int numberOfAyahs;
  final String revelationType;

  Surah({
    required this.number,
    required this.name,
    required this.englishName,
    required this.englishNameTranslation,
    required this.numberOfAyahs,
    required this.revelationType,
  });

  /// إنشاء نموذج من بيانات JSON
  factory Surah.fromJson(Map<String, dynamic> json) {
    return Surah(
      number: json['number'] ?? 0,
      name: json['name'] ?? '',
      englishName: json['englishName'] ?? '',
      englishNameTranslation: json['englishNameTranslation'] ?? '',
      numberOfAyahs: json['numberOfAyahs'] ?? 0,
      revelationType: json['revelationType'] ?? 'Meccan',
    );
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name': name,
      'englishName': englishName,
      'englishNameTranslation': englishNameTranslation,
      'numberOfAyahs': numberOfAyahs,
      'revelationType': revelationType,
    };
  }
}

/// نموذج بيانات للآية القرآنية
class Ayah {
  final int number;
  final String text;
  final int numberInSurah;
  final int juz;
  final int page;
  final int hizbQuarter;
  final bool sajda;

  Ayah({
    required this.number,
    required this.text,
    required this.numberInSurah,
    required this.juz,
    required this.page,
    required this.hizbQuarter,
    required this.sajda,
  });

  /// إنشاء نموذج من بيانات JSON
  factory Ayah.fromJson(Map<String, dynamic> json) {
    return Ayah(
      number: json['number'] ?? 0,
      text: json['text'] ?? '',
      numberInSurah: json['numberInSurah'] ?? 0,
      juz: json['juz'] ?? 0,
      page: json['page'] ?? 0,
      hizbQuarter: json['hizbQuarter'] ?? 0,
      sajda: json['sajda'] is bool ? json['sajda'] : false,
    );
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'text': text,
      'numberInSurah': numberInSurah,
      'juz': juz,
      'page': page,
      'hizbQuarter': hizbQuarter,
      'sajda': sajda,
    };
  }
}

/// نموذج بيانات للقرآن الكريم كاملاً
class QuranData {
  final List<Surah> surahs;

  QuranData({required this.surahs});

  /// إنشاء نموذج من بيانات JSON
  factory QuranData.fromJson(Map<String, dynamic> json) {
    try {
      final data = json['data'];
      if (data == null) {
        return QuranData(surahs: []);
      }

      final List<dynamic> surahsJson = data['surahs'] ?? [];
      final List<Surah> surahs =
          surahsJson.map((surahJson) => Surah.fromJson(surahJson)).toList();
      return QuranData(surahs: surahs);
    } catch (e) {
      // في حالة حدوث خطأ، نعيد قائمة فارغة
      return QuranData(surahs: []);
    }
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'data': {
        'surahs': surahs.map((surah) => surah.toJson()).toList(),
      },
    };
  }
}
