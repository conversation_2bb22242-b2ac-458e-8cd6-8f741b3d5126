import 'dart:async';
import '../models/statistics_model.dart';
import '../services/statistics_database_service.dart';
import '../utils/logger.dart';

/// خدمة تتبع الإحصائيات في الوقت الفعلي
class StatisticsTracker {
  static final StatisticsTracker _instance = StatisticsTracker._internal();
  factory StatisticsTracker() => _instance;
  StatisticsTracker._internal();

  final StatisticsDatabaseService _dbService = StatisticsDatabaseService();

  // جلسة الاستخدام الحالية
  int? _currentSessionId;
  DateTime? _sessionStartTime;
  String _currentActivity = 'general';
  String? _currentCategory;

  // إحصائيات اليوم الحالي
  DailyStatistics? _todayStats;

  // مؤقت لحفظ البيانات دورياً
  Timer? _saveTimer;

  // متغيرات التتبع
  int _appOpenCount = 0;
  int _tasbihCount = 0;
  int _favoritesAdded = 0;
  int _quranReadingTimeMinutes = 0;
  final Map<String, int> _categoryCompletions = {};

  /// تهيئة المتتبع
  Future<void> initialize() async {
    try {
      AppLogger.info('تهيئة متتبع الإحصائيات');

      // تحميل إحصائيات اليوم
      await _loadTodayStats();

      // بدء جلسة استخدام جديدة
      await _startNewSession();

      // بدء مؤقت الحفظ الدوري (كل 30 ثانية)
      _startPeriodicSave();

      AppLogger.info('تم تهيئة متتبع الإحصائيات بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة متتبع الإحصائيات: $e');
    }
  }

  /// تحميل إحصائيات اليوم
  Future<void> _loadTodayStats() async {
    try {
      final today = DateTime.now();
      _todayStats = await _dbService.getDailyStatistics(today);

      if (_todayStats == null) {
        // إنشاء إحصائيات جديدة لليوم
        _todayStats = DailyStatistics.empty(today);
        await _dbService.saveDailyStatistics(_todayStats!);
      }

      // تحميل البيانات المحفوظة
      _appOpenCount = _todayStats!.appOpenCount;
      _tasbihCount = _todayStats!.tasbihCount;
      _favoritesAdded = _todayStats!.favoritesAdded;
      _quranReadingTimeMinutes = _todayStats!.quranReadingTimeMinutes;
      _categoryCompletions.addAll(_todayStats!.categoryCompletions);
    } catch (e) {
      AppLogger.error('خطأ في تحميل إحصائيات اليوم: $e');
      _todayStats = DailyStatistics.empty(DateTime.now());
    }
  }

  /// بدء جلسة استخدام جديدة
  Future<void> _startNewSession() async {
    try {
      _sessionStartTime = DateTime.now();

      final sessionData = {
        'startTime': _sessionStartTime!.toIso8601String(),
        'endTime': null,
        'activityType': _currentActivity,
        'categoryName': _currentCategory,
        'durationMinutes': 0,
        'metadata': '{}',
      };

      _currentSessionId = await _dbService.saveUsageSession(sessionData);

      // تسجيل فتح التطبيق
      await recordAppOpen();

      AppLogger.info('بدء جلسة استخدام جديدة: $_currentSessionId');
    } catch (e) {
      AppLogger.error('خطأ في بدء جلسة الاستخدام: $e');
    }
  }

  /// بدء مؤقت الحفظ الدوري
  void _startPeriodicSave() {
    _saveTimer?.cancel();
    _saveTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _saveCurrentStats();
    });
  }

  /// حفظ الإحصائيات الحالية
  Future<void> _saveCurrentStats() async {
    try {
      if (_todayStats == null) return;

      final updatedStats = _todayStats!.copyWith(
        appOpenCount: _appOpenCount,
        tasbihCount: _tasbihCount,
        favoritesAdded: _favoritesAdded,
        quranReadingTimeMinutes: _quranReadingTimeMinutes,
        categoryCompletions: Map.from(_categoryCompletions),
        updatedAt: DateTime.now(),
      );

      await _dbService.updateDailyStatistics(updatedStats);
      _todayStats = updatedStats;

      AppLogger.debug('تم حفظ الإحصائيات الحالية');
    } catch (e) {
      AppLogger.error('خطأ في حفظ الإحصائيات: $e');
    }
  }

  /// تسجيل فتح التطبيق
  Future<void> recordAppOpen() async {
    try {
      _appOpenCount++;
      AppLogger.info('تسجيل فتح التطبيق: $_appOpenCount');
    } catch (e) {
      AppLogger.error('خطأ في تسجيل فتح التطبيق: $e');
    }
  }

  /// تسجيل استخدام المسبحة
  Future<void> recordTasbihCount(int count) async {
    try {
      _tasbihCount += count;
      AppLogger.info('تسجيل استخدام المسبحة: +$count (المجموع: $_tasbihCount)');
    } catch (e) {
      AppLogger.error('خطأ في تسجيل استخدام المسبحة: $e');
    }
  }

  /// تسجيل إضافة مفضلة
  Future<void> recordFavoriteAdded() async {
    try {
      _favoritesAdded++;
      AppLogger.info('تسجيل إضافة مفضلة: $_favoritesAdded');
    } catch (e) {
      AppLogger.error('خطأ في تسجيل إضافة المفضلة: $e');
    }
  }

  /// تسجيل وقت قراءة القرآن
  Future<void> recordQuranReadingTime(int minutes) async {
    try {
      _quranReadingTimeMinutes += minutes;
      AppLogger.info(
        'تسجيل وقت قراءة القرآن: +$minutes دقيقة (المجموع: $_quranReadingTimeMinutes)',
      );
    } catch (e) {
      AppLogger.error('خطأ في تسجيل وقت قراءة القرآن: $e');
    }
  }

  /// تسجيل إكمال تصنيف
  Future<void> recordCategoryCompletion(String category) async {
    try {
      _categoryCompletions[category] =
          (_categoryCompletions[category] ?? 0) + 1;
      AppLogger.info(
        'تسجيل إكمال تصنيف: $category (المجموع: ${_categoryCompletions[category]})',
      );
    } catch (e) {
      AppLogger.error('خطأ في تسجيل إكمال التصنيف: $e');
    }
  }

  /// تغيير النشاط الحالي
  Future<void> changeActivity(
    String activityType, {
    String? categoryName,
  }) async {
    try {
      // إنهاء الجلسة الحالية
      await _endCurrentSession();

      // بدء جلسة جديدة
      _currentActivity = activityType;
      _currentCategory = categoryName;
      await _startNewSession();

      AppLogger.info(
        'تغيير النشاط إلى: $activityType${categoryName != null ? ' - $categoryName' : ''}',
      );
    } catch (e) {
      AppLogger.error('خطأ في تغيير النشاط: $e');
    }
  }

  /// إنهاء الجلسة الحالية
  Future<void> _endCurrentSession() async {
    try {
      if (_currentSessionId == null || _sessionStartTime == null) return;

      final endTime = DateTime.now();
      final duration = endTime.difference(_sessionStartTime!);

      final sessionData = {
        'endTime': endTime.toIso8601String(),
        'durationMinutes': duration.inMinutes,
      };

      await _dbService.updateUsageSession(_currentSessionId!, sessionData);

      AppLogger.info(
        'إنهاء الجلسة: $_currentSessionId (المدة: ${duration.inMinutes} دقيقة)',
      );
    } catch (e) {
      AppLogger.error('خطأ في إنهاء الجلسة: $e');
    }
  }

  /// الحصول على الإحصائيات الحالية
  DailyStatistics? get currentStats => _todayStats;

  /// الحصول على عدد فتحات التطبيق اليوم
  int get appOpenCount => _appOpenCount;

  /// الحصول على عدد استخدام المسبحة اليوم
  int get tasbihCount => _tasbihCount;

  /// الحصول على عدد المفضلات المضافة اليوم
  int get favoritesAdded => _favoritesAdded;

  /// الحصول على وقت قراءة القرآن اليوم (بالدقائق)
  int get quranReadingTimeMinutes => _quranReadingTimeMinutes;

  /// الحصول على إكمالات التصنيفات
  Map<String, int> get categoryCompletions =>
      Map.unmodifiable(_categoryCompletions);

  /// إنهاء المتتبع وحفظ البيانات
  Future<void> dispose() async {
    try {
      AppLogger.info('إنهاء متتبع الإحصائيات');

      // إيقاف المؤقت
      _saveTimer?.cancel();

      // حفظ البيانات النهائية
      await _saveCurrentStats();

      // إنهاء الجلسة الحالية
      await _endCurrentSession();

      // إغلاق قاعدة البيانات
      await _dbService.close();

      AppLogger.info('تم إنهاء متتبع الإحصائيات بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في إنهاء متتبع الإحصائيات: $e');
    }
  }

  /// فرض حفظ البيانات فوراً
  Future<void> forceSave() async {
    await _saveCurrentStats();
  }

  /// إعادة تعيين الإحصائيات (للاختبار)
  Future<void> resetStats() async {
    try {
      _appOpenCount = 0;
      _tasbihCount = 0;
      _favoritesAdded = 0;
      _quranReadingTimeMinutes = 0;
      _categoryCompletions.clear();

      _todayStats = DailyStatistics.empty(DateTime.now());
      await _dbService.saveDailyStatistics(_todayStats!);

      AppLogger.info('تم إعادة تعيين الإحصائيات');
    } catch (e) {
      AppLogger.error('خطأ في إعادة تعيين الإحصائيات: $e');
    }
  }
}
