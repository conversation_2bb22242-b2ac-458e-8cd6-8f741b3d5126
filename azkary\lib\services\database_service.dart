import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'dart:io' show Directory;
import '../models/azkar_model.dart';
import '../constants/azkar_data.dart';
import '../utils/logger.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  static Database? _database;

  factory DatabaseService() {
    return _instance;
  }

  DatabaseService._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // دالة للتحقق من وجود البيانات وإعادة تهيئتها إذا لزم الأمر
  Future<bool> checkAndReinitializeDatabase() async {
    try {
      AppLogger.info(
        'بدء التحقق من قاعدة البيانات وإعادة تهيئتها إذا لزم الأمر',
      );
      final db = await database;

      // التحقق من وجود التصنيفات
      final categoriesCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM categories'),
      );

      // التحقق من وجود الأذكار
      final azkarCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM azkar'),
      );

      AppLogger.info(
        'فحص قاعدة البيانات - عدد التصنيفات: $categoriesCount، عدد الأذكار: $azkarCount',
      );

      // إذا كانت البيانات فارغة أو غير كاملة، قم بإعادة إدخالها
      if ((categoriesCount == null ||
              categoriesCount < 10 ||
              azkarCount == null ||
              azkarCount < azkarData.length) &&
          azkarData.isNotEmpty) {
        AppLogger.info('جاري إعادة تهيئة قاعدة البيانات بالبيانات الكاملة');

        // حذف البيانات الموجودة (مع الحفاظ على جدول الأذكار الخاصة)
        await db.delete('categories');
        await db.delete('azkar');

        // التحقق من وجود جدول الأذكار الخاصة
        final customTableExists =
            Sqflite.firstIntValue(
              await db.rawQuery(
                "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='custom_azkar'",
              ),
            ) ??
            0;

        // إنشاء جدول الأذكار الخاصة إذا لم يكن موجوداً
        if (customTableExists == 0) {
          AppLogger.info('جدول الأذكار الخاصة غير موجود، جاري إنشاؤه...');
          await db.execute('''
            CREATE TABLE custom_azkar(
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              text TEXT,
              count INTEGER,
              source TEXT,
              fadl TEXT,
              isFavorite INTEGER DEFAULT 0,
              currentCount INTEGER DEFAULT 0,
              createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
          ''');
          AppLogger.info('تم إنشاء جدول الأذكار الخاصة بنجاح');
        }

        // إعادة إدخال البيانات
        AppLogger.info('جاري إدخال البيانات الأولية...');
        await _insertInitialData(db);

        // التحقق من نجاح إعادة التهيئة
        final newCategoriesCount = Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM categories'),
        );
        final newAzkarCount = Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM azkar'),
        );
        AppLogger.info(
          'بعد إعادة التهيئة - عدد التصنيفات: $newCategoriesCount، عدد الأذكار: $newAzkarCount',
        );

        return true;
      }

      return false;
    } catch (e) {
      AppLogger.error('خطأ في التحقق من قاعدة البيانات: $e');
      // في حالة حدوث خطأ، حاول إعادة تهيئة قاعدة البيانات
      try {
        AppLogger.info('جاري إعادة إنشاء قاعدة البيانات من الصفر');

        // حفظ الأذكار الخاصة قبل حذف قاعدة البيانات
        List<Zikr> customAzkar = [];
        try {
          customAzkar = await getCustomAzkar();
          AppLogger.info(
            'تم حفظ ${customAzkar.length} ذكر خاص قبل إعادة إنشاء قاعدة البيانات',
          );
        } catch (e) {
          AppLogger.warning('لم يتم العثور على أذكار خاصة: $e');
        }

        // إعادة إنشاء قاعدة البيانات
        _database = null;
        String path = join(await getDatabasesPath(), 'azkary_database.db');
        await deleteDatabase(path);
        _database = await _initDatabase();

        // استعادة الأذكار الخاصة
        if (customAzkar.isNotEmpty) {
          AppLogger.info('جاري استعادة ${customAzkar.length} ذكر خاص');
          for (var zikr in customAzkar) {
            await addCustomZikr(zikr);
          }
          AppLogger.info('تم استعادة الأذكار الخاصة بنجاح');
        }

        // التحقق من نجاح إعادة التهيئة
        final db = await database;
        final newCategoriesCount = Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM categories'),
        );
        final newAzkarCount = Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM azkar'),
        );
        AppLogger.info(
          'بعد إعادة الإنشاء - عدد التصنيفات: $newCategoriesCount، عدد الأذكار: $newAzkarCount',
        );

        return true;
      } catch (e) {
        AppLogger.error('فشل في إعادة إنشاء قاعدة البيانات: $e');
        return false;
      }
    }
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'azkary_database.db');
    AppLogger.info('مسار قاعدة البيانات: $path');

    // إنشاء المجلدات إذا لم تكن موجودة
    try {
      await Directory(dirname(path)).create(recursive: true);
      AppLogger.info('تم إنشاء المجلدات اللازمة');
    } catch (e) {
      AppLogger.error('خطأ في إنشاء المجلدات: $e');
    }

    AppLogger.info('جاري فتح قاعدة البيانات...');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onOpen: (db) async {
        AppLogger.info('تم فتح قاعدة البيانات بنجاح');

        // التحقق من وجود البيانات عند فتح قاعدة البيانات
        final categoriesCount = Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM categories'),
        );

        AppLogger.info(
          'عدد التصنيفات عند فتح قاعدة البيانات: $categoriesCount',
        );

        // إذا كانت قاعدة البيانات فارغة، قم بإدخال البيانات الأولية
        if (categoriesCount == null || categoriesCount == 0) {
          AppLogger.info(
            'قاعدة البيانات فارغة، جاري إدخال البيانات الأولية...',
          );
          await _insertInitialData(db);
        }
      },
      // إضافة خيارات لتحسين الأداء
      singleInstance: true,
      readOnly: false,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    AppLogger.info('جاري إنشاء قاعدة البيانات...');
    try {
      // Create categories table
      AppLogger.info('جاري إنشاء جدول التصنيفات...');
      await db.execute('''
        CREATE TABLE categories(
          id INTEGER PRIMARY KEY,
          name TEXT,
          icon TEXT,
          count INTEGER,
          description TEXT
        )
      ''');
      AppLogger.info('تم إنشاء جدول التصنيفات بنجاح');

      // Create azkar table
      AppLogger.info('جاري إنشاء جدول الأذكار...');
      await db.execute('''
        CREATE TABLE azkar(
          id INTEGER PRIMARY KEY,
          text TEXT,
          count INTEGER,
          source TEXT,
          category TEXT,
          reference TEXT,
          fadl TEXT,
          isFavorite INTEGER DEFAULT 0,
          currentCount INTEGER DEFAULT 0
        )
      ''');
      AppLogger.info('تم إنشاء جدول الأذكار بنجاح');

      // Create custom azkar table
      AppLogger.info('جاري إنشاء جدول الأذكار الخاصة...');
      await db.execute('''
        CREATE TABLE custom_azkar(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          text TEXT,
          count INTEGER,
          source TEXT,
          fadl TEXT,
          isFavorite INTEGER DEFAULT 0,
          currentCount INTEGER DEFAULT 0,
          createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      ''');
      AppLogger.info('تم إنشاء جدول الأذكار الخاصة بنجاح');

      // Insert initial data
      AppLogger.info('جاري إدخال البيانات الأولية...');
      await _insertInitialData(db);
      AppLogger.info('تم إدخال البيانات الأولية بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في إنشاء قاعدة البيانات: $e');
      // إعادة المحاولة
      try {
        AppLogger.info('جاري إعادة المحاولة لإنشاء قاعدة البيانات...');
        // إنشاء الجداول بشكل منفصل
        await db.execute('DROP TABLE IF EXISTS categories');
        await db.execute('DROP TABLE IF EXISTS azkar');
        await db.execute('DROP TABLE IF EXISTS custom_azkar');

        // إعادة إنشاء الجداول
        await db.execute('''
          CREATE TABLE categories(
            id INTEGER PRIMARY KEY,
            name TEXT,
            icon TEXT,
            count INTEGER,
            description TEXT
          )
        ''');

        await db.execute('''
          CREATE TABLE azkar(
            id INTEGER PRIMARY KEY,
            text TEXT,
            count INTEGER,
            source TEXT,
            category TEXT,
            reference TEXT,
            fadl TEXT,
            isFavorite INTEGER DEFAULT 0,
            currentCount INTEGER DEFAULT 0
          )
        ''');

        await db.execute('''
          CREATE TABLE custom_azkar(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            text TEXT,
            count INTEGER,
            source TEXT,
            fadl TEXT,
            isFavorite INTEGER DEFAULT 0,
            currentCount INTEGER DEFAULT 0,
            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          )
        ''');

        // إدخال البيانات الأولية
        await _insertInitialData(db);
        AppLogger.info('تم إنشاء قاعدة البيانات بنجاح بعد إعادة المحاولة');
      } catch (e2) {
        AppLogger.error(
          'فشل في إنشاء قاعدة البيانات حتى بعد إعادة المحاولة: $e2',
        );
      }
    }
  }

  Future<void> _insertInitialData(Database db) async {
    // استخدام المعاملات الجماعية لتسريع إدخال البيانات
    try {
      AppLogger.info('Starting to insert initial data');
      AppLogger.info('Azkar to insert: ${azkarData.length}');

      // إدخال التصنيفات الافتراضية في معاملة واحدة
      await db.transaction((txn) async {
        Batch batch = txn.batch();

        // إضافة التصنيفات الافتراضية
        batch.insert(
          'categories',
          Category(
            id: 1,
            name: 'أذكار الصباح',
            icon: 'assets/icons/morning.png',
            count: 31,
            description: 'أذكار الصباح',
          ).toMap(),
        );

        batch.insert(
          'categories',
          Category(
            id: 2,
            name: 'أذكار المساء',
            icon: 'assets/icons/evening.png',
            count: 31,
            description: 'أذكار المساء',
          ).toMap(),
        );

        batch.insert(
          'categories',
          Category(
            id: 3,
            name: 'أذكار النوم',
            icon: 'assets/icons/sleep.png',
            count: 15,
            description: 'أذكار النوم',
          ).toMap(),
        );

        batch.insert(
          'categories',
          Category(
            id: 4,
            name: 'أذكار الاستيقاظ',
            icon: 'assets/icons/wakeup.png',
            count: 8,
            description: 'أذكار الاستيقاظ',
          ).toMap(),
        );

        batch.insert(
          'categories',
          Category(
            id: 5,
            name: 'أذكار الصلاة',
            icon: 'assets/icons/prayer.png',
            count: 12,
            description: 'أذكار الصلاة',
          ).toMap(),
        );

        batch.insert(
          'categories',
          Category(
            id: 6,
            name: 'أذكار المسجد',
            icon: 'assets/icons/mosque.png',
            count: 10,
            description: 'أذكار المسجد',
          ).toMap(),
        );

        batch.insert(
          'categories',
          Category(
            id: 7,
            name: 'أذكار الوضوء',
            icon: 'assets/icons/wudu.png',
            count: 6,
            description: 'أذكار الوضوء',
          ).toMap(),
        );

        batch.insert(
          'categories',
          Category(
            id: 8,
            name: 'أذكار الطعام',
            icon: 'assets/icons/food.png',
            count: 8,
            description: 'أذكار الطعام',
          ).toMap(),
        );

        batch.insert(
          'categories',
          Category(
            id: 9,
            name: 'أذكار السفر',
            icon: 'assets/icons/travel.png',
            count: 10,
            description: 'أذكار السفر',
          ).toMap(),
        );

        batch.insert(
          'categories',
          Category(
            id: 10,
            name: 'أذكار متنوعة',
            icon: 'assets/icons/general.png',
            count: 20,
            description: 'أذكار متنوعة',
          ).toMap(),
        );

        await batch.commit(noResult: true);
      });

      // التحقق من إدخال التصنيفات
      final categoriesCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM categories'),
      );
      AppLogger.info('Categories inserted: $categoriesCount');

      // إدخال الأذكار في معاملة واحدة
      await db.transaction((txn) async {
        Batch batch = txn.batch();
        for (var zikr in azkarData) {
          batch.insert('azkar', zikr.toMap());
        }
        await batch.commit(noResult: true);
      });

      // التحقق من إدخال الأذكار
      final azkarCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM azkar'),
      );
      AppLogger.info('Azkar inserted: $azkarCount');

      // التحقق من وجود كل التصنيفات
      final List<Map<String, dynamic>> categoryMaps = await db.query(
        'categories',
      );
      AppLogger.info('Category names in database:');
      for (var map in categoryMaps) {
        AppLogger.info('- ${map['name']}');
      }
    } catch (e) {
      AppLogger.error('Error inserting initial data: $e');
      // إعادة المحاولة بطريقة أبسط في حالة الفشل
      try {
        // إدخال التصنيفات بشكل فردي
        await db.insert(
          'categories',
          Category(
            id: 1,
            name: 'أذكار الصباح',
            icon: 'assets/icons/morning.png',
            count: 31,
            description: 'أذكار الصباح',
          ).toMap(),
        );

        await db.insert(
          'categories',
          Category(
            id: 2,
            name: 'أذكار المساء',
            icon: 'assets/icons/evening.png',
            count: 31,
            description: 'أذكار المساء',
          ).toMap(),
        );

        await db.insert(
          'categories',
          Category(
            id: 3,
            name: 'أذكار النوم',
            icon: 'assets/icons/sleep.png',
            count: 15,
            description: 'أذكار النوم',
          ).toMap(),
        );

        await db.insert(
          'categories',
          Category(
            id: 4,
            name: 'أذكار الاستيقاظ',
            icon: 'assets/icons/wakeup.png',
            count: 8,
            description: 'أذكار الاستيقاظ',
          ).toMap(),
        );

        await db.insert(
          'categories',
          Category(
            id: 5,
            name: 'أذكار الصلاة',
            icon: 'assets/icons/prayer.png',
            count: 12,
            description: 'أذكار الصلاة',
          ).toMap(),
        );

        await db.insert(
          'categories',
          Category(
            id: 6,
            name: 'أذكار المسجد',
            icon: 'assets/icons/mosque.png',
            count: 10,
            description: 'أذكار المسجد',
          ).toMap(),
        );

        await db.insert(
          'categories',
          Category(
            id: 7,
            name: 'أذكار الوضوء',
            icon: 'assets/icons/wudu.png',
            count: 6,
            description: 'أذكار الوضوء',
          ).toMap(),
        );

        await db.insert(
          'categories',
          Category(
            id: 8,
            name: 'أذكار الطعام',
            icon: 'assets/icons/food.png',
            count: 8,
            description: 'أذكار الطعام',
          ).toMap(),
        );

        await db.insert(
          'categories',
          Category(
            id: 9,
            name: 'أذكار السفر',
            icon: 'assets/icons/travel.png',
            count: 10,
            description: 'أذكار السفر',
          ).toMap(),
        );

        await db.insert(
          'categories',
          Category(
            id: 10,
            name: 'أذكار متنوعة',
            icon: 'assets/icons/general.png',
            count: 20,
            description: 'أذكار متنوعة',
          ).toMap(),
        );

        // إدخال الأذكار بشكل فردي
        for (var zikr in azkarData) {
          await db.insert('azkar', zikr.toMap());
        }

        AppLogger.info('Inserted data using individual inserts');
      } catch (e) {
        AppLogger.error(
          'Failed to insert data even with individual inserts: $e',
        );
      }
    }
  }

  // Get all categories
  Future<List<Category>> getCategories() async {
    try {
      AppLogger.info('بدء استرجاع التصنيفات من قاعدة البيانات');
      final db = await database;

      // التحقق من وجود جدول التصنيفات
      final tableExists =
          Sqflite.firstIntValue(
            await db.rawQuery(
              "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='categories'",
            ),
          ) ??
          0;

      AppLogger.info(
        'هل جدول التصنيفات موجود؟ ${tableExists > 0 ? 'نعم' : 'لا'}',
      );

      if (tableExists == 0) {
        AppLogger.warning(
          'جدول التصنيفات غير موجود، جاري إعادة تهيئة قاعدة البيانات',
        );
        await checkAndReinitializeDatabase();
      }

      final List<Map<String, dynamic>> maps = await db.query('categories');
      AppLogger.info('تم استرجاع ${maps.length} تصنيف من قاعدة البيانات');

      if (maps.isEmpty) {
        AppLogger.warning(
          'لا توجد تصنيفات في قاعدة البيانات، جاري إعادة التهيئة',
        );
        await checkAndReinitializeDatabase();
        // Try again after reinitialization
        final newMaps = await db.query('categories');
        maps.addAll(newMaps);
        AppLogger.info('بعد إعادة التهيئة: ${maps.length} تصنيف');
      }

      // Log category names for debugging
      if (maps.isNotEmpty) {
        AppLogger.info('أسماء التصنيفات المسترجعة:');
        for (var map in maps) {
          AppLogger.info('- ${map['name']} (ID: ${map['id']})');
        }
      }

      return List.generate(maps.length, (i) {
        return Category.fromMap(maps[i]);
      });
    } catch (e) {
      AppLogger.error('Error getting categories: $e');
      // Return empty list in case of error
      return [];
    }
  }

  // Get azkar by category
  Future<List<Zikr>> getAzkarByCategory(String category) async {
    try {
      AppLogger.info('Getting azkar for category: $category');
      final db = await database;

      // التحقق من وجود جدول الأذكار
      final tableExists =
          Sqflite.firstIntValue(
            await db.rawQuery(
              "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='azkar'",
            ),
          ) ??
          0;

      if (tableExists == 0) {
        AppLogger.warning(
          'Azkar table does not exist, reinitializing database',
        );
        await checkAndReinitializeDatabase();
      }

      final List<Map<String, dynamic>> maps = await db.query(
        'azkar',
        where: 'category = ?',
        whereArgs: [category],
      );

      AppLogger.info('Retrieved ${maps.length} azkar for category: $category');

      if (maps.isEmpty) {
        AppLogger.warning(
          'No azkar found for category: $category, checking if data needs to be reinitialized',
        );

        // التحقق من وجود أذكار في هذا التصنيف في البيانات المحلية
        final localAzkar =
            azkarData.where((zikr) => zikr.category == category).toList();

        if (localAzkar.isNotEmpty) {
          AppLogger.info(
            'Found ${localAzkar.length} azkar in local data for category: $category',
          );
          AppLogger.info('Reinitializing database to include these azkar');
          await checkAndReinitializeDatabase();

          // محاولة الحصول على الأذكار مرة أخرى بعد إعادة التهيئة
          final updatedMaps = await db.query(
            'azkar',
            where: 'category = ?',
            whereArgs: [category],
          );

          AppLogger.info(
            'After reinitialization: ${updatedMaps.length} azkar found for category: $category',
          );

          if (updatedMaps.isNotEmpty) {
            return List.generate(updatedMaps.length, (i) {
              return Zikr.fromMap(updatedMaps[i]);
            });
          }

          // إذا فشلت إعادة التهيئة، استخدم البيانات المحلية مباشرة
          AppLogger.info('Using local data directly as fallback');
          return localAzkar;
        }
      }

      return List.generate(maps.length, (i) {
        return Zikr.fromMap(maps[i]);
      });
    } catch (e) {
      AppLogger.error('Error getting azkar by category: $e');

      // استخدام البيانات المحلية كحل احتياطي
      AppLogger.warning('Using local data as fallback due to error');
      return azkarData.where((zikr) => zikr.category == category).toList();
    }
  }

  // Get favorite azkar
  Future<List<Zikr>> getFavoriteAzkar() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'azkar',
      where: 'isFavorite = ?',
      whereArgs: [1],
    );
    return List.generate(maps.length, (i) {
      return Zikr.fromMap(maps[i]);
    });
  }

  // Toggle favorite
  Future<void> toggleFavorite(int id, bool isFavorite) async {
    final db = await database;
    await db.update(
      'azkar',
      {'isFavorite': isFavorite ? 1 : 0},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Update current count
  Future<void> updateCurrentCount(int id, int currentCount) async {
    final db = await database;
    await db.update(
      'azkar',
      {'currentCount': currentCount},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Reset all current counts
  Future<void> resetAllCurrentCounts() async {
    final db = await database;
    await db.update('azkar', {'currentCount': 0});
  }

  // Search azkar
  Future<List<Zikr>> searchAzkar(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'azkar',
      where: 'text LIKE ? OR source LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
    );
    return List.generate(maps.length, (i) {
      return Zikr.fromMap(maps[i]);
    });
  }

  // Get random zikr for daily zikr
  Future<Zikr> getRandomZikr() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      'SELECT * FROM azkar ORDER BY RANDOM() LIMIT 1',
    );
    return Zikr.fromMap(maps.first);
  }

  // ===== دوال الأذكار الخاصة =====

  // إضافة ذكر خاص جديد
  Future<int> addCustomZikr(Zikr zikr) async {
    try {
      AppLogger.info('DatabaseService: بدء إضافة ذكر خاص جديد: ${zikr.text}');

      // الحصول على قاعدة البيانات
      final db = await database;
      AppLogger.info('DatabaseService: تم الحصول على قاعدة البيانات بنجاح');

      // التحقق من وجود جدول الأذكار الخاصة
      final tableExists =
          Sqflite.firstIntValue(
            await db.rawQuery(
              "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='custom_azkar'",
            ),
          ) ??
          0;
      AppLogger.info(
        'DatabaseService: نتيجة التحقق من وجود جدول الأذكار الخاصة: $tableExists',
      );

      // إنشاء جدول الأذكار الخاصة إذا لم يكن موجوداً
      if (tableExists == 0) {
        AppLogger.warning(
          'DatabaseService: جدول الأذكار الخاصة غير موجود، جاري إنشاؤه...',
        );
        try {
          await db.execute('''
            CREATE TABLE custom_azkar(
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              text TEXT,
              count INTEGER,
              source TEXT,
              fadl TEXT,
              isFavorite INTEGER DEFAULT 0,
              currentCount INTEGER DEFAULT 0,
              createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
          ''');
          AppLogger.info('DatabaseService: تم إنشاء جدول الأذكار الخاصة بنجاح');
        } catch (e) {
          AppLogger.error(
            'DatabaseService: خطأ في إنشاء جدول الأذكار الخاصة: $e',
          );
          throw Exception('فشل في إنشاء جدول الأذكار الخاصة: $e');
        }
      }

      // إعداد بيانات الذكر للإدخال
      final Map<String, dynamic> zikrMap = {
        'text': zikr.text,
        'count': zikr.count,
        'source': zikr.source,
        'fadl': zikr.fadl,
        'isFavorite': zikr.isFavorite ? 1 : 0,
        'currentCount': zikr.currentCount,
      };
      AppLogger.info(
        'DatabaseService: تم إعداد بيانات الذكر للإدخال: $zikrMap',
      );

      // محاولة إدخال الذكر
      AppLogger.info('DatabaseService: جاري إضافة الذكر إلى قاعدة البيانات...');
      int id;
      try {
        id = await db.insert('custom_azkar', zikrMap);
        AppLogger.info('DatabaseService: تم إضافة الذكر بنجاح، المعرف: $id');
      } catch (e) {
        AppLogger.error('DatabaseService: خطأ في إدخال الذكر: $e');

        // محاولة إعادة إنشاء الجدول وإدخال الذكر مرة أخرى
        AppLogger.warning('DatabaseService: محاولة إعادة إنشاء الجدول...');
        try {
          await db.execute('DROP TABLE IF EXISTS custom_azkar');
          await db.execute('''
            CREATE TABLE custom_azkar(
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              text TEXT,
              count INTEGER,
              source TEXT,
              fadl TEXT,
              isFavorite INTEGER DEFAULT 0,
              currentCount INTEGER DEFAULT 0,
              createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
          ''');
          AppLogger.info(
            'DatabaseService: تم إعادة إنشاء الجدول بنجاح، جاري إعادة المحاولة...',
          );
          id = await db.insert('custom_azkar', zikrMap);
          AppLogger.info(
            'DatabaseService: تم إضافة الذكر بنجاح بعد إعادة إنشاء الجدول، المعرف: $id',
          );
        } catch (e2) {
          AppLogger.error('DatabaseService: فشل في إعادة المحاولة: $e2');
          throw Exception('فشل في إضافة الذكر حتى بعد إعادة إنشاء الجدول: $e2');
        }
      }

      return id;
    } catch (e) {
      AppLogger.error('DatabaseService: خطأ عام في إضافة الذكر الخاص: $e');
      return -1;
    }
  }

  // الحصول على جميع الأذكار الخاصة
  Future<List<Zikr>> getCustomAzkar() async {
    try {
      AppLogger.info('DatabaseService: بدء الحصول على الأذكار الخاصة');

      // الحصول على قاعدة البيانات
      final db = await database;
      AppLogger.info('DatabaseService: تم الحصول على قاعدة البيانات بنجاح');

      // التحقق من وجود جدول الأذكار الخاصة
      final tableExists =
          Sqflite.firstIntValue(
            await db.rawQuery(
              "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='custom_azkar'",
            ),
          ) ??
          0;
      AppLogger.info(
        'DatabaseService: نتيجة التحقق من وجود جدول الأذكار الخاصة: $tableExists',
      );

      // إذا لم يكن الجدول موجوداً، قم بإنشائه
      if (tableExists == 0) {
        AppLogger.warning(
          'DatabaseService: جدول الأذكار الخاصة غير موجود، جاري إنشاؤه...',
        );
        try {
          await db.execute('''
            CREATE TABLE custom_azkar(
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              text TEXT,
              count INTEGER,
              source TEXT,
              fadl TEXT,
              isFavorite INTEGER DEFAULT 0,
              currentCount INTEGER DEFAULT 0,
              createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
          ''');
          AppLogger.info('DatabaseService: تم إنشاء جدول الأذكار الخاصة بنجاح');
        } catch (e) {
          AppLogger.error(
            'DatabaseService: خطأ في إنشاء جدول الأذكار الخاصة: $e',
          );
          // حتى لو فشل إنشاء الجدول، نستمر ونعيد قائمة فارغة
        }
        return []; // إرجاع قائمة فارغة لأن الجدول جديد
      }

      // الحصول على الأذكار الخاصة
      AppLogger.info('DatabaseService: جاري الاستعلام عن الأذكار الخاصة...');
      List<Map<String, dynamic>> maps = [];
      try {
        maps = await db.query('custom_azkar', orderBy: 'createdAt DESC');
        AppLogger.info('DatabaseService: تم العثور على ${maps.length} ذكر خاص');
      } catch (e) {
        AppLogger.error(
          'DatabaseService: خطأ في الاستعلام عن الأذكار الخاصة: $e',
        );

        // محاولة إعادة إنشاء الجدول
        AppLogger.warning('DatabaseService: محاولة إعادة إنشاء الجدول...');
        try {
          await db.execute('DROP TABLE IF EXISTS custom_azkar');
          await db.execute('''
            CREATE TABLE custom_azkar(
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              text TEXT,
              count INTEGER,
              source TEXT,
              fadl TEXT,
              isFavorite INTEGER DEFAULT 0,
              currentCount INTEGER DEFAULT 0,
              createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
          ''');
          AppLogger.info('DatabaseService: تم إعادة إنشاء الجدول بنجاح');
          return []; // إرجاع قائمة فارغة بعد إعادة إنشاء الجدول
        } catch (e2) {
          AppLogger.error('DatabaseService: فشل في إعادة إنشاء الجدول: $e2');
          return []; // إرجاع قائمة فارغة في حالة الفشل
        }
      }

      // تحويل البيانات إلى كائنات Zikr
      AppLogger.info('DatabaseService: جاري تحويل البيانات إلى كائنات Zikr...');
      try {
        final result = List.generate(maps.length, (i) {
          final map = maps[i];
          return Zikr(
            id: map['id'],
            text: map['text'],
            count: map['count'],
            source: map['source'],
            category: 'أذكاري الخاصة', // تصنيف ثابت للأذكار الخاصة
            fadl: map['fadl'],
            isFavorite: map['isFavorite'] == 1,
            currentCount: map['currentCount'] ?? 0,
          );
        });
        AppLogger.info(
          'DatabaseService: تم تحويل البيانات بنجاح، عدد الأذكار: ${result.length}',
        );
        return result;
      } catch (e) {
        AppLogger.error('DatabaseService: خطأ في تحويل البيانات: $e');
        return []; // إرجاع قائمة فارغة في حالة الفشل
      }
    } catch (e) {
      AppLogger.error(
        'DatabaseService: خطأ عام في الحصول على الأذكار الخاصة: $e',
      );
      return [];
    }
  }

  // تحديث ذكر خاص
  Future<int> updateCustomZikr(Zikr zikr) async {
    try {
      final db = await database;
      final Map<String, dynamic> zikrMap = {
        'text': zikr.text,
        'count': zikr.count,
        'source': zikr.source,
        'fadl': zikr.fadl,
        'isFavorite': zikr.isFavorite ? 1 : 0,
      };

      return await db.update(
        'custom_azkar',
        zikrMap,
        where: 'id = ?',
        whereArgs: [zikr.id],
      );
    } catch (e) {
      // خطأ في تحديث الذكر الخاص
      return -1;
    }
  }

  // حذف ذكر خاص
  Future<int> deleteCustomZikr(int id) async {
    try {
      final db = await database;
      return await db.delete('custom_azkar', where: 'id = ?', whereArgs: [id]);
    } catch (e) {
      // خطأ في حذف الذكر الخاص
      return -1;
    }
  }

  // تحديث عداد الذكر الخاص
  Future<void> updateCustomZikrCount(int id, int currentCount) async {
    try {
      final db = await database;
      await db.update(
        'custom_azkar',
        {'currentCount': currentCount},
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      // خطأ في تحديث عداد الذكر الخاص
    }
  }

  // تبديل حالة المفضلة للذكر الخاص
  Future<void> toggleCustomZikrFavorite(int id, bool isFavorite) async {
    try {
      final db = await database;
      await db.update(
        'custom_azkar',
        {'isFavorite': isFavorite ? 1 : 0},
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      // خطأ في تبديل حالة المفضلة للذكر الخاص
    }
  }

  // إضافة تصنيف جديد
  Future<int> addCategory(Category category) async {
    try {
      AppLogger.info(
        'بدء إضافة تصنيف جديد إلى قاعدة البيانات: ${category.toMap()}',
      );

      // التحقق من صحة بيانات التصنيف
      if (category.name.trim().isEmpty) {
        AppLogger.warning('اسم التصنيف فارغ');
        return -3; // رمز خاص للإشارة إلى أن اسم التصنيف فارغ
      }

      // التحقق من صحة الأيقونة
      if (category.icon.trim().isEmpty) {
        AppLogger.warning('أيقونة التصنيف فارغة');
        // استخدام أيقونة افتراضية
        category = category.copyWith(icon: 'assets/icons/category.png');
        AppLogger.info('تم استخدام أيقونة افتراضية: ${category.icon}');
      }

      // التأكد من أن مسار الأيقونة صحيح
      String iconPath = category.icon;
      if (iconPath.isEmpty) {
        // استخدام أيقونة افتراضية إذا كانت فارغة
        iconPath = 'assets/icons/category.png';
        category = category.copyWith(icon: iconPath);
        AppLogger.info('تم استخدام أيقونة افتراضية: $iconPath');
      } else if (!iconPath.startsWith('assets/')) {
        // إذا كان الاسم بدون المسار الكامل، نضيف المسار
        if (!iconPath.endsWith('.png')) {
          iconPath = 'assets/icons/$iconPath.png';
        } else {
          iconPath = 'assets/icons/$iconPath';
        }
        category = category.copyWith(icon: iconPath);
        AppLogger.info('تم تصحيح مسار الأيقونة: $iconPath');
      }

      final db = await database;

      // التحقق من عدم وجود تصنيف بنفس الاسم
      final existingCategory = await db.query(
        'categories',
        where: 'name = ?',
        whereArgs: [category.name.trim()],
      );

      if (existingCategory.isNotEmpty) {
        AppLogger.warning('يوجد تصنيف بنفس الاسم: ${category.name}');
        return -2; // رمز خاص للإشارة إلى وجود تصنيف بنفس الاسم
      }

      // الحصول على أعلى معرف موجود
      final maxIdResult = await db.rawQuery(
        'SELECT MAX(id) as max_id FROM categories',
      );
      final maxId = Sqflite.firstIntValue(maxIdResult) ?? 10;
      AppLogger.info('أعلى معرف موجود: $maxId');

      // إنشاء تصنيف جديد بمعرف جديد
      final newCategory = category.copyWith(
        id: maxId + 1,
        name: category.name.trim(),
        description:
            category.description.trim().isEmpty
                ? category.name.trim()
                : category.description.trim(),
      );
      AppLogger.info('تم إنشاء تصنيف جديد بمعرف: ${newCategory.id}');

      // طباعة بيانات التصنيف الجديد
      final categoryMap = newCategory.toMap();
      AppLogger.info('بيانات التصنيف الجديد: $categoryMap');

      // التحقق من هيكل جدول التصنيفات
      try {
        final tableInfo = await db.rawQuery("PRAGMA table_info(categories)");
        AppLogger.info('معلومات جدول التصنيفات: $tableInfo');
      } catch (e) {
        AppLogger.error('خطأ في الحصول على معلومات جدول التصنيفات: $e');
      }

      try {
        // إدخال التصنيف الجديد
        final id = await db.insert(
          'categories',
          categoryMap,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
        AppLogger.info('تم إضافة تصنيف جديد بمعرف: $id');

        // التحقق من إضافة التصنيف بنجاح
        final checkResult = await db.query(
          'categories',
          where: 'id = ?',
          whereArgs: [newCategory.id],
        );

        if (checkResult.isNotEmpty) {
          AppLogger.info(
            'تم التحقق من إضافة التصنيف بنجاح: ${checkResult.first}',
          );
          return newCategory.id; // إرجاع معرف التصنيف الجديد
        } else {
          AppLogger.warning('لم يتم العثور على التصنيف بعد إضافته!');

          // محاولة البحث باستخدام الاسم بدلاً من المعرف
          final checkByName = await db.query(
            'categories',
            where: 'name = ?',
            whereArgs: [newCategory.name],
          );

          if (checkByName.isNotEmpty) {
            AppLogger.info(
              'تم العثور على التصنيف باستخدام الاسم: ${checkByName.first}',
            );
            return int.parse(checkByName.first['id'].toString());
          }

          return -1;
        }
      } catch (insertError) {
        AppLogger.error('خطأ في إدخال التصنيف الجديد: $insertError');

        // محاولة إدخال التصنيف باستخدام طريقة بديلة
        try {
          final id = await db.rawInsert(
            'INSERT INTO categories (id, name, icon, count, description) VALUES (?, ?, ?, ?, ?)',
            [
              newCategory.id,
              newCategory.name,
              newCategory.icon,
              newCategory.count,
              newCategory.description,
            ],
          );
          AppLogger.info(
            'تم إضافة تصنيف جديد باستخدام الطريقة البديلة بمعرف: $id',
          );
          return newCategory.id;
        } catch (rawInsertError) {
          AppLogger.error(
            'خطأ في إدخال التصنيف باستخدام الطريقة البديلة: $rawInsertError',
          );
          return -1;
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في إضافة تصنيف جديد: $e');
      return -1;
    }
  }

  // حذف تصنيف
  Future<bool> deleteCategory(int id) async {
    try {
      final db = await database;

      // حذف التصنيف
      final result = await db.delete(
        'categories',
        where: 'id = ?',
        whereArgs: [id],
      );

      // حذف الأذكار المرتبطة بالتصنيف
      final categoryName = await getCategoryName(id);
      if (categoryName != null) {
        await db.delete(
          'azkar',
          where: 'category = ?',
          whereArgs: [categoryName],
        );
      }

      return result > 0;
    } catch (e) {
      AppLogger.error('خطأ في حذف التصنيف: $e');
      return false;
    }
  }

  // الحصول على اسم التصنيف بواسطة المعرف
  Future<String?> getCategoryName(int id) async {
    try {
      final db = await database;
      final result = await db.query(
        'categories',
        columns: ['name'],
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isNotEmpty) {
        return result.first['name'] as String;
      }
      return null;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على اسم التصنيف: $e');
      return null;
    }
  }

  // إضافة ذكر إلى تصنيف محدد
  Future<int> addZikrToCategory(Zikr zikr, String categoryName) async {
    try {
      final db = await database;

      // الحصول على أعلى معرف موجود
      final maxIdResult = await db.rawQuery(
        'SELECT MAX(id) as max_id FROM azkar',
      );
      final maxId = Sqflite.firstIntValue(maxIdResult) ?? 1000;

      // إنشاء ذكر جديد بمعرف جديد وتصنيف محدد
      final newZikr = zikr.copyWith(id: maxId + 1, category: categoryName);

      // إدخال الذكر الجديد
      final id = await db.insert('azkar', newZikr.toMap());

      // تحديث عدد الأذكار في التصنيف
      await _updateCategoryCount(categoryName);

      AppLogger.info('تم إضافة ذكر جديد بمعرف: $id إلى التصنيف: $categoryName');
      return id;
    } catch (e) {
      AppLogger.error('خطأ في إضافة ذكر إلى تصنيف: $e');
      return -1;
    }
  }

  // تحديث عدد الأذكار في تصنيف
  Future<void> _updateCategoryCount(String categoryName) async {
    try {
      final db = await database;

      // الحصول على عدد الأذكار في التصنيف
      final countResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM azkar WHERE category = ?',
        [categoryName],
      );
      final count = Sqflite.firstIntValue(countResult) ?? 0;

      // تحديث عدد الأذكار في التصنيف
      await db.update(
        'categories',
        {'count': count},
        where: 'name = ?',
        whereArgs: [categoryName],
      );

      AppLogger.info(
        'تم تحديث عدد الأذكار في التصنيف $categoryName إلى $count',
      );
    } catch (e) {
      AppLogger.error('خطأ في تحديث عدد الأذكار في التصنيف: $e');
    }
  }
}
