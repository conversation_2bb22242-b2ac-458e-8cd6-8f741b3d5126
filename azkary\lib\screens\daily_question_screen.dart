import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../services/daily_question_service.dart';
import '../models/daily_question.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/islamic_background.dart';
import '../utils/page_transitions.dart';
import '../utils/color_extensions.dart';

class DailyQuestionScreen extends StatefulWidget {
  const DailyQuestionScreen({super.key});

  @override
  State<DailyQuestionScreen> createState() => _DailyQuestionScreenState();
}

class _DailyQuestionScreenState extends State<DailyQuestionScreen>
    with TickerProviderStateMixin {
  int? _selectedAnswerIndex;
  bool _showResult = false;
  bool _isAnswered = false;
  Timer? _timer;
  int _timeSpent = 0;
  late AnimationController _pulseController;
  late AnimationController _resultController;
  Timer? _countdownTimer;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..repeat(reverse: true);

    _resultController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _countdownTimer?.cancel();
    _pulseController.dispose();
    _resultController.dispose();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isAnswered) {
        setState(() {
          _timeSpent++;
        });
      }
    });
  }

  void _selectAnswer(int index) {
    // التحقق من إجابة السؤال الحالي
    final questionService = context.read<DailyQuestionService>();
    final currentQuestion = questionService.currentQuestion;
    if (currentQuestion != null &&
        questionService.isQuestionAnsweredToday(currentQuestion.id)) {
      return; // السؤال تم الإجابة عليه بالفعل
    }

    setState(() {
      _selectedAnswerIndex = index;
    });
  }

  Future<void> _submitAnswer() async {
    if (_selectedAnswerIndex == null) return;

    final questionService = context.read<DailyQuestionService>();
    final currentQuestion = questionService.currentQuestion;

    // التحقق من إجابة السؤال الحالي
    if (currentQuestion != null &&
        questionService.isQuestionAnsweredToday(currentQuestion.id)) {
      return; // السؤال تم الإجابة عليه بالفعل
    }

    setState(() {
      _isAnswered = true;
    });

    final isCorrect = await questionService.answerQuestion(
      _selectedAnswerIndex!,
      _timeSpent,
    );

    setState(() {
      _showResult = true;
    });

    _resultController.forward();

    // إظهار النتيجة مع تأثير صوتي (اختياري)
    if (isCorrect) {
      _showSuccessDialog();
    } else {
      _showFailureDialog();
    }

    // إذا كان هذا آخر سؤال، إظهار رسالة الإكمال
    if (questionService.hasCompletedAllToday) {
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'تهانينا! تم إكمال جميع أسئلة اليوم! 🎊',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }
      });
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AnimatedWidgets.fadeInCard(
            index: 0,
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppColors.greenWithOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 60,
                    ),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'أحسنت! 🎉',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 10),
                  const Text('إجابة صحيحة', style: TextStyle(fontSize: 16)),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('متابعة'),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  void _showFailureDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AnimatedWidgets.fadeInCard(
            index: 0,
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppColors.orangeWithOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.lightbulb,
                      color: Colors.orange,
                      size: 60,
                    ),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'لا بأس! 💪',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 10),
                  const Text(
                    'تعلمنا شيئاً جديداً',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('متابعة'),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'الأسئلة اليومية',
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.primaryWithOpacity(
                theme.colorScheme.primary,
                0.1,
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.timer, size: 16, color: theme.colorScheme.primary),
                const SizedBox(width: 4),
                Text(
                  _formatTime(_timeSpent),
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: IslamicBackground(
        opacity: theme.brightness == Brightness.dark ? 0.02 : 0.03,
        showPattern: true,
        showGradient: false,
        child: Selector<
          DailyQuestionService,
          ({
            bool isLoading,
            bool hasCompleted,
            DailyQuestion? currentQuestion,
            int currentIndex,
            int totalQuestions,
            int answeredToday,
          })
        >(
          selector:
              (context, service) => (
                isLoading: service.isLoading,
                hasCompleted: service.hasCompletedAllToday,
                currentQuestion: service.currentQuestion,
                currentIndex: service.currentQuestionIndex,
                totalQuestions: service.totalDailyQuestions,
                answeredToday: service.answeredQuestionsToday,
              ),
          builder: (context, data, child) {
            if (data.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (data.hasCompleted && !_showResult) {
              return _buildCompletedAllView(data);
            }

            final question = data.currentQuestion;
            if (question == null) {
              return _buildNoQuestionView();
            }

            return Column(
              children: [
                // مؤشر التقدم
                _buildProgressIndicator(data),
                // محتوى السؤال
                Expanded(child: _buildQuestionView(question, theme)),
                // أزرار التنقل
                if (_showResult) _buildNavigationButtons(data),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildQuestionView(DailyQuestion question, ThemeData theme) {
    final questionService = context.read<DailyQuestionService>();
    final isQuestionAnswered = questionService.isQuestionAnsweredToday(
      question.id,
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // عنوان السؤال
          AnimatedWidgets.fadeInCard(
            index: 0,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color:
                      theme.brightness == Brightness.dark
                          ? AppColors.greyWithOpacity(0.3)
                          : AppColors.greyWithOpacity(0.2),
                ),
                boxShadow: [
                  BoxShadow(
                    color:
                        theme.brightness == Brightness.dark
                            ? AppColors.blackWithOpacity(0.3)
                            : AppColors.blackWithOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primaryWithOpacity(
                            theme.colorScheme.primary,
                            0.1,
                          ),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          question.category,
                          style: TextStyle(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getDifficultyColor(
                            question.difficulty,
                          ).withOpacityValue(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          question.difficultyText,
                          style: TextStyle(
                            color: _getDifficultyColor(question.difficulty),
                            fontWeight: FontWeight.bold,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Text(
                    question.question,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // خيارات الإجابة
          ...question.options.asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            final isSelected = _selectedAnswerIndex == index;
            final isCorrect = index == question.correctAnswerIndex;
            final isWrong = _showResult && isSelected && !isCorrect;

            return AnimatedWidgets.fadeInCard(
              index: index + 1,
              child: Container(
                margin: const EdgeInsets.only(bottom: 12),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () => _selectAnswer(index),
                    borderRadius: BorderRadius.circular(12),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: _getOptionColor(
                          isSelected,
                          isCorrect,
                          isWrong,
                          theme,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _getOptionBorderColor(
                            isSelected,
                            isCorrect,
                            isWrong,
                            theme,
                          ),
                          width: 2,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _getOptionIconColor(
                                isSelected,
                                isCorrect,
                                isWrong,
                                theme,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                String.fromCharCode(65 + index), // A, B, C, D
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              option,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight:
                                    isSelected
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                              ),
                            ),
                          ),
                          if (_showResult && isCorrect)
                            const Icon(Icons.check_circle, color: Colors.green),
                          if (isWrong)
                            const Icon(Icons.cancel, color: Colors.red),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          }),

          const SizedBox(height: 20),

          // زر الإرسال أو رسالة السؤال المجاب
          if (!_showResult && !isQuestionAnswered)
            AnimatedWidgets.fadeInCard(
              index: question.options.length + 1,
              child: AnimatedBuilder(
                animation: _pulseController,
                builder: (context, child) {
                  return Transform.scale(
                    scale:
                        _selectedAnswerIndex != null
                            ? 1.0 + (_pulseController.value * 0.05)
                            : 1.0,
                    child: ElevatedButton(
                      onPressed:
                          _selectedAnswerIndex != null ? _submitAnswer : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: _selectedAnswerIndex != null ? 4 : 0,
                      ),
                      child: const Text(
                        'إرسال الإجابة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

          // رسالة السؤال المجاب
          if (isQuestionAnswered && !_showResult)
            AnimatedWidgets.fadeInCard(
              index: question.options.length + 1,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.greenWithOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.greenWithOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.check_circle, color: Colors.green),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'تم الإجابة على هذا السؤال بالفعل',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.green,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // التفسير
          if (_showResult)
            AnimatedWidgets.fadeInCard(
              index: question.options.length + 2,
              child: Container(
                margin: const EdgeInsets.only(top: 20),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.primaryWithOpacity(
                    theme.colorScheme.primary,
                    0.05,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: AppColors.primaryWithOpacity(
                      theme.colorScheme.primary,
                      0.2,
                    ),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.lightbulb, color: theme.colorScheme.primary),
                        const SizedBox(width: 8),
                        Text(
                          'التفسير',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      question.explanation,
                      style: const TextStyle(fontSize: 16, height: 1.5),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'المصدر: ${question.source}',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.primaryWithOpacity(
                          theme.colorScheme.onSurface,
                          0.6,
                        ),
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// مؤشر التقدم
  Widget _buildProgressIndicator(
    ({
      bool isLoading,
      bool hasCompleted,
      DailyQuestion? currentQuestion,
      int currentIndex,
      int totalQuestions,
      int answeredToday,
    })
    data,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'السؤال ${data.currentIndex + 1} من ${data.totalQuestions}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${data.answeredToday}/${data.totalQuestions} مكتمل',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (data.currentIndex + 1) / data.totalQuestions,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  /// أزرار التنقل
  Widget _buildNavigationButtons(
    ({
      bool isLoading,
      bool hasCompleted,
      DailyQuestion? currentQuestion,
      int currentIndex,
      int totalQuestions,
      int answeredToday,
    })
    data,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          if (data.currentIndex > 0)
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  context.read<DailyQuestionService>().previousQuestion();
                  setState(() {
                    _showResult = false;
                    _selectedAnswerIndex = null;
                    _isAnswered = false;
                  });
                },
                icon: const Icon(Icons.arrow_back),
                label: const Text('السابق'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[600],
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          if (data.currentIndex > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              onPressed:
                  data.currentIndex < data.totalQuestions - 1
                      ? () {
                        context.read<DailyQuestionService>().nextQuestion();
                        setState(() {
                          _showResult = false;
                          _selectedAnswerIndex = null;
                          _isAnswered = false;
                        });
                      }
                      : () => Navigator.of(context).pop(),
              icon: Icon(
                data.currentIndex < data.totalQuestions - 1
                    ? Icons.arrow_forward
                    : Icons.home,
              ),
              label: Text(
                data.currentIndex < data.totalQuestions - 1
                    ? 'التالي'
                    : 'الرئيسية',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompletedAllView(
    ({
      bool isLoading,
      bool hasCompleted,
      DailyQuestion? currentQuestion,
      int currentIndex,
      int totalQuestions,
      int answeredToday,
    })
    data,
  ) {
    return Center(
      child: AnimatedWidgets.fadeInCard(
        index: 0,
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(30),
                decoration: BoxDecoration(
                  color: AppColors.greenWithOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 80,
                ),
              ),
              const SizedBox(height: 30),
              const Text(
                'انتهت أسئلة اليوم! 🎉',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'أجبت على ${data.answeredToday} من ${data.totalQuestions} أسئلة',
                style: const TextStyle(fontSize: 16, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              // العد التنازلي
              Consumer<DailyQuestionService>(
                builder: (context, questionService, child) {
                  // بدء المؤقت إذا لم يكن موجوداً
                  _countdownTimer ??= Timer.periodic(
                    const Duration(seconds: 1),
                    (timer) {
                      if (mounted) {
                        setState(() {
                          // تحديث العد التنازلي كل ثانية
                        });
                      }
                    },
                  );

                  final timeUntilMidnight =
                      questionService.getTimeUntilMidnight();
                  final hours = timeUntilMidnight.inHours;
                  final minutes = timeUntilMidnight.inMinutes % 60;

                  return Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.primaryWithOpacity(
                        Theme.of(context).colorScheme.primary,
                        0.1,
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.primaryWithOpacity(
                          Theme.of(context).colorScheme.primary,
                          0.3,
                        ),
                      ),
                    ),
                    child: Column(
                      children: [
                        const Text(
                          'عد بعد',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '$hours ساعة و $minutes دقيقة',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'لأسئلة جديدة',
                          style: TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                      ],
                    ),
                  );
                },
              ),
              const SizedBox(height: 30),
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).push(
                    PageTransitions.slideFromRight(const QuestionStatsScreen()),
                  );
                },
                icon: const Icon(Icons.analytics),
                label: const Text('عرض الإحصائيات'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNoQuestionView() {
    return const Center(
      child: Text('لا يوجد سؤال متاح حالياً', style: TextStyle(fontSize: 18)),
    );
  }

  Color _getDifficultyColor(int difficulty) {
    switch (difficulty) {
      case 1:
        return Colors.green;
      case 2:
        return Colors.orange;
      case 3:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getOptionColor(
    bool isSelected,
    bool isCorrect,
    bool isWrong,
    ThemeData theme,
  ) {
    if (_showResult) {
      if (isCorrect) return AppColors.greenWithOpacity(0.1);
      if (isWrong) return AppColors.redWithOpacity(0.1);
    }
    if (isSelected) {
      return AppColors.primaryWithOpacity(theme.colorScheme.primary, 0.1);
    }
    return theme.cardColor;
  }

  Color _getOptionBorderColor(
    bool isSelected,
    bool isCorrect,
    bool isWrong,
    ThemeData theme,
  ) {
    if (_showResult) {
      if (isCorrect) return Colors.green;
      if (isWrong) return Colors.red;
    }
    if (isSelected) return theme.colorScheme.primary;
    return theme.brightness == Brightness.dark
        ? AppColors.greyWithOpacity(0.3)
        : AppColors.greyWithOpacity(0.2);
  }

  Color _getOptionIconColor(
    bool isSelected,
    bool isCorrect,
    bool isWrong,
    ThemeData theme,
  ) {
    if (_showResult) {
      if (isCorrect) return Colors.green;
      if (isWrong) return Colors.red;
    }
    if (isSelected) return theme.colorScheme.primary;
    return Colors.grey;
  }
}

// شاشة الإحصائيات (مؤقتة)
class QuestionStatsScreen extends StatelessWidget {
  const QuestionStatsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('إحصائيات الأسئلة')),
      body: const Center(child: Text('شاشة الإحصائيات قيد التطوير')),
    );
  }
}
