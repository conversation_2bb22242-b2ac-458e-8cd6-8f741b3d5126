import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/daily_ayah_model.dart';
import '../models/quran_model.dart';
import '../utils/logger.dart';
import 'quran_service.dart';

/// خدمة للتعامل مع الآية اليومية
class DailyAyahService {
  static const String _dailyAyahKey = 'daily_ayah';
  static const String _lastSurahKey = 'last_surah_number';
  static const String _lastAyahKey = 'last_ayah_number';
  static const String _appSessionKey = 'app_session_counter';

  final QuranService _quranService = QuranService();

  /// الحصول على الآية اليومية
  Future<DailyAyah?> getDailyAyah() async {
    try {
      // الحصول على التفضيلات المشتركة
      final prefs = await SharedPreferences.getInstance();

      // زيادة عداد جلسات التطبيق (كل مرة يتم فيها فتح التطبيق)
      int sessionCounter = prefs.getInt(_appSessionKey) ?? 0;
      sessionCounter++;
      await prefs.setInt(_appSessionKey, sessionCounter);

      AppLogger.info('جلسة التطبيق رقم: $sessionCounter');

      // التحقق من وجود آية يومية مخزنة
      final dailyAyahJson = prefs.getString(_dailyAyahKey);
      if (dailyAyahJson != null) {
        // تحويل البيانات المخزنة إلى نموذج
        final dailyAyah = DailyAyah.fromJson(json.decode(dailyAyahJson));

        // إذا لم تتم قراءة الآية، نعيدها مرة أخرى
        if (!dailyAyah.isRead) {
          AppLogger.info(
            'إعادة عرض الآية السابقة لأنها لم تتم قراءتها: سورة ${dailyAyah.surah.name} - آية ${dailyAyah.ayah.numberInSurah}',
          );
          return dailyAyah;
        }

        // إذا تمت قراءة الآية، ننتقل إلى الآية التالية
        AppLogger.info('الآية السابقة تمت قراءتها، الانتقال إلى الآية التالية');
      }

      // إنشاء آية جديدة
      return await _generateNewDailyAyah();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الآية اليومية: $e');
      return null;
    }
  }

  /// إنشاء آية يومية جديدة
  Future<DailyAyah?> _generateNewDailyAyah() async {
    try {
      // الحصول على التفضيلات المشتركة
      final prefs = await SharedPreferences.getInstance();

      // الحصول على آخر سورة وآية تم عرضها
      int lastSurahNumber = prefs.getInt(_lastSurahKey) ?? 0;
      int lastAyahNumber = prefs.getInt(_lastAyahKey) ?? 0;

      // الحصول على قائمة السور
      final surahs = await _quranService.getSurahs();

      // تحديد السورة والآية التالية
      int nextSurahNumber = lastSurahNumber;
      int nextAyahNumber = lastAyahNumber + 1;

      // التحقق مما إذا كنا قد وصلنا إلى نهاية السورة الحالية
      if (nextSurahNumber < surahs.length) {
        final currentSurah = surahs[nextSurahNumber];

        // إذا وصلنا إلى نهاية السورة، ننتقل إلى السورة التالية
        if (nextAyahNumber >= currentSurah.numberOfAyahs) {
          nextSurahNumber++;
          nextAyahNumber = 0;

          // إذا وصلنا إلى نهاية القرآن، نبدأ من جديد
          if (nextSurahNumber >= surahs.length) {
            nextSurahNumber = 0;
            nextAyahNumber = 0;
          }
        }
      } else {
        // إذا كان هناك خطأ في الأرقام، نبدأ من البداية
        nextSurahNumber = 0;
        nextAyahNumber = 0;
      }

      // الحصول على السورة التالية
      final nextSurah = surahs[nextSurahNumber];

      // الحصول على آيات السورة
      final ayahs = await _quranService.getAyahs(nextSurah.number);

      // التحقق من وجود آيات
      if (ayahs.isEmpty) {
        AppLogger.error('لا توجد آيات للسورة ${nextSurah.number}');
        return null;
      }

      // التحقق من صحة رقم الآية
      if (nextAyahNumber >= ayahs.length) {
        nextAyahNumber = 0;
      }

      // الحصول على الآية التالية
      final nextAyah = ayahs[nextAyahNumber];

      // تخطي البسملة إذا كانت آية منفصلة (ما عدا في سورة الفاتحة)
      if (nextAyah.isBismillah && nextSurah.number != 1) {
        // إذا كانت البسملة، ننتقل إلى الآية التالية
        nextAyahNumber++;

        // التحقق من صحة رقم الآية الجديد
        if (nextAyahNumber >= ayahs.length) {
          nextSurahNumber++;
          nextAyahNumber = 0;

          // إذا وصلنا إلى نهاية القرآن، نبدأ من جديد
          if (nextSurahNumber >= surahs.length) {
            nextSurahNumber = 0;
            nextAyahNumber = 0;
          }

          // الحصول على السورة والآيات الجديدة
          final newNextSurah = surahs[nextSurahNumber];
          final newAyahs = await _quranService.getAyahs(newNextSurah.number);

          // التحقق من وجود آيات
          if (newAyahs.isEmpty) {
            AppLogger.error('لا توجد آيات للسورة ${newNextSurah.number}');
            return null;
          }

          // الحصول على الآية الجديدة
          final newNextAyah = newAyahs[nextAyahNumber];

          // حفظ الآية اليومية الجديدة
          final dailyAyah = await _saveDailyAyah(newNextSurah, newNextAyah);
          return dailyAyah;
        } else {
          // الحصول على الآية التالية
          final newNextAyah = ayahs[nextAyahNumber];

          // حفظ الآية اليومية الجديدة
          final dailyAyah = await _saveDailyAyah(nextSurah, newNextAyah);
          return dailyAyah;
        }
      }

      // حفظ الآية اليومية الجديدة
      final dailyAyah = await _saveDailyAyah(nextSurah, nextAyah);
      return dailyAyah;
    } catch (e) {
      AppLogger.error('خطأ في إنشاء آية يومية جديدة: $e');
      return null;
    }
  }

  /// حفظ الآية اليومية
  Future<DailyAyah> _saveDailyAyah(Surah surah, Ayah ayah) async {
    try {
      // الحصول على التفضيلات المشتركة
      final prefs = await SharedPreferences.getInstance();

      // الحصول على تفسير الآية
      final ayahsWithTafsir = await _quranService.getAyahsWithTafsir(
        surah.number,
        [ayah],
      );
      final ayahWithTafsir =
          ayahsWithTafsir.isNotEmpty ? ayahsWithTafsir.first : ayah;

      // إنشاء آية يومية جديدة
      final dailyAyah = DailyAyah(
        surah: surah,
        ayah: ayah,
        date: DateTime.now(),
        tafsir: ayahWithTafsir.tafsir,
        isRead: false, // الآية الجديدة غير مقروءة بعد
      );

      // حفظ الآية اليومية
      await prefs.setString(_dailyAyahKey, json.encode(dailyAyah.toJson()));

      // حفظ آخر سورة وآية تم عرضها
      await prefs.setInt(
        _lastSurahKey,
        surah.number - 1,
      ); // نستخدم الفهرس بدلاً من رقم السورة
      await prefs.setInt(_lastAyahKey, ayah.numberInSurah);

      AppLogger.info(
        'تم حفظ الآية الجديدة: سورة ${surah.name} - آية ${ayah.numberInSurah}',
      );

      return dailyAyah;
    } catch (e) {
      AppLogger.error('خطأ في حفظ الآية اليومية: $e');
      rethrow;
    }
  }

  /// تحديث حالة قراءة الآية اليومية
  Future<DailyAyah?> markDailyAyahAsRead() async {
    try {
      // الحصول على التفضيلات المشتركة
      final prefs = await SharedPreferences.getInstance();

      // التحقق من وجود آية يومية مخزنة
      final dailyAyahJson = prefs.getString(_dailyAyahKey);
      if (dailyAyahJson == null) {
        return null;
      }

      // تحويل البيانات المخزنة إلى نموذج
      final dailyAyah = DailyAyah.fromJson(json.decode(dailyAyahJson));

      // إنشاء نسخة جديدة من الآية مع تحديث حالة القراءة
      final updatedAyah = dailyAyah.copyWith(isRead: true);

      // حفظ الآية المحدثة
      await prefs.setString(_dailyAyahKey, json.encode(updatedAyah.toJson()));

      AppLogger.info(
        'تم تحديث حالة قراءة الآية: سورة ${updatedAyah.surah.name} - آية ${updatedAyah.ayah.numberInSurah}',
      );

      return updatedAyah;
    } catch (e) {
      AppLogger.error('خطأ في تحديث حالة قراءة الآية اليومية: $e');
      return null;
    }
  }
}
