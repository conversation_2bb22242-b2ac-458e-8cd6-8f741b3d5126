/// نموذج دعاء
class Duaa {
  final int id;
  final String text;
  final String source;
  final String? occasion;
  final String? fadl;

  Duaa({
    required this.id,
    required this.text,
    required this.source,
    this.occasion,
    this.fadl,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'text': text,
      'source': source,
      'occasion': occasion,
      'fadl': fadl,
    };
  }

  factory Duaa.fromMap(Map<String, dynamic> map) {
    return Duaa(
      id: map['id'],
      text: map['text'],
      source: map['source'],
      occasion: map['occasion'],
      fadl: map['fadl'],
    );
  }

  Duaa copyWith({
    int? id,
    String? text,
    String? source,
    String? occasion,
    String? fadl,
  }) {
    return Duaa(
      id: id ?? this.id,
      text: text ?? this.text,
      source: source ?? this.source,
      occasion: occasion ?? this.occasion,
      fadl: fadl ?? this.fadl,
    );
  }
}

/// بيانات الأدعية
final List<Duaa> duaaData = [
  Duaa(
    id: 1,
    text: 'رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً وَقِنَا عَذَابَ النَّارِ',
    source: 'سورة البقرة: 201',
    occasion: 'دعاء عام',
    fadl: 'كان أكثر دعاء النبي صلى الله عليه وسلم',
  ),
  Duaa(
    id: 2,
    text: 'اللَّهُمَّ إِنِّي أَسْأَلُكَ الْهُدَى وَالتُّقَى، وَالْعَفَافَ وَالْغِنَى',
    source: 'رواه مسلم',
    occasion: 'دعاء عام',
    fadl: 'من أدعية النبي صلى الله عليه وسلم',
  ),
  Duaa(
    id: 3,
    text: 'اللَّهُمَّ اغْفِرْ لِي ذَنْبِي كُلَّهُ، دِقَّهُ وَجِلَّهُ، وَأَوَّلَهُ وَآخِرَهُ، وَعَلَانِيَتَهُ وَسِرَّهُ',
    source: 'رواه مسلم',
    occasion: 'دعاء الاستغفار',
    fadl: 'من أدعية النبي صلى الله عليه وسلم في الاستغفار',
  ),
  Duaa(
    id: 4,
    text: 'اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنْ الْهَمِّ وَالْحَزَنِ، وَالْعَجْزِ وَالْكَسَلِ، وَالْبُخْلِ وَالْجُبْنِ، وَضَلَعِ الدَّيْنِ وَغَلَبَةِ الرِّجَالِ',
    source: 'رواه البخاري',
    occasion: 'دعاء الهم والحزن',
    fadl: 'كان النبي صلى الله عليه وسلم يتعوذ بهذا الدعاء',
  ),
  Duaa(
    id: 5,
    text: 'اللَّهُمَّ رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً، وَفِي الْآخِرَةِ حَسَنَةً، وَقِنَا عَذَابَ النَّارِ',
    source: 'رواه البخاري ومسلم',
    occasion: 'دعاء عام',
    fadl: 'كان أكثر دعاء النبي صلى الله عليه وسلم',
  ),
  Duaa(
    id: 6,
    text: 'اللَّهُمَّ إِنِّي أَسْأَلُكَ عِلْمًا نَافِعًا، وَرِزْقًا طَيِّبًا، وَعَمَلًا مُتَقَبَّلًا',
    source: 'رواه ابن ماجه',
    occasion: 'دعاء طلب العلم',
    fadl: 'كان النبي صلى الله عليه وسلم يدعو به بعد صلاة الفجر',
  ),
  Duaa(
    id: 7,
    text: 'اللَّهُمَّ أَصْلِحْ لِي دِينِي الَّذِي هُوَ عِصْمَةُ أَمْرِي، وَأَصْلِحْ لِي دُنْيَايَ الَّتِي فِيهَا مَعَاشِي، وَأَصْلِحْ لِي آخِرَتِي الَّتِي فِيهَا مَعَادِي، وَاجْعَلِ الْحَيَاةَ زِيَادَةً لِي فِي كُلِّ خَيْرٍ، وَاجْعَلِ الْمَوْتَ رَاحَةً لِي مِنْ كُلِّ شَرٍّ',
    source: 'رواه مسلم',
    occasion: 'دعاء عام',
    fadl: 'من أدعية النبي صلى الله عليه وسلم',
  ),
  Duaa(
    id: 8,
    text: 'اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنْ زَوَالِ نِعْمَتِكَ، وَتَحَوُّلِ عَافِيَتِكَ، وَفُجَاءَةِ نِقْمَتِكَ، وَجَمِيعِ سَخَطِكَ',
    source: 'رواه مسلم',
    occasion: 'دعاء التعوذ',
    fadl: 'من أدعية النبي صلى الله عليه وسلم في التعوذ',
  ),
  Duaa(
    id: 9,
    text: 'اللَّهُمَّ إِنِّي ظَلَمْتُ نَفْسِي ظُلْمًا كَثِيرًا، وَلَا يَغْفِرُ الذُّنُوبَ إِلَّا أَنْتَ، فَاغْفِرْ لِي مَغْفِرَةً مِنْ عِنْدِكَ، وَارْحَمْنِي، إِنَّكَ أَنْتَ الْغَفُورُ الرَّحِيمُ',
    source: 'رواه البخاري ومسلم',
    occasion: 'دعاء الاستغفار',
    fadl: 'علمه النبي صلى الله عليه وسلم لأبي بكر الصديق رضي الله عنه',
  ),
  Duaa(
    id: 10,
    text: 'اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنْ عِلْمٍ لَا يَنْفَعُ، وَمِنْ قَلْبٍ لَا يَخْشَعُ، وَمِنْ نَفْسٍ لَا تَشْبَعُ، وَمِنْ دَعْوَةٍ لَا يُسْتَجَابُ لَهَا',
    source: 'رواه مسلم',
    occasion: 'دعاء التعوذ',
    fadl: 'من أدعية النبي صلى الله عليه وسلم',
  ),
];
