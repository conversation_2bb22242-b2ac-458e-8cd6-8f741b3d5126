# 🧪 دليل اختبار التطبيق الإسلامي "أذكاري"

## 📱 معلومات APK

- **المسار**: `build/app/outputs/flutter-apk/app-release.apk`
- **الحجم**: 29.5 ميجابايت
- **النوع**: Release APK
- **التاريخ**: 24 مايو 2024

---

## 🚀 خطوات التثبيت والاختبار

### 1. تثبيت APK على جهاز أندرويد

#### الطريقة الأولى: عبر ADB
```bash
# تأكد من تفعيل USB Debugging على الجهاز
adb devices

# تثبيت APK
adb install build/app/outputs/flutter-apk/app-release.apk
```

#### الطريقة الثانية: نسخ مباشر
1. انسخ ملف `app-release.apk` إلى الجهاز
2. افتح مدير الملفات على الجهاز
3. اضغط على ملف APK
4. اتبع تعليمات التثبيت

### 2. متطلبات الجهاز
- **نظام التشغيل**: Android 5.0 (API 21) أو أحدث
- **المساحة المطلوبة**: 50 ميجابايت على الأقل
- **الأذونات**: الموقع، التخزين، الإنترنت

---

## ✅ قائمة اختبار الميزات

### 🌟 الميزات الأساسية

#### [ ] 1. الشاشة الرئيسية
- [ ] فتح التطبيق بنجاح
- [ ] عرض الشعار والترحيب
- [ ] التنقل السلس بين الأقسام
- [ ] عمل التنقل السفلي

#### [ ] 2. أذكار الصباح والمساء
- [ ] فتح قسم أذكار الصباح
- [ ] عرض قائمة الأذكار بشكل صحيح
- [ ] عمل عدادات الأذكار
- [ ] حفظ التقدم عند إغلاق التطبيق
- [ ] عرض المصادر والمراجع

#### [ ] 3. القرآن الكريم
- [ ] عرض قائمة السور
- [ ] فتح سورة وعرض الآيات
- [ ] عمل البحث في القرآن
- [ ] عرض التفسير (إن وجد)
- [ ] التنقل بين الآيات

#### [ ] 4. البوصلة الإسلامية
- [ ] فتح البوصلة
- [ ] طلب إذن الموقع
- [ ] عرض اتجاه القبلة بدقة
- [ ] عمل الرسوم المتحركة

#### [ ] 5. أوقات الصلاة
- [ ] عرض أوقات الصلاة للمدينة الحالية
- [ ] دقة الأوقات
- [ ] عرض الوقت المتبقي للصلاة التالية

#### [ ] 6. أسماء الله الحسنى
- [ ] عرض قائمة الأسماء
- [ ] عرض المعاني
- [ ] التمرير الأفقي في الشاشة الرئيسية

---

### 🆕 الميزات الجديدة

#### [ ] 7. نظام الإحصائيات
- [ ] فتح شاشة الإحصائيات من القائمة
- [ ] عرض الإحصائيات اليومية:
  - [ ] عدد الأذكار المكتملة اليوم
  - [ ] النقاط المكتسبة
  - [ ] الوقت المقضي
- [ ] عرض الإحصائيات الأسبوعية والشهرية
- [ ] عمل الخريطة الحرارية:
  - [ ] عرض النشاط اليومي
  - [ ] ألوان مختلفة حسب مستوى النشاط
- [ ] عرض مخططات التقدم:
  - [ ] مخطط دائري للتصنيفات
  - [ ] مخطط خطي للتقدم الزمني

#### [ ] 8. نظام الإنجازات
- [ ] فتح شاشة الإنجازات
- [ ] عرض الإنجازات المفتوحة والمقفلة
- [ ] عرض تفاصيل كل إنجاز:
  - [ ] العنوان والوصف
  - [ ] التقدم الحالي/المطلوب
  - [ ] النقاط المكتسبة
  - [ ] المستوى (برونزي، فضي، ذهبي، إلخ)
- [ ] فتح إنجاز جديد عند تحقيق الهدف

#### [ ] 9. النافذة المنبثقة للاحتفال
**خطوات الاختبار:**
1. [ ] اختر تصنيف أذكار (مثل أذكار الصباح)
2. [ ] أكمل جميع الأذكار في التصنيف
3. [ ] تحقق من ظهور النافذة المنبثقة
4. [ ] تحقق من المحتوى:
   - [ ] رسالة "بارك الله فيك!"
   - [ ] اسم التصنيف المكتمل
   - [ ] عدد الأذكار المكتملة
   - [ ] النقاط المكتسبة
5. [ ] تحقق من الرسوم المتحركة:
   - [ ] ظهور النافذة بسلاسة
   - [ ] النجوم المتحركة
   - [ ] التأثيرات البصرية
6. [ ] اضغط "عرض الإحصائيات" والتحقق من الانتقال
7. [ ] اضغط "متابعة" والتحقق من الإغلاق

---

### 🎨 اختبار الثيمات والتصميم

#### [ ] 10. الثيمات
- [ ] الوضع الفاتح:
  - [ ] ألوان فاتحة ومريحة للعين
  - [ ] وضوح النصوص
  - [ ] تناسق الألوان
- [ ] الوضع المعتم:
  - [ ] ألوان معتمة مناسبة
  - [ ] وضوح النصوص البيضاء
  - [ ] راحة العين في الإضاءة المنخفضة
- [ ] الوضع الليلي:
  - [ ] ألوان داكنة جداً
  - [ ] تقليل الضوء الأزرق
  - [ ] مناسب للاستخدام الليلي

#### [ ] 11. التصميم والواجهة
- [ ] محاذاة النصوص العربية (RTL)
- [ ] وضوح الخطوط العربية
- [ ] تناسق الألوان الإسلامية
- [ ] سلاسة الرسوم المتحركة
- [ ] استجابة الواجهة للمس

---

### 🔧 اختبار الوظائف التقنية

#### [ ] 12. الأداء
- [ ] سرعة فتح التطبيق
- [ ] سلاسة التنقل بين الشاشات
- [ ] عدم تجمد التطبيق
- [ ] استهلاك معقول للبطارية
- [ ] استهلاك معقول للذاكرة

#### [ ] 13. حفظ البيانات
- [ ] حفظ تقدم الأذكار عند إغلاق التطبيق
- [ ] حفظ الإعدادات (الثيم، إلخ)
- [ ] حفظ الإحصائيات والنقاط
- [ ] حفظ حالة الإنجازات

#### [ ] 14. الأذونات
- [ ] طلب إذن الموقع للبوصلة
- [ ] طلب إذن التخزين لحفظ البيانات
- [ ] عمل التطبيق بدون إنترنت (الميزات الأساسية)

---

## 🐛 تسجيل المشاكل

### إذا واجهت أي مشاكل، سجل:

#### معلومات الجهاز:
- نوع الجهاز: ________________
- إصدار أندرويد: ________________
- مساحة التخزين المتاحة: ________________

#### وصف المشكلة:
- الميزة المتأثرة: ________________
- خطوات إعادة الإنتاج: ________________
- السلوك المتوقع: ________________
- السلوك الفعلي: ________________
- لقطة شاشة (إن أمكن): ________________

---

## ✅ تقرير الاختبار النهائي

### الميزات التي تعمل بشكل مثالي:
- [ ] الشاشة الرئيسية والتنقل
- [ ] أذكار الصباح والمساء
- [ ] القرآن الكريم والبحث
- [ ] البوصلة الإسلامية
- [ ] أوقات الصلاة
- [ ] أسماء الله الحسنى
- [ ] نظام الإحصائيات
- [ ] نظام الإنجازات
- [ ] النافذة المنبثقة للاحتفال
- [ ] الثيمات والتصميم

### الميزات التي تحتاج تحسين:
- ________________
- ________________
- ________________

### التقييم العام:
- **الأداء**: ⭐⭐⭐⭐⭐ (1-5)
- **التصميم**: ⭐⭐⭐⭐⭐ (1-5)
- **سهولة الاستخدام**: ⭐⭐⭐⭐⭐ (1-5)
- **الاستقرار**: ⭐⭐⭐⭐⭐ (1-5)

### ملاحظات إضافية:
________________
________________
________________

---

## 🎉 الخلاصة

التطبيق الإسلامي "أذكاري" جاهز للاستخدام مع جميع الميزات الجديدة:
- ✅ نظام إحصائيات شامل ومتطور
- ✅ نظام إنجازات محفز ومتدرج
- ✅ نافذة احتفال جميلة ومتحركة
- ✅ تصميم إسلامي أصيل ومتسق
- ✅ أداء محسن واستقرار عالي

**جاهز للنشر والاستخدام! 🚀**
