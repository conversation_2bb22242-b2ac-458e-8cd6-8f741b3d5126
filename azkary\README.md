# تطبيق أذكاري

تطبيق أذكار إسلامي يومي مبني باستخدام Flutter/Dart.

## الميزات

- أذكار مقسمة حسب التصنيف (أذكار الصباح، أذكار المساء، أذكار النوم، أذكار الاستيقاظ، أذكار الصلاة، أذكار الاستغفار)
- عداد لكل ذكر لتتبع عدد التكرارات المطلوبة
- وضع ليلي ووضع نهاري
- إمكانية نسخ ومشاركة الأذكار
- إشعارات تذكير بأذكار الصباح والمساء
- ميزة "ذكر اليوم" لعرض ذكر مختار يومياً
- قائمة المفضلة لحفظ الأذكار المفضلة
- واجهة مستخدم عربية بالكامل

## التثبيت

1. تأكد من تثبيت Flutter على جهازك. يمكنك اتباع [دليل التثبيت الرسمي](https://flutter.dev/docs/get-started/install).

2. استنسخ هذا المستودع:
```
git clone https://github.com/yourusername/azkary.git
```

3. انتقل إلى مجلد المشروع:
```
cd azkary
```

4. قم بتثبيت التبعيات:
```
flutter pub get
```

5. قم بتشغيل التطبيق:
```
flutter run
```

## بنية المشروع

- `lib/main.dart`: نقطة الدخول الرئيسية للتطبيق
- `lib/models/`: نماذج البيانات
- `lib/screens/`: شاشات التطبيق المختلفة
- `lib/widgets/`: العناصر المرئية المخصصة
- `lib/services/`: خدمات التطبيق (قاعدة البيانات، الإشعارات، إلخ)
- `lib/utils/`: أدوات مساعدة
- `lib/theme/`: ملفات السمات والألوان
- `lib/constants/`: البيانات الثابتة

## المساهمة

نرحب بمساهماتكم! يرجى إرسال طلب سحب أو فتح مشكلة إذا كان لديك أي اقتراحات أو تحسينات.

## الترخيص

هذا المشروع مرخص بموجب رخصة MIT - انظر ملف LICENSE للحصول على التفاصيل.
