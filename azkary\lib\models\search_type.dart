/// نوع البحث في القرآن
enum SearchType {
  /// البحث في أسماء السور
  surahName,

  /// البحث في نصوص الآيات
  ayahText,
}

/// امتداد لتحويل نوع البحث إلى نص
extension SearchTypeExtension on SearchType {
  /// الحصول على اسم نوع البحث بالعربية
  String get arabicName {
    switch (this) {
      case SearchType.surahName:
        return 'البحث في أسماء السور';
      case SearchType.ayahText:
        return 'البحث في نصوص الآيات';
    }
  }

  /// الحصول على وصف نوع البحث بالعربية
  String get description {
    switch (this) {
      case SearchType.surahName:
        return 'البحث عن اسم سورة معينة';
      case SearchType.ayahText:
        return 'البحث عن نص آية أو جزء منها';
    }
  }
}
