import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// شريط تنقل علوي مخصص ومتناسق لجميع شاشات التطبيق
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final double elevation;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final SystemUiOverlayStyle? systemOverlayStyle;
  final double height;
  final bool showBorder;
  final VoidCallback? onLeadingPressed;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.elevation = 2,
    this.backgroundColor,
    this.foregroundColor,
    this.systemOverlayStyle,
    this.height = kToolbarHeight + 8,
    this.showBorder = true,
    this.onLeadingPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // تحديد الألوان بناءً على السمة الحالية
    final bgColor =
        backgroundColor ??
        theme.appBarTheme.backgroundColor ??
        theme.colorScheme.surface;
    final fgColor =
        foregroundColor ??
        theme.appBarTheme.foregroundColor ??
        theme.colorScheme.onSurface;

    // تحديد لون الحدود بناءً على وضع السمة
    final borderColor =
        isDarkMode
            ? Colors.grey.shade800.withAlpha((0.5 * 255).round())
            : Colors.grey.shade300;

    return Container(
      height: height,
      decoration: BoxDecoration(
        color: bgColor,
        boxShadow:
            elevation > 0
                ? [
                  BoxShadow(
                    color:
                        isDarkMode
                            ? Colors.black.withAlpha(77) // 0.3 * 255 = ~77
                            : Colors.black.withAlpha(26), // 0.1 * 255 = ~26
                    blurRadius: elevation * 2,
                    offset: const Offset(0, 1),
                  ),
                ]
                : null,
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(16)),
        border:
            showBorder
                ? Border(bottom: BorderSide(color: borderColor, width: 0.5))
                : null,
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4.0),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // زر الرجوع أو القائمة
              Positioned(
                right: 4,
                child:
                    leading ??
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: theme.colorScheme.primary.withAlpha(20),
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.arrow_forward,
                          color: theme.colorScheme.primary,
                          size: 22,
                        ),
                        onPressed:
                            onLeadingPressed ??
                            () => Navigator.of(context).pop(),
                        tooltip: 'رجوع',
                      ),
                    ),
              ),

              // العنوان
              Center(
                child: Text(
                  title,
                  style: TextStyle(
                    color: fgColor,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              // الإجراءات
              if (actions != null && actions!.isNotEmpty)
                Positioned(
                  left: 4,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: actions!,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
}
