import 'package:flutter/material.dart';
import '../models/azkar_completion_model.dart';

/// واجهة عرض إكمال أذكار الصنف
class AzkarCompletionOverlay extends StatefulWidget {
  /// نموذج إكمال الأذكار
  final AzkarCompletionModel completionModel;

  /// دالة إعادة ضبط الأذكار
  final VoidCallback onReset;

  /// دالة إغلاق الواجهة
  final VoidCallback onClose;

  const AzkarCompletionOverlay({
    super.key,
    required this.completionModel,
    required this.onReset,
    required this.onClose,
  });

  @override
  State<AzkarCompletionOverlay> createState() => _AzkarCompletionOverlayState();
}

class _AzkarCompletionOverlayState extends State<AzkarCompletionOverlay>
    with TickerProviderStateMixin {
  // متحكم حركة الظهور الرئيسي
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  // متحكم حركة النبض للأيقونة
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  // متحكم حركة التوهج للنص
  late AnimationController _glowController;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();

    // إعداد حركة الظهور الرئيسية
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _scaleAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    );

    _opacityAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    );

    // إعداد حركة النبض للأيقونة
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // إعداد حركة التوهج للنص
    _glowController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _glowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _glowController, curve: Curves.easeInOut),
    );

    // بدء الحركات
    _controller.forward();

    // بدء حركة النبض بعد ظهور الواجهة
    Future.delayed(const Duration(milliseconds: 500), () {
      _pulseController.repeat(reverse: true);
      _glowController.repeat(reverse: true);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _pulseController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Material(
      color: Colors.transparent,
      child: GestureDetector(
        onTap: widget.onClose,
        child: Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.black54,
          child: Center(
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Opacity(
                  opacity: _opacityAnimation.value,
                  child: Transform.scale(
                    scale: _scaleAnimation.value,
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.85,
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: isDarkMode ? Colors.grey[900] : Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: theme.colorScheme.primary.withValues(
                              red: theme.colorScheme.primary.r.toDouble(),
                              green: theme.colorScheme.primary.g.toDouble(),
                              blue: theme.colorScheme.primary.b.toDouble(),
                              alpha: 0.3,
                            ),
                            blurRadius: 15,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // أيقونة الإكمال مع حركة النبض
                          AnimatedBuilder(
                            animation: _pulseController,
                            builder: (context, child) {
                              return Transform.scale(
                                scale: _pulseAnimation.value,
                                child: Container(
                                  width: 100,
                                  height: 100,
                                  decoration: BoxDecoration(
                                    color: theme.colorScheme.primary.withAlpha(
                                      50,
                                    ),
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: theme.colorScheme.primary
                                            .withAlpha(100),
                                        blurRadius: 20,
                                        spreadRadius: 5,
                                      ),
                                    ],
                                  ),
                                  child: Icon(
                                    Icons.check_circle,
                                    color: theme.colorScheme.primary,
                                    size: 70,
                                  ),
                                ),
                              );
                            },
                          ),
                          const SizedBox(height: 24),

                          // عنوان الإكمال مع حركة التوهج
                          AnimatedBuilder(
                            animation: _glowController,
                            builder: (context, child) {
                              return ShaderMask(
                                shaderCallback: (Rect bounds) {
                                  return LinearGradient(
                                    colors: [
                                      theme.colorScheme.primary,
                                      theme.colorScheme.secondary,
                                      theme.colorScheme.primary,
                                    ],
                                    stops: [0.0, _glowAnimation.value, 1.0],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ).createShader(bounds);
                                },
                                child: Text(
                                  'أحسنت',
                                  style: TextStyle(
                                    fontSize: 40,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              );
                            },
                          ),
                          const SizedBox(height: 16),

                          // نص الإكمال مع تأثير بصري محسن
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 10,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary.withAlpha(30),
                              borderRadius: BorderRadius.circular(15),
                              border: Border.all(
                                color: theme.colorScheme.primary.withAlpha(100),
                                width: 2,
                              ),
                            ),
                            child: Text(
                              'تم تسبيح أذكار ${widget.completionModel.category}',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color:
                                    isDarkMode ? Colors.white : Colors.black87,
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),

                          // نسبة الإكمال مع تأثير بصري محسن
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.star, color: Colors.amber, size: 24),
                              const SizedBox(width: 8),
                              Text(
                                '${widget.completionModel.completedCount}/${widget.completionModel.totalCount}',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Icon(Icons.star, color: Colors.amber, size: 24),
                            ],
                          ),
                          const SizedBox(height: 30),

                          // أزرار الإجراءات مع تأثيرات محسنة
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // زر إعادة الضبط
                              ElevatedButton.icon(
                                onPressed: () {
                                  widget.onReset();
                                  widget.onClose();
                                },
                                icon: const Icon(Icons.refresh),
                                label: const Text(
                                  'إعادة ضبط',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: theme.colorScheme.primary,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 15,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  elevation: 5,
                                ),
                              ),
                              const SizedBox(width: 20),

                              // زر الإغلاق
                              OutlinedButton.icon(
                                onPressed: widget.onClose,
                                icon: const Icon(Icons.close),
                                label: const Text(
                                  'إغلاق',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                style: OutlinedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 15,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  side: BorderSide(
                                    color: theme.colorScheme.primary,
                                    width: 2,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
