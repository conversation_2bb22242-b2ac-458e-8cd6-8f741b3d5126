import 'package:flutter/material.dart';

/// تأثيرات انتقالية محسنة للصفحات
class PageTransitions {
  /// انتقال بتأثير الانزلاق من اليمين (مناسب للعربية) - محسن للأداء
  static PageRouteBuilder slideFromRight(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(
        milliseconds: 200,
      ), // تقليل المدة لتحسين الأداء
      reverseTransitionDuration: const Duration(
        milliseconds: 150,
      ), // تقليل المدة للعودة
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeOut; // منحنى أبسط وأسرع

        var tween = Tween(
          begin: begin,
          end: end,
        ).chain(CurveTween(curve: curve));

        return SlideTransition(position: animation.drive(tween), child: child);
      },
    );
  }

  /// انتقال بتأثير الانزلاق من اليسار - محسن للأداء
  static PageRouteBuilder slideFromLeft(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 200), // تحسين الأداء
      reverseTransitionDuration: const Duration(milliseconds: 150),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(-1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeOut; // منحنى أبسط

        var tween = Tween(
          begin: begin,
          end: end,
        ).chain(CurveTween(curve: curve));

        return SlideTransition(position: animation.drive(tween), child: child);
      },
    );
  }

  /// انتقال بتأثير التلاشي - محسن للأداء
  static PageRouteBuilder fadeTransition(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 250), // تحسين الأداء
      reverseTransitionDuration: const Duration(milliseconds: 200),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(opacity: animation, child: child);
      },
    );
  }

  /// انتقال بتأثير التكبير - محسن للأداء
  static PageRouteBuilder scaleTransition(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 200), // تحسين الأداء
      reverseTransitionDuration: const Duration(milliseconds: 150),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const curve = Curves.easeOut; // منحنى أبسط
        var tween = Tween(
          begin: 0.95,
          end: 1.0,
        ).chain(CurveTween(curve: curve)); // تقليل التكبير

        return ScaleTransition(
          scale: animation.drive(tween),
          child: FadeTransition(opacity: animation, child: child),
        );
      },
    );
  }

  /// انتقال بتأثير الدوران - محسن للأداء
  static PageRouteBuilder rotationTransition(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(
        milliseconds: 250,
      ), // تحسين كبير للأداء
      reverseTransitionDuration: const Duration(milliseconds: 200),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const curve = Curves.easeOut; // منحنى أبسط
        // تبسيط التأثير - إزالة الدوران والاكتفاء بالتكبير
        var scaleTween = Tween(
          begin: 0.95,
          end: 1.0,
        ).chain(CurveTween(curve: curve));

        // تبسيط التأثير - استخدام ScaleTransition فقط لتحسين الأداء
        return ScaleTransition(
          scale: animation.drive(scaleTween),
          child: FadeTransition(opacity: animation, child: child),
        );
      },
    );
  }

  /// انتقال بتأثير الانزلاق من الأسفل - محسن للأداء
  static PageRouteBuilder slideFromBottom(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 250), // تحسين الأداء
      reverseTransitionDuration: const Duration(milliseconds: 200),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        const curve = Curves.easeOut; // منحنى أبسط

        var tween = Tween(
          begin: begin,
          end: end,
        ).chain(CurveTween(curve: curve));

        return SlideTransition(position: animation.drive(tween), child: child);
      },
    );
  }

  /// انتقال مخصص للصفحات الإسلامية - محسن للأداء
  static PageRouteBuilder islamicTransition(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(
        milliseconds: 250,
      ), // تحسين كبير للأداء
      reverseTransitionDuration: const Duration(milliseconds: 200),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const curve = Curves.easeOut; // منحنى أبسط وأسرع

        // تأثير التلاشي البسيط فقط
        return FadeTransition(
          opacity: animation.drive(
            Tween(begin: 0.0, end: 1.0).chain(CurveTween(curve: curve)),
          ),
          child: child,
        );
      },
    );
  }
}

/// تأثيرات انتقالية للعناصر داخل الصفحة
class AnimatedWidgets {
  /// تأثير ظهور تدريجي للبطاقات - محسن للأداء
  static Widget fadeInCard({
    required Widget child,
    required int index,
    Duration delay = const Duration(milliseconds: 30), // تقليل التأخير أكثر
  }) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 150 + (index * 20)), // تقليل المدة أكثر
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.easeOut, // منحنى أبسط
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 8 * (1 - value)), // تقليل المسافة أكثر
          child: Opacity(opacity: value, child: child),
        );
      },
      child: child,
    );
  }

  /// تأثير نبضة للأزرار - محسن للأداء
  static Widget pulseButton({
    required Widget child,
    required VoidCallback onTap,
    Duration duration = const Duration(milliseconds: 100), // تحسين الأداء
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedScale(
        scale: 1.0,
        duration: duration,
        curve: Curves.easeOut,
        child: child,
      ),
    );
  }

  /// تأثير موجة للتسبيح - محسن للأداء
  static Widget rippleEffect({
    required Widget child,
    required bool isActive,
    Color? color,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200), // تحسين الأداء
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow:
            isActive
                ? [
                  // ظل واحد مبسط لتحسين الأداء
                  BoxShadow(
                    color: (color ?? Colors.green).withAlpha(76), // 0.3 * 255
                    blurRadius: 15, // تقليل التمويه
                    spreadRadius: 3, // تقليل الانتشار
                  ),
                ]
                : null,
      ),
      child: child,
    );
  }
}
