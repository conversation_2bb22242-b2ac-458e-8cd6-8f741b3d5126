import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/comprehensive_statistics.dart';
import '../utils/logger.dart';

/// خدمة الإحصائيات الشاملة
class ComprehensiveStatisticsService {
  static final ComprehensiveStatisticsService _instance =
      ComprehensiveStatisticsService._internal();
  factory ComprehensiveStatisticsService() => _instance;
  ComprehensiveStatisticsService._internal();

  // إحصائيات اليوم الحالي
  ComprehensiveStatistics? _todayStats;

  // مؤقت لحفظ البيانات دورياً
  Timer? _saveTimer;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    try {
      AppLogger.info('تهيئة خدمة الإحصائيات الشاملة...');

      await _loadTodayStats();
      await _recordAppOpen();
      _startPeriodicSave();

      AppLogger.info('تم تهيئة خدمة الإحصائيات الشاملة بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة خدمة الإحصائيات الشاملة: $e');
    }
  }

  /// تحميل إحصائيات اليوم
  Future<void> _loadTodayStats() async {
    try {
      final today = DateTime.now();
      final dateString =
          '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';

      // محاولة تحميل إحصائيات اليوم من قاعدة البيانات
      final existingStats = await _getStatsFromDatabase(dateString);

      if (existingStats != null) {
        _todayStats = existingStats;
      } else {
        // إنشاء إحصائيات جديدة لليوم
        _todayStats = ComprehensiveStatistics(
          date: dateString,
          createdAt: today,
          updatedAt: today,
        );
        await _saveStatsToDatabase(_todayStats!);
      }

      AppLogger.info('تم تحميل إحصائيات اليوم: $dateString');
    } catch (e) {
      AppLogger.error('خطأ في تحميل إحصائيات اليوم: $e');
    }
  }

  /// حفظ الإحصائيات إلى قاعدة البيانات
  Future<void> _saveStatsToDatabase(ComprehensiveStatistics stats) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          'comprehensive_stats_${stats.date}', stats.toJsonString());
      AppLogger.info('تم حفظ إحصائيات ${stats.date}');
    } catch (e) {
      AppLogger.error('خطأ في حفظ الإحصائيات: $e');
    }
  }

  /// تحميل الإحصائيات من قاعدة البيانات
  Future<ComprehensiveStatistics?> _getStatsFromDatabase(String date) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('comprehensive_stats_$date');

      if (jsonString != null) {
        return ComprehensiveStatistics.fromJsonString(jsonString);
      }
      return null;
    } catch (e) {
      AppLogger.error('خطأ في تحميل الإحصائيات من قاعدة البيانات: $e');
      return null;
    }
  }

  /// بدء الحفظ الدوري
  void _startPeriodicSave() {
    _saveTimer?.cancel();
    _saveTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _saveCurrentStats();
    });
  }

  /// حفظ الإحصائيات الحالية
  Future<void> _saveCurrentStats() async {
    if (_todayStats != null) {
      final updatedStats = _todayStats!.copyWith(
        updatedAt: DateTime.now(),
      );
      _todayStats = updatedStats;
      await _saveStatsToDatabase(updatedStats);
    }
  }

  /// تسجيل فتح التطبيق
  Future<void> _recordAppOpen() async {
    try {
      if (_todayStats != null) {
        _todayStats = _todayStats!.copyWith(
          appOpenCount: _todayStats!.appOpenCount + 1,
          updatedAt: DateTime.now(),
        );

        // تحديث السلسلة المتتالية
        await _updateStreak();

        AppLogger.info('تسجيل فتح التطبيق: ${_todayStats!.appOpenCount}');
      }
    } catch (e) {
      AppLogger.error('خطأ في تسجيل فتح التطبيق: $e');
    }
  }

  /// تحديث السلسلة المتتالية
  Future<void> _updateStreak() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastOpenDate = prefs.getString('last_app_open_date');
      final today = DateTime.now();
      final todayString =
          '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';

      if (lastOpenDate == null || lastOpenDate != todayString) {
        // يوم جديد
        await prefs.setString('last_app_open_date', todayString);

        if (lastOpenDate != null) {
          final lastDate = DateTime.parse(lastOpenDate);
          final daysDifference = today.difference(lastDate).inDays;

          if (daysDifference == 1) {
            // يوم متتالي
            final currentStreak = prefs.getInt('current_streak') ?? 0;
            await prefs.setInt('current_streak', currentStreak + 1);

            if (_todayStats != null) {
              _todayStats = _todayStats!.copyWith(
                streakDays: currentStreak + 1,
              );
            }
          } else if (daysDifference > 1) {
            // انقطعت السلسلة
            await prefs.setInt('current_streak', 1);

            if (_todayStats != null) {
              _todayStats = _todayStats!.copyWith(
                streakDays: 1,
              );
            }
          }
        } else {
          // أول مرة
          await prefs.setInt('current_streak', 1);

          if (_todayStats != null) {
            _todayStats = _todayStats!.copyWith(
              streakDays: 1,
            );
          }
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في تحديث السلسلة المتتالية: $e');
    }
  }

  /// تسجيل إكمال ذكر
  Future<void> recordZikrCompletion(
      String categoryName, int completedCount, int totalCount) async {
    try {
      if (_todayStats != null) {
        final currentCategoryStats = _todayStats!.azkarStats[categoryName] ??
            CategoryStatistics(
                categoryName: categoryName, totalAzkar: totalCount);

        final updatedCategoryStats = currentCategoryStats.copyWith(
          completedAzkar: completedCount,
          totalAzkar: totalCount,
          sessions: currentCategoryStats.sessions + 1,
          isCompleted: completedCount >= totalCount,
          lastCompletedAt: completedCount >= totalCount ? DateTime.now() : null,
        );

        final updatedAzkarStats =
            Map<String, CategoryStatistics>.from(_todayStats!.azkarStats);
        updatedAzkarStats[categoryName] = updatedCategoryStats;

        _todayStats = _todayStats!.copyWith(
          azkarStats: updatedAzkarStats,
          totalAzkarCompleted: _todayStats!.totalAzkarCompleted + 1,
          totalAzkarSessions: _todayStats!.totalAzkarSessions + 1,
          updatedAt: DateTime.now(),
        );

        AppLogger.info(
            'تسجيل إكمال ذكر: $categoryName ($completedCount/$totalCount)');
      }
    } catch (e) {
      AppLogger.error('خطأ في تسجيل إكمال الذكر: $e');
    }
  }

  /// تسجيل إجابة سؤال
  Future<void> recordQuestionAnswer(bool isCorrect) async {
    try {
      if (_todayStats != null) {
        _todayStats = _todayStats!.copyWith(
          questionsAnswered: _todayStats!.questionsAnswered + 1,
          correctAnswers: isCorrect
              ? _todayStats!.correctAnswers + 1
              : _todayStats!.correctAnswers,
          wrongAnswers: !isCorrect
              ? _todayStats!.wrongAnswers + 1
              : _todayStats!.wrongAnswers,
          updatedAt: DateTime.now(),
        );

        // حساب دقة الإجابات
        final accuracy = _todayStats!.questionsAnswered > 0
            ? (_todayStats!.correctAnswers / _todayStats!.questionsAnswered) *
                100
            : 0.0;

        _todayStats = _todayStats!.copyWith(questionsAccuracy: accuracy);

        AppLogger.info('تسجيل إجابة سؤال: ${isCorrect ? 'صحيحة' : 'خاطئة'}');
      }
    } catch (e) {
      AppLogger.error('خطأ في تسجيل إجابة السؤال: $e');
    }
  }

  /// تسجيل استخدام التسبيح
  Future<void> recordTasbihUsage(int count) async {
    try {
      if (_todayStats != null) {
        _todayStats = _todayStats!.copyWith(
          tasbihCount: _todayStats!.tasbihCount + count,
          tasbihSessions: _todayStats!.tasbihSessions + 1,
          updatedAt: DateTime.now(),
        );

        AppLogger.info(
            'تسجيل استخدام التسبيح: +$count (المجموع: ${_todayStats!.tasbihCount})');
      }
    } catch (e) {
      AppLogger.error('خطأ في تسجيل استخدام التسبيح: $e');
    }
  }

  /// تسجيل قراءة القرآن
  Future<void> recordQuranReading(int minutes, int verses, int chapters) async {
    try {
      if (_todayStats != null) {
        _todayStats = _todayStats!.copyWith(
          quranReadingTime: _todayStats!.quranReadingTime + minutes,
          versesRead: _todayStats!.versesRead + verses,
          chaptersRead: _todayStats!.chaptersRead + chapters,
          updatedAt: DateTime.now(),
        );

        AppLogger.info(
            'تسجيل قراءة القرآن: $minutes دقيقة، $verses آية، $chapters سورة');
      }
    } catch (e) {
      AppLogger.error('خطأ في تسجيل قراءة القرآن: $e');
    }
  }

  /// تسجيل إضافة مفضلة
  Future<void> recordFavoriteAdded() async {
    try {
      if (_todayStats != null) {
        _todayStats = _todayStats!.copyWith(
          favoritesAdded: _todayStats!.favoritesAdded + 1,
          updatedAt: DateTime.now(),
        );

        AppLogger.info('تسجيل إضافة مفضلة: ${_todayStats!.favoritesAdded}');
      }
    } catch (e) {
      AppLogger.error('خطأ في تسجيل إضافة المفضلة: $e');
    }
  }

  /// الحصول على إحصائيات اليوم
  ComprehensiveStatistics? get todayStats => _todayStats;

  /// تنظيف الموارد
  void dispose() {
    _saveTimer?.cancel();
    _saveCurrentStats();
  }
}
