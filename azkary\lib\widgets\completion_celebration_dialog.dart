import 'package:flutter/material.dart';

/// نافذة الاحتفال بإكمال الأذكار
class CompletionCelebrationDialog extends StatefulWidget {
  final String categoryName;
  final int completedCount;
  final int earnedPoints;
  final VoidCallback? onClosed;

  const CompletionCelebrationDialog({
    super.key,
    required this.categoryName,
    required this.completedCount,
    required this.earnedPoints,
    this.onClosed,
  });

  @override
  State<CompletionCelebrationDialog> createState() =>
      _CompletionCelebrationDialogState();
}

class _CompletionCelebrationDialogState
    extends State<CompletionCelebrationDialog>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late AnimationController _slideController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // تهيئة المتحكمات
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // تهيئة الرسوم المتحركة
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutBack),
    );

    // بدء الرسوم المتحركة
    _startAnimations();
  }

  void _startAnimations() async {
    await Future.delayed(const Duration(milliseconds: 100));
    _fadeController.forward();

    await Future.delayed(const Duration(milliseconds: 200));
    _scaleController.forward();

    await Future.delayed(const Duration(milliseconds: 300));
    _slideController.forward();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Material(
      color: Colors.black.withAlpha(128),
      child: Center(
        child: AnimatedBuilder(
          animation: Listenable.merge([
            _scaleController,
            _fadeController,
            _slideController,
          ]),
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: _buildDialogContent(theme),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildDialogContent(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.all(32),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(51),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أيقونة الاحتفال
          _buildCelebrationIcon(theme),

          const SizedBox(height: 24),

          // رسالة التهنئة الرئيسية
          _buildMainMessage(theme),

          const SizedBox(height: 16),

          // تفاصيل الإنجاز
          _buildAchievementDetails(theme),

          const SizedBox(height: 24),

          // أزرار الإجراءات
          _buildActionButtons(theme),
        ],
      ),
    );
  }

  Widget _buildCelebrationIcon(ThemeData theme) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withAlpha(26),
        shape: BoxShape.circle,
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // تأثير الوهج
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(51),
              shape: BoxShape.circle,
            ),
          ),

          // الأيقونة الرئيسية
          Icon(Icons.celebration, size: 40, color: theme.colorScheme.primary),

          // نجوم متحركة
          ..._buildFloatingStars(theme),
        ],
      ),
    );
  }

  List<Widget> _buildFloatingStars(ThemeData theme) {
    return List.generate(6, (index) {
      final radius = 50.0;

      return AnimatedBuilder(
        animation: _scaleController,
        builder: (context, child) {
          final progress = _scaleController.value;
          final x = radius * progress * (index.isEven ? 1 : -1) * 0.8;
          final y = radius * progress * (index.isOdd ? 1 : -1) * 0.8;

          return Transform.translate(
            offset: Offset(x, y),
            child: Opacity(
              opacity: progress,
              child: Icon(
                Icons.star,
                size: 12,
                color: theme.colorScheme.secondary,
              ),
            ),
          );
        },
      );
    });
  }

  Widget _buildMainMessage(ThemeData theme) {
    return Column(
      children: [
        Text(
          'بارك الله فيك!',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 8),

        Text(
          'لقد أكملت جميع أذكار',
          style: TextStyle(
            fontSize: 16,
            color: theme.colorScheme.onSurface.withAlpha(179),
          ),
          textAlign: TextAlign.center,
        ),

        Text(
          widget.categoryName,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.secondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildAchievementDetails(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withAlpha(13),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.primary.withAlpha(51),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            icon: Icons.check_circle,
            label: 'أذكار مكتملة',
            value: '${widget.completedCount}',
            color: Colors.green,
          ),

          Container(
            width: 1,
            height: 40,
            color: theme.colorScheme.outline.withAlpha(51),
          ),

          _buildStatItem(
            icon: Icons.stars,
            label: 'نقاط مكتسبة',
            value: '+${widget.earnedPoints}',
            color: theme.colorScheme.secondary,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurface.withAlpha(179),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildActionButtons(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onClosed?.call();
            },
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              side: BorderSide(color: theme.colorScheme.primary),
            ),
            child: Text(
              'متابعة',
              style: TextStyle(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),

        const SizedBox(width: 12),

        Expanded(
          child: ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // يمكن إضافة انتقال إلى شاشة الإحصائيات هنا
              widget.onClosed?.call();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
            child: const Text(
              'عرض الإحصائيات',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ),
      ],
    );
  }
}
