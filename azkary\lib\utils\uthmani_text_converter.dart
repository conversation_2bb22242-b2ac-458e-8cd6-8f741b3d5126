import 'package:azkary/utils/logger.dart';

/// أداة مساعدة لتحويل النص القرآني إلى الرسم العثماني
class UthmaniTextConverter {
  /// تحويل النص العادي إلى الرسم العثماني
  static String convertToUthmani(String text) {
    // التحقق من أن النص ليس فارغًا
    if (text.isEmpty) {
      return text;
    }

    try {
      // إضافة بعض العلامات الخاصة بالرسم العثماني
      String uthmaniText = text;

      // إضافة علامة المد فوق الألف في بعض الكلمات المحددة
      uthmaniText = uthmaniText.replaceAll('الرحمن', 'الرحمٰن');
      uthmaniText = uthmaniText.replaceAll('الرحيم', 'الرحيم');
      uthmaniText = uthmaniText.replaceAll('العالمين', 'العٰلمين');
      uthmaniText = uthmaniText.replaceAll('الصراط', 'الصرٰط');

      // إضافة السكون المدورة في بعض المواضع
      uthmaniText = uthmaniText.replaceAll(' من ', ' مِنْ ');
      uthmaniText = uthmaniText.replaceAll(' عن ', ' عَنْ ');

      // إضافة الألف الصغيرة فوق بعض الحروف
      uthmaniText = uthmaniText.replaceAll('هذا', 'هٰذا');
      uthmaniText = uthmaniText.replaceAll('ذلك', 'ذٰلك');

      // تعديل بعض الكلمات الشائعة في القرآن
      uthmaniText = uthmaniText.replaceAll('الله', 'اللّٰه');
      uthmaniText = uthmaniText.replaceAll('إله', 'إلٰه');

      // تعديل بعض الكلمات الإضافية
      uthmaniText = uthmaniText.replaceAll('السموات', 'السمٰوات');
      uthmaniText = uthmaniText.replaceAll('الرحمن', 'الرحمٰن');
      uthmaniText = uthmaniText.replaceAll('الكتاب', 'الكتٰب');
      uthmaniText = uthmaniText.replaceAll('الإنسان', 'الإنسٰن');

      // تعديل بعض الحروف المقطعة
      uthmaniText = uthmaniText.replaceAll('الم', 'الٓمٓ');
      uthmaniText = uthmaniText.replaceAll('المص', 'الٓمٓصٓ');
      uthmaniText = uthmaniText.replaceAll('الر', 'الٓر');
      uthmaniText = uthmaniText.replaceAll('كهيعص', 'كٓهٓيٓعٓصٓ');
      uthmaniText = uthmaniText.replaceAll('طه', 'طٰهٰ');
      uthmaniText = uthmaniText.replaceAll('يس', 'يٓس');

      return uthmaniText;
    } catch (e) {
      // في حالة حدوث أي خطأ، نعيد النص الأصلي
      AppLogger.error('خطأ في تحويل النص إلى الرسم العثماني: $e');
      return text;
    }
  }

  /// تحويل رقم الآية إلى الترقيم العثماني
  static String convertNumberToUthmani(int number) {
    // تحويل الأرقام العربية إلى الأرقام العثمانية
    final Map<String, String> uthmaniNumbers = {
      '0': '٠',
      '1': '١',
      '2': '٢',
      '3': '٣',
      '4': '٤',
      '5': '٥',
      '6': '٦',
      '7': '٧',
      '8': '٨',
      '9': '٩',
    };

    // تحويل الرقم إلى نص
    String numberText = number.toString();

    // تطبيق التحويلات
    uthmaniNumbers.forEach((key, value) {
      numberText = numberText.replaceAll(key, value);
    });

    return numberText;
  }
}
