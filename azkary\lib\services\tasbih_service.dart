import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/tasbih_model.dart';
import '../utils/logger.dart';

/// خدمة إدارة التسبيحات
class TasbihService {
  static const String _tasbihsKey = 'tasbihs';
  static const String _lastIdKey = 'last_tasbih_id';
  static const String _statsKey = 'tasbih_stats';
  static const String _soundEnabledKey = 'tasbih_sound_enabled';
  static const String _vibrationEnabledKey = 'tasbih_vibration_enabled';

  /// الحصول على جميع التسبيحات
  Future<List<Tasbih>> getTasbihs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? tasbihsJson = prefs.getString(_tasbihsKey);

      if (tasbihsJson == null || tasbihsJson.isEmpty) {
        // إذا لم تكن هناك تسبيحات محفوظة، استخدم التسبيحات الافتراضية
        final defaultTasbihs = Tasbih.getDefaultTasbihs();
        await saveTasbihs(defaultTasbihs);
        return defaultTasbihs;
      }

      final List<dynamic> decodedList = jsonDecode(tasbihsJson);
      return decodedList.map((item) => Tasbih.fromMap(item)).toList();
    } catch (e) {
      AppLogger.error('خطأ في استرجاع التسبيحات: $e');
      // في حالة حدوث خطأ، استخدم التسبيحات الافتراضية
      final defaultTasbihs = Tasbih.getDefaultTasbihs();
      await saveTasbihs(defaultTasbihs);
      return defaultTasbihs;
    }
  }

  /// حفظ جميع التسبيحات
  Future<bool> saveTasbihs(List<Tasbih> tasbihs) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<Map<String, dynamic>> tasbihMaps =
          tasbihs.map((tasbih) => tasbih.toMap()).toList();
      await prefs.setString(_tasbihsKey, jsonEncode(tasbihMaps));
      return true;
    } catch (e) {
      AppLogger.error('خطأ في حفظ التسبيحات: $e');
      return false;
    }
  }

  /// إضافة تسبيحة جديدة
  Future<Tasbih?> addTasbih(String text, int targetCount) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // الحصول على آخر معرف مستخدم
      int lastId =
          prefs.getInt(_lastIdKey) ??
          8; // نبدأ من 9 لأن التسبيحات الافتراضية تنتهي عند 8
      int newId = lastId + 1;

      // الحصول على التسبيحات الحالية
      List<Tasbih> tasbihs = await getTasbihs();

      // إنشاء تسبيحة جديدة
      final newTasbih = Tasbih(id: newId, text: text, targetCount: targetCount);

      // إضافة التسبيحة الجديدة
      tasbihs.add(newTasbih);

      // حفظ التسبيحات المحدثة
      await saveTasbihs(tasbihs);

      // حفظ آخر معرف مستخدم
      await prefs.setInt(_lastIdKey, newId);

      return newTasbih;
    } catch (e) {
      AppLogger.error('خطأ في إضافة تسبيحة: $e');
      return null;
    }
  }

  /// تحديث تسبيحة
  Future<bool> updateTasbih(Tasbih tasbih) async {
    try {
      // الحصول على التسبيحات الحالية
      List<Tasbih> tasbihs = await getTasbihs();

      // البحث عن التسبيحة المراد تحديثها
      int index = tasbihs.indexWhere((item) => item.id == tasbih.id);
      if (index == -1) {
        return false; // التسبيحة غير موجودة
      }

      // تحديث التسبيحة
      tasbihs[index] = tasbih;

      // حفظ التسبيحات المحدثة
      return await saveTasbihs(tasbihs);
    } catch (e) {
      AppLogger.error('خطأ في تحديث تسبيحة: $e');
      return false;
    }
  }

  /// حذف تسبيحة
  Future<bool> deleteTasbih(int id) async {
    try {
      // الحصول على التسبيحات الحالية
      List<Tasbih> tasbihs = await getTasbihs();

      // حذف التسبيحة
      tasbihs.removeWhere((tasbih) => tasbih.id == id);

      // حفظ التسبيحات المحدثة
      return await saveTasbihs(tasbihs);
    } catch (e) {
      AppLogger.error('خطأ في حذف تسبيحة: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات التسبيح
  Future<Map<String, int>> getStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? statsJson = prefs.getString(_statsKey);

      if (statsJson == null || statsJson.isEmpty) {
        return {'today': 0, 'week': 0, 'total': 0};
      }

      return Map<String, int>.from(jsonDecode(statsJson));
    } catch (e) {
      AppLogger.error('خطأ في استرجاع إحصائيات التسبيح: $e');
      return {'today': 0, 'week': 0, 'total': 0};
    }
  }

  /// تحديث إحصائيات التسبيح
  Future<bool> updateStats(int count) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // الحصول على الإحصائيات الحالية
      Map<String, int> stats = await getStats();

      // تحديث الإحصائيات
      stats['today'] = (stats['today'] ?? 0) + count;
      stats['week'] = (stats['week'] ?? 0) + count;
      stats['total'] = (stats['total'] ?? 0) + count;

      // حفظ الإحصائيات المحدثة
      await prefs.setString(_statsKey, jsonEncode(stats));

      return true;
    } catch (e) {
      AppLogger.error('خطأ في تحديث إحصائيات التسبيح: $e');
      return false;
    }
  }

  /// الحصول على حالة تفعيل الصوت
  Future<bool> getSoundEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_soundEnabledKey) ?? true;
  }

  /// تعيين حالة تفعيل الصوت
  Future<bool> setSoundEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.setBool(_soundEnabledKey, enabled);
  }

  /// الحصول على حالة تفعيل الاهتزاز
  Future<bool> getVibrationEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_vibrationEnabledKey) ?? true;
  }

  /// تعيين حالة تفعيل الاهتزاز
  Future<bool> setVibrationEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.setBool(_vibrationEnabledKey, enabled);
  }
}
