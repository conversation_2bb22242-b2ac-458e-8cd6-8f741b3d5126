import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// مساعد لتسجيل الأحداث في التطبيق
class AppLogger {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
    ),
  );

  /// تسجيل معلومات
  static void info(String message) {
    _logger.i('💡 $message');
    _printToConsole('💡 $message');
  }

  /// تسجيل تحذير
  static void warning(String message) {
    _logger.w('⚠️ $message');
    _printToConsole('⚠️ $message');
  }

  /// تسجيل خطأ
  static void error(String message) {
    _logger.e('⛔ $message');
    _printToConsole('⛔ $message');
  }

  /// تسجيل نجاح
  static void success(String message) {
    _logger.i('✅ $message');
    _printToConsole('✅ $message');
  }

  /// طباعة إلى وحدة التحكم في وضع التصحيح
  static void _printToConsole(String message) {
    if (kDebugMode) {
      debugPrint(message);
    }
  }
}
