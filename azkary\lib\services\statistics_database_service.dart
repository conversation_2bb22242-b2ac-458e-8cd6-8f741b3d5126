import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/statistics_model.dart';
import '../utils/logger.dart';

/// خدمة قاعدة البيانات للإحصائيات
class StatisticsDatabaseService {
  static Database? _database;
  static const String _databaseName = 'statistics.db';
  static const int _databaseVersion = 1;

  // أسماء الجداول
  static const String _dailyStatsTable = 'daily_statistics';
  static const String _weeklyStatsTable = 'weekly_statistics';
  static const String _monthlyStatsTable = 'monthly_statistics';
  static const String _usageSessionsTable = 'usage_sessions';

  /// الحصول على قاعدة البيانات
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    try {
      final dbPath = await getDatabasesPath();
      final path = join(dbPath, _databaseName);

      AppLogger.info('تهيئة قاعدة بيانات الإحصائيات في: $path');

      return await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _createTables,
        onUpgrade: _upgradeTables,
      );
    } catch (e) {
      AppLogger.error('خطأ في تهيئة قاعدة بيانات الإحصائيات: $e');
      rethrow;
    }
  }

  /// إنشاء الجداول
  Future<void> _createTables(Database db, int version) async {
    try {
      AppLogger.info('إنشاء جداول قاعدة بيانات الإحصائيات');

      // جدول الإحصائيات اليومية
      await db.execute('''
        CREATE TABLE $_dailyStatsTable (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          date TEXT UNIQUE NOT NULL,
          completedAzkar INTEGER DEFAULT 0,
          totalAzkar INTEGER DEFAULT 0,
          completedCategories INTEGER DEFAULT 0,
          totalCategories INTEGER DEFAULT 0,
          points INTEGER DEFAULT 0,
          timeSpent INTEGER DEFAULT 0,
          appOpenCount INTEGER DEFAULT 0,
          tasbihCount INTEGER DEFAULT 0,
          favoritesAdded INTEGER DEFAULT 0,
          quranReadingTimeMinutes INTEGER DEFAULT 0,
          categoryCompletions TEXT DEFAULT '{}',
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL
        )
      ''');

      // جدول الإحصائيات الأسبوعية
      await db.execute('''
        CREATE TABLE $_weeklyStatsTable (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          weekStart TEXT NOT NULL,
          weekEnd TEXT NOT NULL,
          totalAzkarCompleted INTEGER DEFAULT 0,
          totalTimeSpentMinutes INTEGER DEFAULT 0,
          totalAppOpens INTEGER DEFAULT 0,
          totalTasbihCount INTEGER DEFAULT 0,
          totalFavoritesAdded INTEGER DEFAULT 0,
          totalQuranReadingTimeMinutes INTEGER DEFAULT 0,
          categoryCompletions TEXT DEFAULT '{}',
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          UNIQUE(weekStart, weekEnd)
        )
      ''');

      // جدول الإحصائيات الشهرية
      await db.execute('''
        CREATE TABLE $_monthlyStatsTable (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          year INTEGER NOT NULL,
          month INTEGER NOT NULL,
          totalAzkarCompleted INTEGER DEFAULT 0,
          totalTimeSpentMinutes INTEGER DEFAULT 0,
          totalAppOpens INTEGER DEFAULT 0,
          totalTasbihCount INTEGER DEFAULT 0,
          totalFavoritesAdded INTEGER DEFAULT 0,
          totalQuranReadingTimeMinutes INTEGER DEFAULT 0,
          categoryCompletions TEXT DEFAULT '{}',
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          UNIQUE(year, month)
        )
      ''');

      // جدول جلسات الاستخدام
      await db.execute('''
        CREATE TABLE $_usageSessionsTable (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          startTime TEXT NOT NULL,
          endTime TEXT,
          activityType TEXT NOT NULL,
          categoryName TEXT,
          durationMinutes INTEGER DEFAULT 0,
          metadata TEXT DEFAULT '{}'
        )
      ''');

      // إنشاء فهارس لتحسين الأداء
      await db.execute('CREATE INDEX idx_daily_date ON $_dailyStatsTable(date)');
      await db.execute('CREATE INDEX idx_weekly_dates ON $_weeklyStatsTable(weekStart, weekEnd)');
      await db.execute('CREATE INDEX idx_monthly_year_month ON $_monthlyStatsTable(year, month)');
      await db.execute('CREATE INDEX idx_sessions_activity ON $_usageSessionsTable(activityType)');
      await db.execute('CREATE INDEX idx_sessions_start_time ON $_usageSessionsTable(startTime)');

      AppLogger.info('تم إنشاء جداول قاعدة بيانات الإحصائيات بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في إنشاء جداول قاعدة بيانات الإحصائيات: $e');
      rethrow;
    }
  }

  /// ترقية الجداول
  Future<void> _upgradeTables(Database db, int oldVersion, int newVersion) async {
    AppLogger.info('ترقية قاعدة بيانات الإحصائيات من الإصدار $oldVersion إلى $newVersion');
    // سيتم إضافة منطق الترقية هنا عند الحاجة
  }

  /// حفظ الإحصائيات اليومية
  Future<int> saveDailyStatistics(DailyStatistics stats) async {
    try {
      final db = await database;
      final map = stats.toMap();
      
      AppLogger.info('حفظ الإحصائيات اليومية لتاريخ: ${stats.date}');
      
      return await db.insert(
        _dailyStatsTable,
        map,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      AppLogger.error('خطأ في حفظ الإحصائيات اليومية: $e');
      rethrow;
    }
  }

  /// تحديث الإحصائيات اليومية
  Future<int> updateDailyStatistics(DailyStatistics stats) async {
    try {
      final db = await database;
      final map = stats.toMap();
      map['updatedAt'] = DateTime.now().toIso8601String();
      
      AppLogger.info('تحديث الإحصائيات اليومية لتاريخ: ${stats.date}');
      
      return await db.update(
        _dailyStatsTable,
        map,
        where: 'date = ?',
        whereArgs: [stats.date.toIso8601String().split('T')[0]],
      );
    } catch (e) {
      AppLogger.error('خطأ في تحديث الإحصائيات اليومية: $e');
      rethrow;
    }
  }

  /// الحصول على الإحصائيات اليومية
  Future<DailyStatistics?> getDailyStatistics(DateTime date) async {
    try {
      final db = await database;
      final dateStr = date.toIso8601String().split('T')[0];
      
      final List<Map<String, dynamic>> maps = await db.query(
        _dailyStatsTable,
        where: 'date = ?',
        whereArgs: [dateStr],
      );

      if (maps.isNotEmpty) {
        return DailyStatistics.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الإحصائيات اليومية: $e');
      return null;
    }
  }

  /// الحصول على الإحصائيات اليومية لفترة
  Future<List<DailyStatistics>> getDailyStatisticsRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final db = await database;
      final startDateStr = startDate.toIso8601String().split('T')[0];
      final endDateStr = endDate.toIso8601String().split('T')[0];
      
      final List<Map<String, dynamic>> maps = await db.query(
        _dailyStatsTable,
        where: 'date BETWEEN ? AND ?',
        whereArgs: [startDateStr, endDateStr],
        orderBy: 'date ASC',
      );

      return maps.map((map) => DailyStatistics.fromMap(map)).toList();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الإحصائيات اليومية للفترة: $e');
      return [];
    }
  }

  /// حذف الإحصائيات القديمة (أكثر من سنة)
  Future<int> deleteOldStatistics() async {
    try {
      final db = await database;
      final oneYearAgo = DateTime.now().subtract(const Duration(days: 365));
      final dateStr = oneYearAgo.toIso8601String().split('T')[0];
      
      AppLogger.info('حذف الإحصائيات الأقدم من: $dateStr');
      
      return await db.delete(
        _dailyStatsTable,
        where: 'date < ?',
        whereArgs: [dateStr],
      );
    } catch (e) {
      AppLogger.error('خطأ في حذف الإحصائيات القديمة: $e');
      return 0;
    }
  }

  /// إغلاق قاعدة البيانات
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      AppLogger.info('تم إغلاق قاعدة بيانات الإحصائيات');
    }
  }

  /// حفظ جلسة استخدام
  Future<int> saveUsageSession(Map<String, dynamic> session) async {
    try {
      final db = await database;
      return await db.insert(_usageSessionsTable, session);
    } catch (e) {
      AppLogger.error('خطأ في حفظ جلسة الاستخدام: $e');
      rethrow;
    }
  }

  /// تحديث جلسة استخدام
  Future<int> updateUsageSession(int id, Map<String, dynamic> session) async {
    try {
      final db = await database;
      return await db.update(
        _usageSessionsTable,
        session,
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      AppLogger.error('خطأ في تحديث جلسة الاستخدام: $e');
      rethrow;
    }
  }

  /// الحصول على جلسات الاستخدام لفترة
  Future<List<Map<String, dynamic>>> getUsageSessionsRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final db = await database;
      final startDateStr = startDate.toIso8601String();
      final endDateStr = endDate.toIso8601String();
      
      return await db.query(
        _usageSessionsTable,
        where: 'startTime BETWEEN ? AND ?',
        whereArgs: [startDateStr, endDateStr],
        orderBy: 'startTime DESC',
      );
    } catch (e) {
      AppLogger.error('خطأ في الحصول على جلسات الاستخدام: $e');
      return [];
    }
  }
}
