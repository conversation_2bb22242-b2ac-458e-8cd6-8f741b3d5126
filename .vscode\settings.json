{"java.configuration.updateBuildConfiguration": "interactive", "dart.flutterSdkPath": ".fvm/flutter_sdk", "search.exclude": {"**/.fvm": true}, "files.watcherExclude": {"**/.fvm": true}, "dart.projectRootPath": "<PERSON>zkar<PERSON>", "dart.runPubGetOnPubspecChanges": "always", "dart.flutterHotReloadOnSave": "all", "dart.hotReloadOnSave": "all", "dart.previewFlutterUiGuides": true, "dart.openDevTools": "flutter", "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "first", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off", "cmake.sourceDirectory": "C:/Users/<USER>/Documents/augment-projects/azkary/azkary/linux", "augment.completions.enableAutomaticCompletions": false, "augment.completions.enableQuickSuggestions": false}