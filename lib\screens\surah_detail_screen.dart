import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../models/quran_model.dart';
import '../services/quran_provider.dart';
import '../services/theme_provider.dart';
import '../models/quran_view_mode.dart';

/// شاشة عرض تفاصيل السورة
class SurahDetailScreen extends StatefulWidget {
  final Surah surah;

  const SurahDetailScreen({super.key, required this.surah});

  @override
  State<SurahDetailScreen> createState() => _SurahDetailScreenState();
}

class _SurahDetailScreenState extends State<SurahDetailScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // تحميل آيات السورة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAyahs();
    });
  }

  /// تحميل آيات السورة
  Future<void> _loadAyahs() async {
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);
    if (!_isInitialized) {
      await quranProvider.loadAyahs(widget.surah.number);
      _isInitialized = true;
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Column(
          children: [
            Text(widget.surah.name),
            Text(
              '${widget.surah.englishName} - ${widget.surah.numberOfAyahs} آية',
              style: TextStyle(fontSize: 12),
            ),
          ],
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: theme.colorScheme.onSurface,
              size: 24,
            ),
            tooltip: 'تحديث',
            onPressed: () {
              HapticFeedback.lightImpact();
              final quranProvider = Provider.of<QuranProvider>(
                context,
                listen: false,
              );
              quranProvider.loadAyahs(widget.surah.number);
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          // خلفية إسلامية
          Positioned.fill(
            child: Opacity(
              opacity: 0.05,
              child: Container(
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withAlpha(26),
                ),
              ),
            ),
          ),

          // محتوى الصفحة
          Consumer<QuranProvider>(
            builder: (context, quranProvider, child) {
              if (quranProvider.isLoading) {
                return _buildLoadingState();
              }

              if (quranProvider.error.isNotEmpty) {
                return _buildErrorState(quranProvider.error);
              }

              if (quranProvider.currentAyahs.isEmpty) {
                return _buildEmptyState();
              }

              return _buildAyahsList(quranProvider.currentAyahs);
            },
          ),
        ],
      ),
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 10,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Container(
            height: 120,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      },
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                final quranProvider = Provider.of<QuranProvider>(
                  context,
                  listen: false,
                );
                quranProvider.loadAyahs(widget.surah.number);
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة القائمة الفارغة
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.menu_book,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد آيات متاحة',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                final quranProvider = Provider.of<QuranProvider>(
                  context,
                  listen: false,
                );
                quranProvider.loadAyahs(widget.surah.number);
              },
              icon: const Icon(Icons.refresh),
              label: const Text('تحديث'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة الآيات
  Widget _buildAyahsList(List<Ayah> ayahs) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: ayahs.length,
      itemBuilder: (context, index) {
        final ayah = ayahs[index];
        return _buildAyahCard(ayah);
      },
    );
  }

  /// بناء بطاقة الآية
  Widget _buildAyahCard(Ayah ayah) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isUthmaniMode = themeProvider.quranViewMode == QuranViewMode.uthmaniContinuous;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: isDarkMode ? const Color(0xFF1E2732) : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // رقم الآية
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary
                        .withAlpha(26), // 0.1 * 255 = ~26
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: theme.colorScheme.primary
                          .withAlpha(77), // 0.3 * 255 = ~77
                      width: 1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      '${ayah.numberInSurah}',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
                const Spacer(),
                // أزرار الإجراءات
                IconButton(
                  icon: Icon(
                    Icons.copy,
                    size: 20,
                    color: theme.colorScheme.primary,
                  ),
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: ayah.text));
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم نسخ الآية'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
                  tooltip: 'نسخ',
                ),
                IconButton(
                  icon: Icon(
                    Icons.share,
                    size: 20,
                    color: theme.colorScheme.primary,
                  ),
                  onPressed: () {
                    Share.share(ayah.text);
                  },
                  tooltip: 'مشاركة',
                ),
              ],
            ),
            const SizedBox(height: 16),
            // نص الآية
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
              child: Text(
                ayah.text,
                style: TextStyle(
                  fontSize: isUthmaniMode ? 24 : 22, // Slightly larger font for Uthmani
                  height: 1.8,
                  letterSpacing: 0.5,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.white : Colors.black87,
                  fontFamily: isUthmaniMode ? 'Uthmani' : null, // Apply Uthmani font
                ),
                textAlign: TextAlign.center,
                textDirection: TextDirection.rtl,
              ),
            ),
            if (ayah.sajda) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.amber.withAlpha(51), // 0.2 * 255 = ~51
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.volunteer_activism,
                      size: 16,
                      color: Colors.amber.shade800,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'سجدة',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.amber.shade800,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
