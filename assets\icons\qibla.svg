<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Compass circle -->
        <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="1.5" fill="none"/>
        
        <!-- <PERSON><PERSON> in center -->
        <rect x="10" y="10" width="4" height="4" fill="currentColor"/>
        
        <!-- Compass needle -->
        <path d="M12,3 L14,12 L12,14 L10,12 L12,3 Z" fill="currentColor" fill-opacity="0.7"/>
        <path d="M12,21 L10,12 L12,10 L14,12 L12,21 Z" fill="currentColor" fill-opacity="0.3"/>
        
        <!-- Cardinal directions -->
        <path d="M12,2 L12,4" stroke="currentColor" stroke-width="1" stroke-linecap="round"/>
        <path d="M12,20 L12,22" stroke="currentColor" stroke-width="1" stroke-linecap="round"/>
        <path d="M2,12 L4,12" stroke="currentColor" stroke-width="1" stroke-linecap="round"/>
        <path d="M20,12 L22,12" stroke="currentColor" stroke-width="1" stroke-linecap="round"/>
        
        <!-- Decorative elements -->
        <circle cx="12" cy="12" r="1" fill="currentColor"/>
        <path d="M12,7 L12.4,8 L13.5,8 L12.6,8.5 L13,9.5 L12,9 L11,9.5 L11.4,8.5 L10.5,8 L11.6,8 Z" fill="currentColor" fill-opacity="0.5"/>
    </g>
</svg>
