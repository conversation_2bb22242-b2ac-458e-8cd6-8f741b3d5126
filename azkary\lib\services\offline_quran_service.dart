import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../models/quran_model.dart';
import '../utils/logger.dart';

/// خدمة تحميل القرآن للاستخدام بدون إنترنت
class OfflineQuranService {
  static const String _downloadedSurahsKey = 'downloaded_surahs_data';
  static const String _downloadedTafsirKey = 'downloaded_tafsir_data';
  static const String _downloadStatusKey = 'download_status';

  /// تحميل سورة واحدة وحفظها محلياً
  Future<bool> downloadSurah(int surahNumber) async {
    try {
      AppLogger.info('بدء تحميل السورة رقم $surahNumber');

      // محاولة تحميل من API
      final apiUrl =
          'https://api.alquran.cloud/v1/surah/$surahNumber/ar.alafasy';
      final response = await http
          .get(Uri.parse(apiUrl))
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);

        if (jsonData['data'] != null && jsonData['data']['ayahs'] != null) {
          // حفظ بيانات السورة محلياً
          await _saveSurahData(surahNumber, jsonData['data']);
          AppLogger.info('تم تحميل وحفظ السورة رقم $surahNumber بنجاح');
          return true;
        }
      }

      AppLogger.warning('فشل في تحميل السورة رقم $surahNumber من API');
      return false;
    } catch (e) {
      AppLogger.error('خطأ في تحميل السورة رقم $surahNumber: $e');
      return false;
    }
  }

  /// تحميل تفسير سورة واحدة وحفظه محلياً
  Future<bool> downloadSurahTafsir(int surahNumber) async {
    try {
      AppLogger.info('بدء تحميل تفسير السورة رقم $surahNumber');

      // تحميل التفسير من API
      final tafsirUrl =
          'https://api.alquran.cloud/v1/surah/$surahNumber/ar.muyassar';
      final response = await http
          .get(Uri.parse(tafsirUrl))
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);

        if (jsonData['data'] != null && jsonData['data']['ayahs'] != null) {
          // حفظ بيانات التفسير محلياً
          await _saveTafsirData(surahNumber, jsonData['data']);
          AppLogger.info('تم تحميل وحفظ تفسير السورة رقم $surahNumber بنجاح');
          return true;
        }
      }

      AppLogger.warning('فشل في تحميل تفسير السورة رقم $surahNumber من API');
      return false;
    } catch (e) {
      AppLogger.error('خطأ في تحميل تفسير السورة رقم $surahNumber: $e');
      return false;
    }
  }

  /// حفظ بيانات السورة محلياً
  Future<void> _saveSurahData(
    int surahNumber,
    Map<String, dynamic> surahData,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // حفظ بيانات السورة
      final surahJson = json.encode(surahData);
      await prefs.setString('surah_$surahNumber', surahJson);

      // تحديث قائمة السور المحملة
      final downloadedSurahs = prefs.getStringList(_downloadedSurahsKey) ?? [];
      if (!downloadedSurahs.contains(surahNumber.toString())) {
        downloadedSurahs.add(surahNumber.toString());
        await prefs.setStringList(_downloadedSurahsKey, downloadedSurahs);
      }

      AppLogger.info('تم حفظ بيانات السورة رقم $surahNumber محلياً');
    } catch (e) {
      AppLogger.error('خطأ في حفظ بيانات السورة رقم $surahNumber: $e');
    }
  }

  /// حفظ بيانات التفسير محلياً
  Future<void> _saveTafsirData(
    int surahNumber,
    Map<String, dynamic> tafsirData,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // حفظ بيانات التفسير
      final tafsirJson = json.encode(tafsirData);
      await prefs.setString('tafsir_$surahNumber', tafsirJson);

      // تحديث قائمة التفاسير المحملة
      final downloadedTafsir = prefs.getStringList(_downloadedTafsirKey) ?? [];
      if (!downloadedTafsir.contains(surahNumber.toString())) {
        downloadedTafsir.add(surahNumber.toString());
        await prefs.setStringList(_downloadedTafsirKey, downloadedTafsir);
      }

      AppLogger.info('تم حفظ تفسير السورة رقم $surahNumber محلياً');
    } catch (e) {
      AppLogger.error('خطأ في حفظ تفسير السورة رقم $surahNumber: $e');
    }
  }

  /// تحميل آيات سورة من التخزين المحلي
  Future<List<Ayah>?> getOfflineAyahs(int surahNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final surahJson = prefs.getString('surah_$surahNumber');

      if (surahJson != null) {
        final surahData = json.decode(surahJson);
        if (surahData['ayahs'] != null) {
          final List<dynamic> ayahsJson = surahData['ayahs'];
          return ayahsJson.map((ayahJson) => Ayah.fromJson(ayahJson)).toList();
        }
      }

      return null;
    } catch (e) {
      AppLogger.error(
        'خطأ في تحميل آيات السورة رقم $surahNumber من التخزين المحلي: $e',
      );
      return null;
    }
  }

  /// تحميل تفسير سورة من التخزين المحلي
  Future<List<Ayah>?> getOfflineTafsir(int surahNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tafsirJson = prefs.getString('tafsir_$surahNumber');

      if (tafsirJson != null) {
        final tafsirData = json.decode(tafsirJson);
        if (tafsirData['ayahs'] != null) {
          final List<dynamic> ayahsJson = tafsirData['ayahs'];
          return ayahsJson.map((ayahJson) => Ayah.fromJson(ayahJson)).toList();
        }
      }

      return null;
    } catch (e) {
      AppLogger.error(
        'خطأ في تحميل تفسير السورة رقم $surahNumber من التخزين المحلي: $e',
      );
      return null;
    }
  }

  /// التحقق من وجود سورة محملة محلياً
  Future<bool> isSurahDownloaded(int surahNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final downloadedSurahs = prefs.getStringList(_downloadedSurahsKey) ?? [];
      return downloadedSurahs.contains(surahNumber.toString());
    } catch (e) {
      AppLogger.error('خطأ في التحقق من تحميل السورة رقم $surahNumber: $e');
      return false;
    }
  }

  /// التحقق من وجود تفسير محمل محلياً
  Future<bool> isTafsirDownloaded(int surahNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final downloadedTafsir = prefs.getStringList(_downloadedTafsirKey) ?? [];
      return downloadedTafsir.contains(surahNumber.toString());
    } catch (e) {
      AppLogger.error(
        'خطأ في التحقق من تحميل تفسير السورة رقم $surahNumber: $e',
      );
      return false;
    }
  }

  /// الحصول على قائمة السور المحملة
  Future<List<int>> getDownloadedSurahs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final downloadedSurahs = prefs.getStringList(_downloadedSurahsKey) ?? [];
      return downloadedSurahs
          .map((str) => int.tryParse(str) ?? 0)
          .where((number) => number > 0)
          .toList();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على قائمة السور المحملة: $e');
      return [];
    }
  }

  /// الحصول على قائمة التفاسير المحملة
  Future<List<int>> getDownloadedTafsir() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final downloadedTafsir = prefs.getStringList(_downloadedTafsirKey) ?? [];
      return downloadedTafsir
          .map((str) => int.tryParse(str) ?? 0)
          .where((number) => number > 0)
          .toList();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على قائمة التفاسير المحملة: $e');
      return [];
    }
  }

  /// حذف جميع البيانات المحملة
  Future<void> clearAllDownloads() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // حذف قوائم التحميل
      await prefs.remove(_downloadedSurahsKey);
      await prefs.remove(_downloadedTafsirKey);

      // حذف بيانات السور والتفاسير
      final keys = prefs.getKeys();
      for (final key in keys) {
        if (key.startsWith('surah_') || key.startsWith('tafsir_')) {
          await prefs.remove(key);
        }
      }

      AppLogger.info('تم حذف جميع البيانات المحملة');
    } catch (e) {
      AppLogger.error('خطأ في حذف البيانات المحملة: $e');
    }
  }

  /// حفظ حالة التحميل
  Future<void> saveDownloadStatus(String status) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_downloadStatusKey, status);
    } catch (e) {
      AppLogger.error('خطأ في حفظ حالة التحميل: $e');
    }
  }

  /// الحصول على حالة التحميل
  Future<String?> getDownloadStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_downloadStatusKey);
    } catch (e) {
      AppLogger.error('خطأ في الحصول على حالة التحميل: $e');
      return null;
    }
  }
}
