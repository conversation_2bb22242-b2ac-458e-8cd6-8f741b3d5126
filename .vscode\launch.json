{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "<PERSON><PERSON><PERSON><PERSON> (Flutter)",
            "request": "launch",
            "type": "dart",
            "program": "azkary/lib/main.dart",
            "cwd": "${workspaceFolder}"
        },
        {
            "name": "<PERSON><PERSON><PERSON><PERSON> (Flutter, profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile",
            "program": "azkary/lib/main.dart",
            "cwd": "${workspaceFolder}"
        },
        {
            "name": "<PERSON><PERSON><PERSON><PERSON> (Flutter, release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "program": "azkary/lib/main.dart",
            "cwd": "${workspaceFolder}"
        },
        {
            "name": "<PERSON><PERSON><PERSON><PERSON> (Chrome)",
            "request": "launch",
            "type": "dart",
            "deviceId": "chrome",
            "program": "azkary/lib/main.dart",
            "cwd": "${workspaceFolder}"
        }
    ]
}