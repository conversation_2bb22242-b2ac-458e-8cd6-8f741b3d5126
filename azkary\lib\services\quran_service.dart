import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/quran_model.dart';
import '../utils/logger.dart';
import '../data/quran_data.dart';
import 'offline_quran_service.dart';

/// خدمة للتعامل مع بيانات القرآن الكريم
class QuranService {
  static const String _apiUrl = 'https://api.alquran.cloud/v1/quran/ar.alafasy';
  static const String _localDataKey = 'quran_data';
  static const String _localDataTimestampKey = 'quran_data_timestamp';
  static const int _cacheExpiryDays =
      30; // مدة صلاحية البيانات المخزنة (30 يوم)

  final OfflineQuranService _offlineService = OfflineQuranService();

  /// الحصول على قائمة السور
  Future<List<Surah>> getSurahs() async {
    try {
      // استخدام البيانات المحلية الكاملة (114 سورة) من ملف quran_data.dart
      AppLogger.info('استخدام بيانات السور المحلية الكاملة (114 سورة)');
      return quranSurahs;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على قائمة السور: $e');
      return [];
    }
  }

  /// الحصول على بيانات سورة محددة
  Future<List<Ayah>> getAyahs(int surahNumber) async {
    AppLogger.info('بدء تحميل آيات السورة $surahNumber');

    // أولاً: محاولة التحميل من التخزين المحلي (للاستخدام بدون إنترنت)
    try {
      final offlineAyahs = await _offlineService.getOfflineAyahs(surahNumber);
      if (offlineAyahs != null && offlineAyahs.isNotEmpty) {
        AppLogger.info(
          'تم تحميل آيات السورة $surahNumber من التخزين المحلي (${offlineAyahs.length} آية)',
        );
        return offlineAyahs;
      }
    } catch (e) {
      AppLogger.warning(
        'فشل في تحميل آيات السورة $surahNumber من التخزين المحلي: $e',
      );
    }

    // إذا كانت السورة هي البقرة، نستخدم API مباشرة لتجنب مشاكل الأداء
    if (surahNumber == 2) {
      AppLogger.info('تحميل سورة البقرة من API مباشرة');
      return await getAyahsFromApi(surahNumber);
    }

    try {
      // محاولة تحميل الآيات من ملف الأصول أولاً
      try {
        AppLogger.info('محاولة تحميل آيات السورة $surahNumber من ملف الأصول');
        final jsonString = await rootBundle.loadString(
          'assets/data/surah_$surahNumber.json',
        );

        if (jsonString.isEmpty) {
          AppLogger.warning('ملف الآيات للسورة $surahNumber فارغ');
          throw Exception('ملف الآيات فارغ');
        }

        final jsonData = json.decode(jsonString);

        if (jsonData == null ||
            jsonData['data'] == null ||
            jsonData['data']['ayahs'] == null) {
          AppLogger.warning('بيانات الآيات للسورة $surahNumber غير صالحة');
          throw Exception('بيانات الآيات غير صالحة');
        }

        // استخراج قائمة الآيات من البيانات
        final List<dynamic> ayahsJson = jsonData['data']['ayahs'];
        final ayahs =
            ayahsJson.map((ayahJson) => Ayah.fromJson(ayahJson)).toList();

        if (ayahs.isNotEmpty) {
          AppLogger.info(
            'تم تحميل ${ayahs.length} آية من ملف الأصول للسورة $surahNumber',
          );
          // معالجة البسملة بشكل صحيح
          return _processBismillahInAyahs(ayahs, surahNumber);
        } else {
          AppLogger.warning('لا توجد آيات في ملف السورة $surahNumber');
          throw Exception('لا توجد آيات في الملف');
        }
      } catch (localError) {
        AppLogger.warning(
          'لم يتم العثور على ملف الآيات المحلي للسورة $surahNumber: $localError',
        );
      }

      // إذا فشل التحميل من الملف المحلي، نحاول التحميل من API
      AppLogger.info('محاولة تحميل آيات السورة $surahNumber من API');

      try {
        // استخدام API لتحميل آيات السورة
        final apiUrl =
            'https://api.alquran.cloud/v1/surah/$surahNumber/ar.alafasy';
        AppLogger.info('استدعاء API: $apiUrl');

        final response = await http
            .get(Uri.parse(apiUrl))
            .timeout(const Duration(seconds: 15)); // زيادة مهلة الانتظار

        if (response.statusCode == 200) {
          final responseBody = response.body;

          if (responseBody.isEmpty) {
            AppLogger.warning('استجابة API فارغة للسورة $surahNumber');
            throw Exception('استجابة API فارغة');
          }

          final jsonData = json.decode(responseBody);

          if (jsonData == null ||
              jsonData['data'] == null ||
              jsonData['data']['ayahs'] == null) {
            AppLogger.warning('بيانات API غير صالحة للسورة $surahNumber');
            throw Exception('بيانات API غير صالحة');
          }

          // استخراج قائمة الآيات من البيانات
          final List<dynamic> ayahsJson = jsonData['data']['ayahs'];

          if (ayahsJson.isEmpty) {
            AppLogger.warning(
              'لا توجد آيات في استجابة API للسورة $surahNumber',
            );
            throw Exception('لا توجد آيات في استجابة API');
          }

          // تحويل البيانات إلى آيات
          final ayahs =
              ayahsJson.map((ayahJson) => Ayah.fromJson(ayahJson)).toList();

          AppLogger.info(
            'تم تحميل ${ayahs.length} آية من API للسورة $surahNumber',
          );

          // حفظ الآيات محلياً للاستخدام المستقبلي
          _saveAyahsLocally(surahNumber, jsonData);

          // معالجة البسملة بشكل صحيح
          return _processBismillahInAyahs(ayahs, surahNumber);
        } else {
          AppLogger.error(
            'فشل في تحميل آيات السورة $surahNumber من API: ${response.statusCode}',
          );
          throw Exception(
            'فشل في تحميل آيات السورة من API: ${response.statusCode}',
          );
        }
      } catch (apiError) {
        AppLogger.error('خطأ في استدعاء API للسورة $surahNumber: $apiError');
        throw Exception('خطأ في استدعاء API: $apiError');
      }
    } catch (e) {
      AppLogger.error('خطأ في الحصول على آيات السورة $surahNumber: $e');

      // إذا كانت السورة من السور المحفوظة محلياً، نقوم بإنشاء آياتها يدوياً
      if (surahNumber == 1) {
        return _getDefaultFatihaAyahs();
      } else if (surahNumber == 112) {
        return _getDefaultIkhlasAyahs();
      } else if (surahNumber == 113) {
        return _getDefaultFalaqAyahs();
      } else if (surahNumber == 114) {
        return _getDefaultNasAyahs();
      }

      return [];
    }
  }

  /// الحصول على آيات سورة محددة مباشرة من API
  Future<List<Ayah>> getAyahsFromApi(int surahNumber) async {
    try {
      AppLogger.info('تحميل آيات السورة $surahNumber مباشرة من API');

      // استخدام API لتحميل آيات السورة
      final apiUrl =
          'https://api.alquran.cloud/v1/surah/$surahNumber/ar.alafasy';
      final response = await http
          .get(Uri.parse(apiUrl))
          .timeout(
            const Duration(seconds: 15),
          ); // زيادة مهلة الانتظار للسور الكبيرة

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);

        // استخراج قائمة الآيات من البيانات
        final List<dynamic> ayahsJson = jsonData['data']['ayahs'];

        // تحويل البيانات إلى آيات
        final ayahs =
            ayahsJson.map((ayahJson) => Ayah.fromJson(ayahJson)).toList();

        AppLogger.info(
          'تم تحميل ${ayahs.length} آية من API للسورة $surahNumber',
        );

        // معالجة البسملة بشكل صحيح
        return _processBismillahInAyahs(ayahs, surahNumber);
      } else {
        AppLogger.error(
          'فشل في تحميل آيات السورة $surahNumber من API: ${response.statusCode}',
        );
        throw Exception('فشل في تحميل آيات السورة من API');
      }
    } catch (e) {
      AppLogger.error('خطأ في الحصول على آيات السورة $surahNumber من API: $e');
      rethrow;
    }
  }

  /// معالجة البسملة في الآيات بشكل صحيح
  List<Ayah> _processBismillahInAyahs(List<Ayah> ayahs, int surahNumber) {
    if (ayahs.isEmpty) return ayahs;

    List<Ayah> processedAyahs = [];

    // سورة التوبة لا تحتوي على بسملة
    if (surahNumber == 9) {
      return ayahs;
    }

    // سورة الفاتحة: معالجة خاصة للبسملة
    if (surahNumber == 1) {
      return _processFatihaAyahs(ayahs);
    }

    // باقي السور: فصل البسملة عن الآية الأولى
    for (int i = 0; i < ayahs.length; i++) {
      final ayah = ayahs[i];

      // إذا كانت البسملة كآية منفصلة، نتخطاها
      if (ayah.isBismillah) {
        continue;
      }

      // إذا كانت الآية الأولى وتحتوي على البسملة في النص
      if (i == 0 &&
          ayah.text.startsWith('بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ')) {
        // إزالة البسملة من النص
        final String textWithoutBismillah =
            ayah.text
                .replaceFirst('بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ', '')
                .trim();

        // إذا كان هناك نص بعد البسملة، ننشئ آية جديدة بدون البسملة
        if (textWithoutBismillah.isNotEmpty) {
          final cleanedAyah = Ayah(
            number: ayah.number,
            text: textWithoutBismillah,
            numberInSurah: ayah.numberInSurah,
            juz: ayah.juz,
            page: ayah.page,
            hizbQuarter: ayah.hizbQuarter,
            sajda: ayah.sajda,
            tafsir: ayah.tafsir,
            isBismillah: false,
          );
          processedAyahs.add(cleanedAyah);
        }
      } else {
        // آية عادية، نضيفها كما هي
        processedAyahs.add(ayah);
      }
    }

    AppLogger.info(
      'تمت معالجة البسملة للسورة $surahNumber: ${processedAyahs.length} آية',
    );

    return processedAyahs;
  }

  /// معالجة خاصة لآيات سورة الفاتحة
  List<Ayah> _processFatihaAyahs(List<Ayah> ayahs) {
    if (ayahs.isEmpty) return ayahs;

    List<Ayah> processedAyahs = [];

    for (int i = 0; i < ayahs.length; i++) {
      final ayah = ayahs[i];

      // إذا كانت البسملة كآية منفصلة، نضيفها كما هي
      if (ayah.isBismillah) {
        processedAyahs.add(ayah);
        continue;
      }

      // إذا كانت الآية الأولى وتحتوي على البسملة مدمجة
      if (i == 0 &&
          ayah.text.startsWith('بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ')) {
        // فصل البسملة كآية منفصلة
        final bismillahAyah = Ayah(
          number: 1,
          text: 'بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ',
          numberInSurah: 1,
          juz: ayah.juz,
          page: ayah.page,
          hizbQuarter: ayah.hizbQuarter,
          sajda: false,
          isBismillah: true,
        );
        processedAyahs.add(bismillahAyah);

        // إزالة البسملة من النص الأصلي
        final String textWithoutBismillah =
            ayah.text
                .replaceFirst('بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ', '')
                .trim();

        // إذا كان هناك نص بعد البسملة، ننشئ آية جديدة
        if (textWithoutBismillah.isNotEmpty) {
          final cleanedAyah = Ayah(
            number: 2,
            text: textWithoutBismillah,
            numberInSurah: 2,
            juz: ayah.juz,
            page: ayah.page,
            hizbQuarter: ayah.hizbQuarter,
            sajda: ayah.sajda,
            tafsir: ayah.tafsir,
            isBismillah: false,
          );
          processedAyahs.add(cleanedAyah);
        }
      } else {
        // آية عادية، نضيفها مع تصحيح الترقيم
        final correctedAyah = Ayah(
          number: processedAyahs.length + 1,
          text: ayah.text,
          numberInSurah: processedAyahs.length + 1,
          juz: ayah.juz,
          page: ayah.page,
          hizbQuarter: ayah.hizbQuarter,
          sajda: ayah.sajda,
          tafsir: ayah.tafsir,
          isBismillah: false,
        );
        processedAyahs.add(correctedAyah);
      }
    }

    // تمت معالجة آيات سورة الفاتحة

    return processedAyahs;
  }

  /// حفظ آيات السورة محلياً
  Future<void> _saveAyahsLocally(
    int surahNumber,
    Map<String, dynamic> jsonData,
  ) async {
    try {
      // هذه الدالة ستكون مفيدة في المستقبل لحفظ الآيات محلياً
      // لكن حالياً لن نقوم بتنفيذها لأنها تتطلب صلاحيات كتابة الملفات
      AppLogger.info('تم تخطي حفظ آيات السورة $surahNumber محلياً');
    } catch (e) {
      AppLogger.error('خطأ في حفظ آيات السورة $surahNumber محلياً: $e');
    }
  }

  /// الحصول على تفسير الآيات لسورة محددة
  Future<List<Ayah>> getAyahsWithTafsir(
    int surahNumber,
    List<Ayah> ayahs,
  ) async {
    try {
      AppLogger.info('جاري تحميل تفسير آيات السورة $surahNumber');

      // أولاً: محاولة التحميل من التخزين المحلي
      try {
        final offlineTafsir = await _offlineService.getOfflineTafsir(
          surahNumber,
        );
        if (offlineTafsir != null && offlineTafsir.isNotEmpty) {
          AppLogger.info(
            'تم تحميل تفسير السورة $surahNumber من التخزين المحلي (${offlineTafsir.length} آية)',
          );
          return offlineTafsir;
        }
      } catch (e) {
        AppLogger.warning(
          'فشل في تحميل تفسير السورة $surahNumber من التخزين المحلي: $e',
        );
      }

      // محاولة تحميل التفسير من API
      final apiUrl =
          'https://api.alquran.cloud/v1/surah/$surahNumber/ar.muyassar';
      final response = await http
          .get(Uri.parse(apiUrl))
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final List<dynamic> tafsirAyahsJson = jsonData['data']['ayahs'];

        // قائمة الآيات الجديدة مع التفسير
        final List<Ayah> ayahsWithTafsir = [];

        // إضافة التفسير لكل آية
        for (int i = 0; i < ayahs.length; i++) {
          if (i < tafsirAyahsJson.length) {
            final tafsirText = tafsirAyahsJson[i]['text'];
            ayahsWithTafsir.add(ayahs[i].copyWithTafsir(tafsirText));
          } else {
            ayahsWithTafsir.add(ayahs[i]);
          }
        }

        AppLogger.info(
          'تم تحميل تفسير ${tafsirAyahsJson.length} آية للسورة $surahNumber',
        );
        return ayahsWithTafsir;
      } else {
        AppLogger.error(
          'فشل في تحميل تفسير السورة $surahNumber: ${response.statusCode}',
        );
        return ayahs; // إرجاع الآيات بدون تفسير
      }
    } catch (e) {
      AppLogger.error('خطأ في الحصول على تفسير السورة $surahNumber: $e');

      // إضافة تفسير افتراضي للسور المحفوظة محلياً
      if (surahNumber == 1) {
        // إضافة تفسير افتراضي لسورة الفاتحة
        final List<Ayah> fatihaAyahs = _getDefaultFatihaAyahs();
        final List<String> tafsirs = [
          "بسم الله الرحمن الرحيم: أبدأ بكل اسم مختص بالله تعالى، وهو الرحمن الرحيم.",
          "الحمد لله رب العالمين: الثناء على الله بصفاته الكاملة وأفعاله الحميدة، وهو مالك الخلق ومربيهم.",
          "الرحمن الرحيم: ذو الرحمة الواسعة لجميع الخلق، وذو الرحمة الخاصة بالمؤمنين.",
          "مالك يوم الدين: هو المتصرف وحده في يوم الجزاء والحساب.",
          "إياك نعبد وإياك نستعين: نخصك وحدك بالعبادة والاستعانة.",
          "اهدنا الصراط المستقيم: دلنا وأرشدنا وثبتنا على الطريق الواضح المستقيم.",
          "صراط الذين أنعمت عليهم غير المغضوب عليهم ولا الضالين: طريق من أنعمت عليهم من النبيين والصديقين والشهداء والصالحين، غير طريق اليهود المغضوب عليهم، ولا النصارى الضالين.",
        ];

        final List<Ayah> ayahsWithTafsir = [];
        for (int i = 0; i < fatihaAyahs.length; i++) {
          ayahsWithTafsir.add(fatihaAyahs[i].copyWithTafsir(tafsirs[i]));
        }

        return ayahsWithTafsir;
      } else if (surahNumber == 112) {
        // إضافة تفسير افتراضي لسورة الإخلاص
        final List<Ayah> ikhlasAyahs = _getDefaultIkhlasAyahs();
        final List<String> tafsirs = [
          "بسم الله الرحمن الرحيم: أبدأ بكل اسم مختص بالله تعالى، وهو الرحمن الرحيم.",
          "قل هو الله أحد: قل يا محمد: هو الله الواحد الأحد المنفرد في ذاته وصفاته وأفعاله.",
          "الله الصمد: هو السيد الذي كمل في سؤدده، المقصود في الحوائج.",
          "لم يلد ولم يولد: لم يكن له ولد ولا والد ولا صاحبة.",
          "ولم يكن له كفوا أحد: ولم يكن له مثيل ولا نظير ولا مساوٍ.",
        ];

        final List<Ayah> ayahsWithTafsir = [];
        for (int i = 0; i < ikhlasAyahs.length; i++) {
          ayahsWithTafsir.add(ikhlasAyahs[i].copyWithTafsir(tafsirs[i]));
        }

        return ayahsWithTafsir;
      }

      // إضافة تفسير افتراضي لباقي الآيات
      final List<Ayah> ayahsWithDefaultTafsir = [];
      for (final ayah in ayahs) {
        final defaultTafsir =
            "تفسير الآية ${ayah.numberInSurah} من سورة $surahNumber سيظهر هنا عند توفر التفاسير.";
        ayahsWithDefaultTafsir.add(ayah.copyWithTafsir(defaultTafsir));
      }

      return ayahsWithDefaultTafsir; // إرجاع الآيات مع تفسير افتراضي
    }
  }

  /// الحصول على آيات سورة الفاتحة الافتراضية
  List<Ayah> _getDefaultFatihaAyahs() {
    return [
      Ayah(
        number: 1,
        text: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        numberInSurah: 1,
        juz: 1,
        page: 1,
        hizbQuarter: 1,
        sajda: false,
        isBismillah: true,
      ),
      Ayah(
        number: 2,
        text: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
        numberInSurah: 2,
        juz: 1,
        page: 1,
        hizbQuarter: 1,
        sajda: false,
      ),
      Ayah(
        number: 3,
        text: 'الرَّحْمَٰنِ الرَّحِيمِ',
        numberInSurah: 3,
        juz: 1,
        page: 1,
        hizbQuarter: 1,
        sajda: false,
      ),
      Ayah(
        number: 4,
        text: 'مَالِكِ يَوْمِ الدِّينِ',
        numberInSurah: 4,
        juz: 1,
        page: 1,
        hizbQuarter: 1,
        sajda: false,
      ),
      Ayah(
        number: 5,
        text: 'إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ',
        numberInSurah: 5,
        juz: 1,
        page: 1,
        hizbQuarter: 1,
        sajda: false,
      ),
      Ayah(
        number: 6,
        text: 'اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ',
        numberInSurah: 6,
        juz: 1,
        page: 1,
        hizbQuarter: 1,
        sajda: false,
      ),
      Ayah(
        number: 7,
        text:
            'صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ',
        numberInSurah: 7,
        juz: 1,
        page: 1,
        hizbQuarter: 1,
        sajda: false,
      ),
    ];
  }

  /// الحصول على آيات سورة الإخلاص الافتراضية
  List<Ayah> _getDefaultIkhlasAyahs() {
    return [
      Ayah(
        number: 6222,
        text: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        numberInSurah: 0,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
        isBismillah: true,
      ),
      Ayah(
        number: 6223,
        text: 'قُلْ هُوَ اللَّهُ أَحَدٌ',
        numberInSurah: 1,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
      ),
      Ayah(
        number: 6224,
        text: 'اللَّهُ الصَّمَدُ',
        numberInSurah: 2,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
      ),
      Ayah(
        number: 6225,
        text: 'لَمْ يَلِدْ وَلَمْ يُولَدْ',
        numberInSurah: 3,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
      ),
      Ayah(
        number: 6226,
        text: 'وَلَمْ يَكُن لَّهُ كُفُوًا أَحَدٌ',
        numberInSurah: 4,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
      ),
    ];
  }

  /// الحصول على آيات سورة الفلق الافتراضية
  List<Ayah> _getDefaultFalaqAyahs() {
    return [
      Ayah(
        number: 6227,
        text: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        numberInSurah: 0,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
        isBismillah: true,
      ),
      Ayah(
        number: 6228,
        text: 'قُلْ أَعُوذُ بِرَبِّ الْفَلَقِ',
        numberInSurah: 1,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
      ),
      Ayah(
        number: 6229,
        text: 'مِن شَرِّ مَا خَلَقَ',
        numberInSurah: 2,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
      ),
      Ayah(
        number: 6230,
        text: 'وَمِن شَرِّ غَاسِقٍ إِذَا وَقَبَ',
        numberInSurah: 3,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
      ),
      Ayah(
        number: 6231,
        text: 'وَمِن شَرِّ النَّفَّاثَاتِ فِي الْعُقَدِ',
        numberInSurah: 4,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
      ),
      Ayah(
        number: 6232,
        text: 'وَمِن شَرِّ حَاسِدٍ إِذَا حَسَدَ',
        numberInSurah: 5,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
      ),
    ];
  }

  /// الحصول على آيات سورة الناس الافتراضية
  List<Ayah> _getDefaultNasAyahs() {
    return [
      Ayah(
        number: 6233,
        text: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        numberInSurah: 0,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
        isBismillah: true,
      ),
      Ayah(
        number: 6234,
        text: 'قُلْ أَعُوذُ بِرَبِّ النَّاسِ',
        numberInSurah: 1,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
      ),
      Ayah(
        number: 6235,
        text: 'مَلِكِ النَّاسِ',
        numberInSurah: 2,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
      ),
      Ayah(
        number: 6236,
        text: 'إِلَٰهِ النَّاسِ',
        numberInSurah: 3,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
      ),
      Ayah(
        number: 6237,
        text: 'مِن شَرِّ الْوَسْوَاسِ الْخَنَّاسِ',
        numberInSurah: 4,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
      ),
      Ayah(
        number: 6238,
        text: 'الَّذِي يُوَسْوِسُ فِي صُدُورِ النَّاسِ',
        numberInSurah: 5,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
      ),
      Ayah(
        number: 6239,
        text: 'مِنَ الْجِنَّةِ وَالنَّاسِ',
        numberInSurah: 6,
        juz: 30,
        page: 604,
        hizbQuarter: 240,
        sajda: false,
      ),
    ];
  }

  /// الحصول على بيانات القرآن الكريم (من الإنترنت أو التخزين المحلي)
  // ignore: unused_element
  Future<QuranData> _getQuranData() async {
    // التحقق من وجود بيانات مخزنة محلياً
    final prefs = await SharedPreferences.getInstance();
    final hasLocalData = prefs.containsKey(_localDataKey);
    final timestamp = prefs.getInt(_localDataTimestampKey) ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;
    final isExpired = now - timestamp > _cacheExpiryDays * 24 * 60 * 60 * 1000;

    // إذا كانت البيانات موجودة ولم تنتهي صلاحيتها، نستخدمها
    if (hasLocalData && !isExpired) {
      AppLogger.info('استخدام بيانات القرآن المخزنة محلياً');
      final jsonString = prefs.getString(_localDataKey)!;
      return QuranData.fromJson(json.decode(jsonString));
    }

    // محاولة تحميل البيانات من الإنترنت
    try {
      AppLogger.info('محاولة تحميل بيانات القرآن من الإنترنت');
      final response = await http
          .get(Uri.parse(_apiUrl))
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final quranData = QuranData.fromJson(jsonData);

        // تخزين البيانات محلياً
        await prefs.setString(_localDataKey, response.body);
        await prefs.setInt(_localDataTimestampKey, now);

        AppLogger.info('تم تحميل وتخزين بيانات القرآن بنجاح');
        return quranData;
      } else {
        throw Exception('فشل في تحميل البيانات: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل بيانات القرآن من الإنترنت: $e');

      // إذا كانت هناك بيانات مخزنة (حتى لو منتهية الصلاحية)، نستخدمها كخطة بديلة
      if (hasLocalData) {
        AppLogger.info('استخدام بيانات القرآن المخزنة محلياً كخطة بديلة');
        final jsonString = prefs.getString(_localDataKey)!;
        return QuranData.fromJson(json.decode(jsonString));
      }

      // إذا لم تكن هناك بيانات مخزنة، نحاول تحميل البيانات من ملف الأصول
      return _loadQuranDataFromAssets();
    }
  }

  /// تحميل بيانات القرآن من ملف الأصول
  // ignore: unused_element
  Future<QuranData> _loadQuranDataFromAssets() async {
    try {
      AppLogger.info('تحميل بيانات القرآن من ملف الأصول');
      final jsonString = await rootBundle.loadString('assets/data/quran.json');
      return QuranData.fromJson(json.decode(jsonString));
    } catch (e) {
      AppLogger.error('خطأ في تحميل بيانات القرآن من ملف الأصول: $e');
      // إذا فشلت جميع المحاولات، نعيد بيانات فارغة
      return QuranData(surahs: []);
    }
  }
}
