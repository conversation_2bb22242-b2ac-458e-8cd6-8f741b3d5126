import 'package:shared_preferences/shared_preferences.dart';
import '../constants/allah_names_data.dart';

/// خدمة لإدارة المحتوى اليومي في التطبيق
class DailyContentService {
  // مفاتيح التخزين المحلي
  static const String _lastUpdateDateKey = 'last_update_date';
  static const String _dailyAllahNameIndexKey = 'daily_allah_name_index';

  /// الحصول على فهرس اسم الله اليومي
  static int getDailyAllahNameIndex() {
    // التحقق من تاريخ آخر تحديث
    final DateTime now = DateTime.now();
    final String today = _formatDate(now);

    // محاولة استرجاع تاريخ آخر تحديث من التخزين المحلي
    final String? lastUpdateDate = _getLastUpdateDate();

    // إذا كان تاريخ آخر تحديث مختلفاً عن اليوم الحالي، قم بتحديث المحتوى اليومي
    if (lastUpdateDate != today) {
      return _updateDailyAllahNameIndex(today);
    }

    // استرجاع فهرس اسم الله اليومي من التخزين المحلي
    final int? storedIndex = _getDailyAllahNameIndexFromPrefs();

    // إذا لم يتم العثور على فهرس مخزن، قم بتحديث المحتوى اليومي
    if (storedIndex == null) {
      return _updateDailyAllahNameIndex(today);
    }

    return storedIndex;
  }

  /// تحديث فهرس اسم الله اليومي
  static int _updateDailyAllahNameIndex(String today) {
    // اختيار فهرس عشوائي من أسماء الله الحسنى
    final int newIndex = _getRandomIndex(allahNamesData.length);

    // حفظ الفهرس الجديد وتاريخ التحديث في التخزين المحلي
    _saveDailyAllahNameIndex(newIndex);
    _saveLastUpdateDate(today);

    return newIndex;
  }

  /// الحصول على فهرس عشوائي
  static int _getRandomIndex(int max) {
    // استخدام تاريخ اليوم كبذرة للعشوائية
    final DateTime now = DateTime.now();
    final int seed = now.year * 10000 + now.month * 100 + now.day;

    // حساب فهرس شبه عشوائي باستخدام البذرة
    return seed % max;
  }

  /// تنسيق التاريخ كسلسلة نصية
  static String _formatDate(DateTime date) {
    return '${date.year}-${date.month}-${date.day}';
  }

  /// الحصول على تاريخ آخر تحديث من التخزين المحلي
  static String? _getLastUpdateDate() {
    try {
      // استخدام طريقة بديلة للحصول على تاريخ آخر تحديث
      // في حالة عدم توفر SharedPreferences، نستخدم التاريخ الحالي
      return null;
    } catch (e) {
      return null;
    }
  }

  /// حفظ تاريخ آخر تحديث في التخزين المحلي
  static Future<void> _saveLastUpdateDate(String date) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastUpdateDateKey, date);
    } catch (e) {
      // تجاهل الأخطاء
    }
  }

  /// الحصول على فهرس اسم الله اليومي من التخزين المحلي
  static int? _getDailyAllahNameIndexFromPrefs() {
    try {
      // استخدام طريقة بديلة للحصول على فهرس اسم الله اليومي
      // في حالة عدم توفر SharedPreferences، نستخدم فهرس عشوائي
      return null;
    } catch (e) {
      return null;
    }
  }

  /// حفظ فهرس اسم الله اليومي في التخزين المحلي
  static Future<void> _saveDailyAllahNameIndex(int index) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_dailyAllahNameIndexKey, index);
    } catch (e) {
      // تجاهل الأخطاء
    }
  }
}
