import 'package:flutter/material.dart';

/// أنواع الإنجازات
enum AchievementType {
  streak, // سلسلة أيام متتالية
  daily, // إنجازات يومية
  weekly, // إنجازات أسبوعية
  monthly, // إنجازات شهرية
  total, // إنجازات إجمالية
  special, // إنجازات خاصة
}

/// مستويات الإنجازات
enum AchievementLevel {
  bronze, // برونزي
  silver, // فضي
  gold, // ذهبي
  platinum, // بلاتيني
  diamond, // ماسي
}

/// نموذج بيانات الإنجاز
class Achievement {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final AchievementType type;
  final AchievementLevel level;
  final int targetValue;
  final int currentValue;
  final int points;
  final bool isUnlocked;
  final DateTime? unlockedDate;
  final Color color;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.type,
    required this.level,
    required this.targetValue,
    required this.currentValue,
    required this.points,
    required this.isUnlocked,
    this.unlockedDate,
    required this.color,
  });

  /// نسبة التقدم (0.0 إلى 1.0)
  double get progress => targetValue > 0 ? currentValue / targetValue : 0.0;

  /// ما إذا كان الإنجاز مكتملاً
  bool get isCompleted => currentValue >= targetValue;

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'iconCodePoint': icon.codePoint,
      'type': type.index,
      'level': level.index,
      'targetValue': targetValue,
      'currentValue': currentValue,
      'points': points,
      'isUnlocked': isUnlocked ? 1 : 0,
      'unlockedDate': unlockedDate?.toIso8601String(),
      'colorValue': color.toARGB32(),
    };
  }

  /// إنشاء من Map
  factory Achievement.fromMap(Map<String, dynamic> map) {
    return Achievement(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      icon: IconData(map['iconCodePoint'], fontFamily: 'MaterialIcons'),
      type: AchievementType.values[map['type']],
      level: AchievementLevel.values[map['level']],
      targetValue: map['targetValue'],
      currentValue: map['currentValue'],
      points: map['points'],
      isUnlocked: map['isUnlocked'] == 1,
      unlockedDate: map['unlockedDate'] != null
          ? DateTime.parse(map['unlockedDate'])
          : null,
      color: Color(map['colorValue']),
    );
  }

  /// نسخ مع تعديل بعض القيم
  Achievement copyWith({
    String? id,
    String? title,
    String? description,
    IconData? icon,
    AchievementType? type,
    AchievementLevel? level,
    int? targetValue,
    int? currentValue,
    int? points,
    bool? isUnlocked,
    DateTime? unlockedDate,
    Color? color,
  }) {
    return Achievement(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      type: type ?? this.type,
      level: level ?? this.level,
      targetValue: targetValue ?? this.targetValue,
      currentValue: currentValue ?? this.currentValue,
      points: points ?? this.points,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      unlockedDate: unlockedDate ?? this.unlockedDate,
      color: color ?? this.color,
    );
  }
}

/// فئة مساعدة لإنشاء الإنجازات المحددة مسبق

