import 'package:flutter/foundation.dart';
import '../models/daily_ayah_model.dart';
import '../models/quran_model.dart';
import 'daily_ayah_service.dart';
import 'quran_service.dart';
import '../utils/logger.dart';

/// مزود بيانات الآية اليومية
class DailyAyahProvider with ChangeNotifier {
  final DailyAyahService _dailyAyahService = DailyAyahService();
  final QuranService _quranService = QuranService();

  DailyAyah? _dailyAyah;
  bool _isLoading = false;
  String _error = '';
  bool _showTafsir = false;

  // متغيرات للتنقل بين الآيات
  int _currentSurahIndex = 0;
  int _currentAyahIndex = 0;
  List<Surah> _surahs = [];
  List<Ayah> _currentSurahAyahs = [];

  /// الآية اليومية
  DailyAyah? get dailyAyah => _dailyAyah;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String get error => _error;

  /// حالة عرض التفسير
  bool get showTafsir => _showTafsir;

  /// تهيئة مزود البيانات
  Future<void> initialize() async {
    await loadDailyAyah();
  }

  /// تحميل الآية اليومية
  Future<void> loadDailyAyah() async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      // تحميل قائمة السور
      _surahs = await _quranService.getSurahs();

      // تحميل الآية اليومية
      _dailyAyah = await _dailyAyahService.getDailyAyah();

      if (_dailyAyah != null) {
        // تحديد السورة والآية الحالية
        _currentSurahIndex = _dailyAyah!.surah.number - 1;
        _currentAyahIndex = _dailyAyah!.ayah.numberInSurah;

        // تحميل آيات السورة الحالية
        _currentSurahAyahs = await _quranService.getAyahs(
          _dailyAyah!.surah.number,
        );
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'حدث خطأ أثناء تحميل الآية اليومية: $e';
      AppLogger.error(_error);
      notifyListeners();
    }
  }

  /// تبديل حالة عرض التفسير
  void toggleTafsir() {
    _showTafsir = !_showTafsir;
    notifyListeners();
  }

  /// تحديث حالة قراءة الآية اليومية
  Future<void> markAyahAsRead() async {
    try {
      // تحديث حالة الآية في الخدمة
      final updatedAyah = await _dailyAyahService.markDailyAyahAsRead();

      if (updatedAyah != null) {
        // تحديث الآية في المزود
        _dailyAyah = updatedAyah;
        notifyListeners();

        AppLogger.info('تم تحديث حالة قراءة الآية في المزود');
      }
    } catch (e) {
      _error = 'حدث خطأ أثناء تحديث حالة قراءة الآية: $e';
      AppLogger.error(_error);
      notifyListeners();
    }
  }

  /// الانتقال إلى الآية التالية
  Future<void> nextAyah() async {
    if (_surahs.isEmpty || _currentSurahAyahs.isEmpty) {
      return;
    }

    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      // زيادة مؤشر الآية
      _currentAyahIndex++;

      // إذا وصلنا إلى نهاية السورة، ننتقل إلى السورة التالية
      if (_currentAyahIndex >= _currentSurahAyahs.length) {
        _currentSurahIndex++;
        _currentAyahIndex = 0;

        // إذا وصلنا إلى نهاية القرآن، نعود إلى البداية
        if (_currentSurahIndex >= _surahs.length) {
          _currentSurahIndex = 0;
        }

        // تحميل آيات السورة الجديدة
        _currentSurahAyahs = await _quranService.getAyahs(
          _surahs[_currentSurahIndex].number,
        );
      }

      // تحديث الآية اليومية
      await _updateCurrentAyah();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'حدث خطأ أثناء تحميل الآية التالية: $e';
      AppLogger.error(_error);
      notifyListeners();
    }
  }

  /// الانتقال إلى الآية السابقة
  Future<void> previousAyah() async {
    if (_surahs.isEmpty || _currentSurahAyahs.isEmpty) {
      return;
    }

    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      // تقليل مؤشر الآية
      _currentAyahIndex--;

      // إذا وصلنا إلى بداية السورة، ننتقل إلى السورة السابقة
      if (_currentAyahIndex < 0) {
        _currentSurahIndex--;

        // إذا وصلنا إلى بداية القرآن، نعود إلى النهاية
        if (_currentSurahIndex < 0) {
          _currentSurahIndex = _surahs.length - 1;
        }

        // تحميل آيات السورة الجديدة
        _currentSurahAyahs = await _quranService.getAyahs(
          _surahs[_currentSurahIndex].number,
        );

        // تحديد آخر آية في السورة
        _currentAyahIndex = _currentSurahAyahs.length - 1;
      }

      // تحديث الآية اليومية
      await _updateCurrentAyah();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'حدث خطأ أثناء تحميل الآية السابقة: $e';
      AppLogger.error(_error);
      notifyListeners();
    }
  }

  /// تحديث الآية الحالية
  Future<void> _updateCurrentAyah() async {
    try {
      if (_currentAyahIndex < 0 ||
          _currentAyahIndex >= _currentSurahAyahs.length) {
        return;
      }

      // الحصول على السورة والآية الحالية
      var surah = _surahs[_currentSurahIndex];
      var ayah = _currentSurahAyahs[_currentAyahIndex];

      // تخطي البسملة إذا كانت آية منفصلة (ما عدا في سورة الفاتحة)
      if (ayah.isBismillah && surah.number != 1) {
        // إذا كانت البسملة، ننتقل إلى الآية التالية
        _currentAyahIndex++;

        // التحقق من صحة رقم الآية الجديد
        if (_currentAyahIndex >= _currentSurahAyahs.length) {
          _currentSurahIndex++;
          _currentAyahIndex = 0;

          // إذا وصلنا إلى نهاية القرآن، نعود إلى البداية
          if (_currentSurahIndex >= _surahs.length) {
            _currentSurahIndex = 0;
          }

          // تحميل آيات السورة الجديدة
          _currentSurahAyahs = await _quranService.getAyahs(
            _surahs[_currentSurahIndex].number,
          );
        }

        // تحديث المتغيرات
        surah = _surahs[_currentSurahIndex];
        ayah = _currentSurahAyahs[_currentAyahIndex];
      }

      // الحصول على تفسير الآية
      final ayahsWithTafsir = await _quranService.getAyahsWithTafsir(
        surah.number,
        [ayah],
      );
      final ayahWithTafsir =
          ayahsWithTafsir.isNotEmpty ? ayahsWithTafsir.first : ayah;

      // إنشاء آية يومية جديدة
      _dailyAyah = DailyAyah(
        surah: surah,
        ayah: ayah,
        date: DateTime.now(),
        tafsir: ayahWithTafsir.tafsir,
      );

      // إعادة تنبيه المستمعين
      notifyListeners();
    } catch (e) {
      AppLogger.error('خطأ في تحديث الآية الحالية: $e');
      _error = 'حدث خطأ أثناء تحديث الآية: $e';
      notifyListeners();
    }
  }
}
