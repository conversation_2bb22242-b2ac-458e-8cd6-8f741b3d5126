import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/azkar_provider.dart';
import '../widgets/zikr_list_item.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/islamic_background.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  @override
  void initState() {
    super.initState();
    // Load favorites when screen initializes
    // Favorites are loaded automatically in the provider constructor
  }

  @override
  Widget build(BuildContext context) {
    // Remove unused variable

    return Scaffold(
      appBar: CustomAppBar(
        title: 'المفضلة',
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: IslamicBackground(
        opacity: 0.03,
        showPattern: true,
        showGradient: false,
        child: Consumer<AzkarProvider>(
          builder: (context, provider, child) {
            if (provider.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (provider.favoriteAzkar.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.favorite_border,
                      size: 64,
                      color: Theme.of(context).colorScheme.primary.withAlpha(
                        128,
                      ), // 0.5 opacity = 128 alpha
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'لا توجد أذكار في المفضلة',
                      style: TextStyle(fontSize: 18),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'يمكنك إضافة الأذكار إلى المفضلة بالضغط على أيقونة القلب',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: provider.favoriteAzkar.length,
              itemBuilder: (context, index) {
                final zikr = provider.favoriteAzkar[index];
                return ZikrListItem(
                  zikr: zikr,
                  onTap: () {
                    // لا نقوم بالانتقال إلى صفحة التفاصيل، بل نترك التوسيع/الطي للعنصر نفسه
                  },
                  onFavoriteToggle: () {
                    provider.toggleFavorite(zikr);
                  },
                  onCounterIncrement: (zikr, count) {
                    provider.updateZikrCount(zikr, count);
                  },
                );
              },
            );
          },
        ),
      ),
    );
  }
}
