import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart' as share_plus;
import 'package:url_launcher/url_launcher.dart';
import 'package:hijri/hijri_calendar.dart';
import 'package:intl/intl.dart';
import '../screens/prayer_times_screen.dart';
import '../screens/qibla_screen.dart';

/// مكون القائمة الجانبية للتطبيق
class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // الحصول على التاريخ الهجري الحالي
    final hijriDate = HijriCalendar.now();
    final hijriDateFormatted =
        '${hijriDate.hDay} ${_getHijriMonthName(hijriDate.hMonth)} ${hijriDate.hYear}';

    // الحصول على التاريخ الميلادي الحالي
    final gregorianDate = DateTime.now();
    final gregorianDateFormatted = DateFormat.yMMMMd(
      'ar',
    ).format(gregorianDate);

    return Drawer(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.white,
      child: Column(
        children: [
          // رأس القائمة
          _buildDrawerHeader(
            context,
            hijriDateFormatted,
            gregorianDateFormatted,
          ),

          // قائمة العناصر
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                // قسم الميزات الإسلامية
                _buildSectionTitle(context, 'الميزات الإسلامية'),

                // أوقات الصلاة
                _buildDrawerItem(
                  context: context,
                  icon: Icons.access_time,
                  title: 'أوقات الصلاة',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PrayerTimesScreen(),
                      ),
                    );
                  },
                ),

                // بوصلة القبلة
                _buildDrawerItem(
                  context: context,
                  icon: Icons.explore,
                  title: 'بوصلة القبلة',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const QiblaScreen(),
                      ),
                    );
                  },
                ),

                const Divider(),

                // قسم المشاركة
                _buildSectionTitle(context, 'مشاركة التطبيق'),

                // مشاركة التطبيق
                _buildDrawerItem(
                  context: context,
                  icon: Icons.share,
                  title: 'مشاركة التطبيق مع الأصدقاء',
                  onTap: () {
                    Navigator.pop(context);
                    _shareApp();
                  },
                ),

                // تقييم التطبيق
                _buildDrawerItem(
                  context: context,
                  icon: Icons.star,
                  title: 'تقييم التطبيق',
                  onTap: () {
                    Navigator.pop(context);
                    _launchAppStore();
                  },
                ),

                const Divider(),

                // معلومات عن التطبيق
                _buildDrawerItem(
                  context: context,
                  icon: Icons.info_outline,
                  title: 'عن التطبيق',
                  onTap: () {
                    Navigator.pop(context);
                    _showAboutDialog(context);
                  },
                ),
              ],
            ),
          ),

          // نسخة التطبيق في أسفل القائمة
          Container(
            padding: const EdgeInsets.symmetric(vertical: 10),
            alignment: Alignment.center,
            child: Text(
              'أذكاري - الإصدار 1.0.0',
              style: TextStyle(
                fontSize: 12,
                color: theme.colorScheme.onSurface.withValues(
                  red: theme.colorScheme.onSurface.r.toDouble(),
                  green: theme.colorScheme.onSurface.g.toDouble(),
                  blue: theme.colorScheme.onSurface.b.toDouble(),
                  alpha: 0.6,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رأس القائمة الجانبية
  Widget _buildDrawerHeader(
    BuildContext context,
    String hijriDate,
    String gregorianDate,
  ) {
    final theme = Theme.of(context);

    return DrawerHeader(
      decoration: BoxDecoration(
        color: theme.colorScheme.primary,
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withValues(
              red: theme.colorScheme.primary.r.toDouble(),
              green: theme.colorScheme.primary.g.toDouble(),
              blue: theme.colorScheme.primary.b.toDouble(),
              alpha: 0.7,
            ),
          ],
        ),
      ),
      padding: EdgeInsets.zero,
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // شعار التطبيق
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.menu_book,
                    color: theme.colorScheme.primary,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'أذكاري',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Spacer(),
            // التاريخ الهجري
            Text(
              hijriDate,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            // التاريخ الميلادي
            Text(
              gregorianDate,
              style: TextStyle(
                color: Colors.white.withValues(
                  red: 255,
                  green: 255,
                  blue: 255,
                  alpha: 0.8,
                ),
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنوان قسم في القائمة
  Widget _buildSectionTitle(BuildContext context, String title) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(right: 16, top: 16, bottom: 8),
      child: Text(
        title,
        style: TextStyle(
          color: theme.colorScheme.primary,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// بناء عنصر في القائمة
  Widget _buildDrawerItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(icon, color: theme.colorScheme.primary, size: 24),
      title: Text(
        title,
        style: TextStyle(fontSize: 16, color: theme.colorScheme.onSurface),
      ),
      onTap: onTap,
      dense: true,
      visualDensity: const VisualDensity(horizontal: -1, vertical: -1),
    );
  }

  /// الحصول على اسم الشهر الهجري
  String _getHijriMonthName(int month) {
    const List<String> months = [
      'محرم',
      'صفر',
      'ربيع الأول',
      'ربيع الثاني',
      'جمادى الأولى',
      'جمادى الآخرة',
      'رجب',
      'شعبان',
      'رمضان',
      'شوال',
      'ذو القعدة',
      'ذو الحجة',
    ];
    return months[month - 1];
  }

  /// مشاركة التطبيق
  void _shareApp() {
    final text =
        'تطبيق أذكاري - تطبيق للأذكار اليومية\n'
        'حمّل التطبيق الآن من متجر جوجل بلاي:\n'
        'https://play.google.com/store/apps/details?id=com.azkary.app';

    share_plus.SharePlus.instance.share(share_plus.ShareParams(text: text));
  }

  /// فتح متجر التطبيقات لتقييم التطبيق
  void _launchAppStore() async {
    final Uri url = Uri.parse(
      'https://play.google.com/store/apps/details?id=com.azkary.app',
    );
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  /// عرض مربع حوار عن التطبيق
  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AboutDialog(
            applicationName: 'أذكاري',
            applicationVersion: '1.0.0',
            applicationIcon: Image.asset(
              'assets/images/app_icon.png',
              width: 48,
              height: 48,
            ),
            children: [
              const Text(
                'تطبيق أذكاري هو تطبيق إسلامي يساعدك على المداومة على الأذكار اليومية بطريقة سهلة وميسرة.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 14),
              ),
            ],
          ),
    );
  }
}
