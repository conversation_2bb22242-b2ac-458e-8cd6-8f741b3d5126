import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// فئة تحسين أداء قاعدة البيانات والتخزين
class DatabaseOptimizer {
  static const int _maxCacheSize = 1000;
  static const Duration _cacheExpiry = Duration(hours: 24);
  static const int _batchSize = 50;

  // تخزين مؤقت في الذاكرة
  static final Map<String, dynamic> _memoryCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};

  /// تحسين استعلامات قاعدة البيانات
  static Map<String, dynamic> optimizeQuery({
    required String table,
    List<String>? columns,
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) {
    return {
      'table': table,
      'columns': columns,
      'where': where,
      'whereArgs': whereArgs,
      'orderBy': orderBy,
      'limit': limit ?? _batchSize, // تحديد حد افتراضي
      'offset': offset,
      'distinct': true, // تجنب التكرار
    };
  }

  /// تحسين عمليات الإدراج المجمعة
  static List<List<Map<String, dynamic>>> batchInserts(
      List<Map<String, dynamic>> data,
      {int batchSize = 50}) {
    final batches = <List<Map<String, dynamic>>>[];
    for (int i = 0; i < data.length; i += batchSize) {
      final end = (i + batchSize < data.length) ? i + batchSize : data.length;
      batches.add(data.sublist(i, end));
    }
    return batches;
  }

  /// تخزين مؤقت محسن
  static void cacheData(String key, dynamic data) {
    if (_memoryCache.length >= _maxCacheSize) {
      _clearOldCache();
    }
    _memoryCache[key] = data;
    _cacheTimestamps[key] = DateTime.now();
  }

  /// استرجاع البيانات من التخزين المؤقت
  static T? getCachedData<T>(String key) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return null;

    if (DateTime.now().difference(timestamp) > _cacheExpiry) {
      _memoryCache.remove(key);
      _cacheTimestamps.remove(key);
      return null;
    }

    return _memoryCache[key] as T?;
  }

  /// تنظيف التخزين المؤقت القديم
  static void _clearOldCache() {
    final now = DateTime.now();
    final keysToRemove = <String>[];

    _cacheTimestamps.forEach((key, timestamp) {
      if (now.difference(timestamp) > _cacheExpiry) {
        keysToRemove.add(key);
      }
    });

    for (final key in keysToRemove) {
      _memoryCache.remove(key);
      _cacheTimestamps.remove(key);
    }

    // إذا كان التخزين المؤقت لا يزال ممتلئاً، احذف أقدم العناصر
    if (_memoryCache.length >= _maxCacheSize) {
      final sortedKeys = _cacheTimestamps.keys.toList()
        ..sort((a, b) => _cacheTimestamps[a]!.compareTo(_cacheTimestamps[b]!));

      final keysToRemoveCount = _memoryCache.length - (_maxCacheSize ~/ 2);
      for (int i = 0; i < keysToRemoveCount && i < sortedKeys.length; i++) {
        final key = sortedKeys[i];
        _memoryCache.remove(key);
        _cacheTimestamps.remove(key);
      }
    }
  }

  /// تحسين عمليات البحث
  static Map<String, dynamic> optimizeSearchQuery({
    required String searchTerm,
    required List<String> searchFields,
    String? table,
    int limit = 50,
  }) {
    final whereConditions = <String>[];
    final whereArgs = <String>[];

    for (final field in searchFields) {
      whereConditions.add('$field LIKE ?');
      whereArgs.add('%$searchTerm%');
    }

    return {
      'table': table,
      'where': whereConditions.join(' OR '),
      'whereArgs': whereArgs,
      'limit': limit,
      'orderBy': 'id DESC', // ترتيب بالأحدث أولاً
    };
  }

  /// تحسين فهرسة قاعدة البيانات
  static List<String> getOptimizedIndexes(String tableName) {
    switch (tableName) {
      case 'azkar':
        return [
          'CREATE INDEX IF NOT EXISTS idx_azkar_category ON azkar(category_id)',
          'CREATE INDEX IF NOT EXISTS idx_azkar_text ON azkar(text)',
          'CREATE INDEX IF NOT EXISTS idx_azkar_favorite ON azkar(is_favorite)',
        ];
      case 'categories':
        return [
          'CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name)',
          'CREATE INDEX IF NOT EXISTS idx_categories_order ON categories(display_order)',
        ];
      case 'statistics':
        return [
          'CREATE INDEX IF NOT EXISTS idx_statistics_date ON statistics(date)',
          'CREATE INDEX IF NOT EXISTS idx_statistics_type ON statistics(type)',
        ];
      default:
        return [];
    }
  }

  /// تحسين عمليات التحديث المجمعة
  static Future<void> batchUpdate({
    required String table,
    required List<Map<String, dynamic>> updates,
    required String whereColumn,
  }) async {
    // تجميع التحديثات لتقليل عدد العمليات
    final batches = batchInserts(updates);

    for (int i = 0; i < batches.length; i++) {
      // معالجة كل مجموعة بشكل منفصل
      await Future.delayed(const Duration(milliseconds: 1)); // تجنب حجب الواجهة
      // يمكن إضافة معالجة فعلية للمجموعة هنا
    }
  }

  /// تحسين إعدادات SharedPreferences
  static Future<void> optimizeSharedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تنظيف المفاتيح القديمة أو غير المستخدمة
      final keys = prefs.getKeys();
      final keysToRemove = <String>[];

      for (final key in keys) {
        // إزالة المفاتيح المؤقتة القديمة
        if (key.startsWith('temp_') || key.startsWith('cache_')) {
          keysToRemove.add(key);
        }
      }

      for (final key in keysToRemove) {
        await prefs.remove(key);
      }
    } catch (e) {
      debugPrint('خطأ في تحسين SharedPreferences: $e');
    }
  }

  /// تحسين استعلامات الإحصائيات
  static Map<String, dynamic> optimizeStatisticsQuery({
    required DateTime startDate,
    required DateTime endDate,
    String? type,
  }) {
    final whereConditions = <String>['date BETWEEN ? AND ?'];
    final whereArgs = <String>[
      startDate.toIso8601String(),
      endDate.toIso8601String(),
    ];

    if (type != null) {
      whereConditions.add('type = ?');
      whereArgs.add(type);
    }

    return {
      'table': 'statistics',
      'where': whereConditions.join(' AND '),
      'whereArgs': whereArgs,
      'orderBy': 'date DESC',
      'limit': 1000, // حد معقول للإحصائيات
    };
  }

  /// تحسين عمليات النسخ الاحتياطي
  static Map<String, dynamic> getBackupSettings() {
    return {
      'includeUserData': true,
      'includeSettings': true,
      'includeStatistics': true,
      'compressionLevel': 6, // توازن بين الحجم والسرعة
      'maxBackupSize': 10 * 1024 * 1024, // 10 ميجابايت
      'autoBackup': false, // تجنب النسخ التلقائي لتوفير الموارد
    };
  }

  /// تحسين عمليات الاستيراد
  static Map<String, dynamic> getImportSettings() {
    return {
      'batchSize': _batchSize,
      'validateData': true,
      'skipDuplicates': true,
      'createIndexes': true,
      'optimizeAfterImport': true,
    };
  }

  /// تحسين أداء الاستعلامات المعقدة
  static String optimizeComplexQuery(String originalQuery) {
    // إضافة تحسينات للاستعلامات المعقدة
    String optimizedQuery = originalQuery;

    // إضافة LIMIT إذا لم يكن موجوداً
    if (!optimizedQuery.toUpperCase().contains('LIMIT')) {
      optimizedQuery += ' LIMIT 100';
    }

    // إضافة فهارس للبحث
    if (optimizedQuery.toUpperCase().contains('WHERE')) {
      // تحسين شروط WHERE
      optimizedQuery = optimizedQuery.replaceAll('LIKE \'%', 'LIKE \'');
    }

    return optimizedQuery;
  }

  /// تحسين إعدادات الاتصال بقاعدة البيانات
  static Map<String, dynamic> getConnectionSettings() {
    return {
      'readOnly': false,
      'singleInstance': true,
      'version': 1,
      'onConfigure': true, // تفعيل الفهارس والتحسينات
      'onCreate': true,
      'onUpgrade': true,
      'onDowngrade': false, // تجنب التراجع لحماية البيانات
    };
  }

  /// تنظيف قاعدة البيانات
  static Future<void> cleanupDatabase() async {
    try {
      // تنظيف البيانات المؤقتة
      _memoryCache.clear();
      _cacheTimestamps.clear();

      // تنظيف SharedPreferences
      await optimizeSharedPreferences();

      debugPrint('تم تنظيف قاعدة البيانات بنجاح');
    } catch (e) {
      debugPrint('خطأ في تنظيف قاعدة البيانات: $e');
    }
  }

  /// مراقبة أداء قاعدة البيانات
  static void monitorPerformance(String operation, Duration duration) {
    if (kDebugMode) {
      if (duration.inMilliseconds > 100) {
        debugPrint(
            'تحذير: العملية $operation استغرقت ${duration.inMilliseconds}ms');
      }
    }
  }

  /// تحسين عمليات الفرز
  static String optimizeSorting(String orderBy, {bool ascending = true}) {
    final direction = ascending ? 'ASC' : 'DESC';

    // إضافة فهرس ثانوي للفرز
    switch (orderBy.toLowerCase()) {
      case 'name':
      case 'title':
        return '$orderBy COLLATE NOCASE $direction, id $direction';
      case 'date':
      case 'created_at':
        return '$orderBy $direction, id $direction';
      default:
        return '$orderBy $direction';
    }
  }

  /// تحسين عمليات العد
  static String optimizeCountQuery(String table, {String? where}) {
    if (where != null) {
      return 'SELECT COUNT(*) FROM $table WHERE $where';
    }
    return 'SELECT COUNT(*) FROM $table';
  }
}
