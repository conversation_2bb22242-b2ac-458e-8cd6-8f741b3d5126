import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة الأصوات للتطبيق
class SoundService {
  static const String _soundEnabledKey = 'sound_enabled';
  static bool _soundEnabled = true;

  /// تحميل إعدادات الصوت
  static Future<void> loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _soundEnabled = prefs.getBool(_soundEnabledKey) ?? true;
  }

  /// حفظ إعدادات الصوت
  static Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_soundEnabledKey, enabled);
  }

  /// التحقق من تفعيل الصوت
  static bool get isSoundEnabled => _soundEnabled;

  /// تشغيل صوت فقاعة عند إخفاء الذكر
  static Future<void> playBubblePopSound() async {
    if (!_soundEnabled) return;
    
    try {
      // تشغيل صوت نظام iOS/Android للفقاعة
      await SystemSound.play(SystemSoundType.click);
      
      // إضافة اهتزاز خفيف مع الصوت
      await HapticFeedback.lightImpact();
      
      // انتظار قصير ثم اهتزاز آخر للتأثير المضاعف
      await Future.delayed(const Duration(milliseconds: 100));
      await HapticFeedback.selectionClick();
    } catch (e) {
      // في حالة فشل تشغيل الصوت، نكتفي بالاهتزاز
      await HapticFeedback.mediumImpact();
    }
  }

  /// تشغيل صوت نجاح عند إكمال الذكر
  static Future<void> playSuccessSound() async {
    if (!_soundEnabled) return;
    
    try {
      // تشغيل صوت نظام للنجاح
      await SystemSound.play(SystemSoundType.alert);
      
      // اهتزاز قوي للنجاح
      await HapticFeedback.heavyImpact();
    } catch (e) {
      // في حالة فشل تشغيل الصوت، نكتفي بالاهتزاز
      await HapticFeedback.heavyImpact();
    }
  }

  /// تشغيل صوت نقر عادي
  static Future<void> playClickSound() async {
    if (!_soundEnabled) return;
    
    try {
      await SystemSound.play(SystemSoundType.click);
      await HapticFeedback.lightImpact();
    } catch (e) {
      await HapticFeedback.lightImpact();
    }
  }

  /// تشغيل صوت تحذير
  static Future<void> playWarningSound() async {
    if (!_soundEnabled) return;
    
    try {
      await SystemSound.play(SystemSoundType.alert);
      await HapticFeedback.mediumImpact();
    } catch (e) {
      await HapticFeedback.mediumImpact();
    }
  }
}
