import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// فئة تحسين أداء واجهة المستخدم
class UIOptimizer {
  // إعدادات محسنة للأداء
  static const Duration _mediumAnimation = Duration(milliseconds: 200);

  static const Curve _fastCurve = Curves.easeOut;

  /// تحسين الانتقالات بين الصفحات
  static PageRouteBuilder<T> optimizedPageRoute<T>({
    required Widget page,
    Duration? duration,
    Curve? curve,
    PageTransitionType type = PageTransitionType.fade,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration ?? _mediumAnimation,
      reverseTransitionDuration: Duration(
        milliseconds:
            (duration?.inMilliseconds ?? _mediumAnimation.inMilliseconds) - 50,
      ),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        switch (type) {
          case PageTransitionType.fade:
            return FadeTransition(
              opacity: animation.drive(
                Tween(begin: 0.0, end: 1.0).chain(
                  CurveTween(curve: curve ?? _fastCurve),
                ),
              ),
              child: child,
            );
          case PageTransitionType.slide:
            return SlideTransition(
              position: animation.drive(
                Tween(begin: const Offset(1.0, 0.0), end: Offset.zero).chain(
                  CurveTween(curve: curve ?? _fastCurve),
                ),
              ),
              child: child,
            );
          case PageTransitionType.scale:
            return ScaleTransition(
              scale: animation.drive(
                Tween(begin: 0.95, end: 1.0).chain(
                  CurveTween(curve: curve ?? _fastCurve),
                ),
              ),
              child: FadeTransition(opacity: animation, child: child),
            );
        }
      },
    );
  }

  /// تحسين القوائم للأداء
  static Widget optimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    bool shrinkWrap = false,
    double? cacheExtent,
  }) {
    return ListView.builder(
      controller: controller,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      physics: physics ?? const ClampingScrollPhysics(),
      padding:
          padding ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      shrinkWrap: shrinkWrap,
      cacheExtent: cacheExtent ?? 250.0, // تقليل التخزين المؤقت
      addAutomaticKeepAlives: false, // تحسين الذاكرة
      addRepaintBoundaries: true, // تحسين الرسم
      addSemanticIndexes: false, // تقليل العمليات الإضافية
    );
  }

  /// تحسين الشبكات للأداء
  static Widget optimizedGridView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required int crossAxisCount,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    double childAspectRatio = 1.0,
    double crossAxisSpacing = 8.0,
    double mainAxisSpacing = 8.0,
  }) {
    return GridView.builder(
      controller: controller,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: crossAxisSpacing,
        mainAxisSpacing: mainAxisSpacing,
      ),
      physics: physics ?? const ClampingScrollPhysics(),
      padding: padding ?? const EdgeInsets.all(8),
      cacheExtent: 250.0,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      addSemanticIndexes: false,
    );
  }

  /// تحسين البطاقات
  static Widget optimizedCard({
    required Widget child,
    EdgeInsets? margin,
    EdgeInsets? padding,
    Color? color,
    double? elevation,
    BorderRadius? borderRadius,
    List<BoxShadow>? boxShadow,
  }) {
    return Container(
      margin: margin ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color ?? Colors.white,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        boxShadow: boxShadow ??
            [
              BoxShadow(
                color: Colors.black.withAlpha(25), // 0.1 * 255
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
      ),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(16),
        child: child,
      ),
    );
  }

  /// تحسين الرسوم المتحركة
  static Widget optimizedAnimatedWidget({
    required Widget child,
    required int index,
    Duration? duration,
    Curve? curve,
    double offset = 10.0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration ?? Duration(milliseconds: 100 + (index * 20)),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: curve ?? _fastCurve,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, offset * (1 - value)),
          child: Opacity(opacity: value, child: child),
        );
      },
      child: child,
    );
  }

  /// تحسين الأزرار
  static Widget optimizedButton({
    required VoidCallback? onPressed,
    required Widget child,
    Color? backgroundColor,
    Color? foregroundColor,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
    double? elevation,
    Size? minimumSize,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        padding:
            padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(8),
        ),
        elevation: elevation ?? 2,
        minimumSize: minimumSize ?? const Size(88, 36),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap, // تقليل منطقة اللمس
      ),
      child: child,
    );
  }

  /// تحسين حقول النص
  static Widget optimizedTextField({
    TextEditingController? controller,
    String? hintText,
    String? labelText,
    IconData? prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixIconPressed,
    TextInputType? keyboardType,
    bool obscureText = false,
    int? maxLines = 1,
    EdgeInsets? contentPadding,
    InputBorder? border,
  }) {
    return TextField(
      controller: controller,
      keyboardType: keyboardType,
      obscureText: obscureText,
      maxLines: maxLines,
      decoration: InputDecoration(
        hintText: hintText,
        labelText: labelText,
        prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
        suffixIcon: suffixIcon != null
            ? IconButton(
                icon: Icon(suffixIcon),
                onPressed: onSuffixIconPressed,
                splashRadius: 20, // تقليل تأثير الضغط
              )
            : null,
        contentPadding: contentPadding ??
            const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
        border: border ??
            OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
        isDense: true, // تقليل الحجم
      ),
    );
  }

  /// تحسين الحوارات
  static Future<T?> showOptimizedDialog<T>({
    required BuildContext context,
    required Widget child,
    bool barrierDismissible = true,
    Color? barrierColor,
    Duration? transitionDuration,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor ?? Colors.black.withAlpha(128), // 0.5 * 255
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: child,
      ),
    );
  }

  /// تحسين القوائم المنسدلة
  static Future<T?> showOptimizedBottomSheet<T>({
    required BuildContext context,
    required Widget child,
    bool isScrollControlled = false,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: isScrollControlled,
      backgroundColor: backgroundColor,
      elevation: elevation ?? 8,
      shape: shape ??
          const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
      builder: (context) => child,
    );
  }

  /// تحسين الصور
  static Widget optimizedImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    bool isAsset = true,
    BorderRadius? borderRadius,
  }) {
    Widget image;

    if (isAsset) {
      image = Image.asset(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        cacheWidth: width?.round(),
        cacheHeight: height?.round(),
        filterQuality: FilterQuality.medium,
        errorBuilder: (context, error, stackTrace) =>
            _buildErrorImage(width, height),
      );
    } else {
      image = Image.network(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        cacheWidth: width?.round(),
        cacheHeight: height?.round(),
        filterQuality: FilterQuality.medium,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return _buildLoadingImage(width, height);
        },
        errorBuilder: (context, error, stackTrace) =>
            _buildErrorImage(width, height),
      );
    }

    if (borderRadius != null) {
      return ClipRRect(
        borderRadius: borderRadius,
        child: image,
      );
    }

    return image;
  }

  /// بناء صورة خطأ
  static Widget _buildErrorImage(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[300],
      child: const Icon(Icons.error, color: Colors.grey),
    );
  }

  /// بناء صورة تحميل
  static Widget _buildLoadingImage(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[100],
      child: const Center(
        child: SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
    );
  }

  /// تحسين الاهتزاز
  static Future<void> optimizedHapticFeedback({
    HapticFeedbackType type = HapticFeedbackType.lightImpact,
  }) async {
    try {
      switch (type) {
        case HapticFeedbackType.lightImpact:
          await HapticFeedback.lightImpact();
          break;
        case HapticFeedbackType.mediumImpact:
          await HapticFeedback.mediumImpact();
          break;
        case HapticFeedbackType.heavyImpact:
          await HapticFeedback.heavyImpact();
          break;
        case HapticFeedbackType.selectionClick:
          await HapticFeedback.selectionClick();
          break;
      }
    } catch (e) {
      // تجاهل أخطاء الاهتزاز
    }
  }

  /// تحسين الألوان
  static Color optimizedColor(Color color, double opacity) {
    return color.withAlpha((opacity * 255).round().clamp(0, 255));
  }

  /// تحسين النصوص
  static TextStyle optimizedTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    String? fontFamily,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight ?? FontWeight.normal,
      color: color,
      height: height ?? 1.4,
      fontFamily: fontFamily,
    );
  }

  /// تحسين الحشو والهوامش
  static EdgeInsets optimizedPadding({
    double? all,
    double? horizontal,
    double? vertical,
    double? left,
    double? top,
    double? right,
    double? bottom,
  }) {
    if (all != null) return EdgeInsets.all(all);
    if (horizontal != null || vertical != null) {
      return EdgeInsets.symmetric(
        horizontal: horizontal ?? 0,
        vertical: vertical ?? 0,
      );
    }
    return EdgeInsets.only(
      left: left ?? 0,
      top: top ?? 0,
      right: right ?? 0,
      bottom: bottom ?? 0,
    );
  }
}

/// أنواع انتقالات الصفحات
enum PageTransitionType {
  fade,
  slide,
  scale,
}

/// أنواع الاهتزاز
enum HapticFeedbackType {
  lightImpact,
  mediumImpact,
  heavyImpact,
  selectionClick,
}
