import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/azkar_model.dart';
import '../utils/logger.dart';

/// خدمة التخزين المحلي للأذكار الخاصة
class LocalStorageService {
  static const String _customAzkarKey = 'custom_azkar';
  static const String _favoriteAzkarKey = 'favorite_azkar';
  static const String _lastIdKey = 'last_custom_zikr_id';

  /// الحصول على الأذكار الخاصة من التخزين المحلي
  Future<List<Zikr>> getCustomAzkar() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? customAzkarJson = prefs.getString(_customAzkarKey);

      if (customAzkarJson == null || customAzkarJson.isEmpty) {
        return [];
      }

      final List<dynamic> decodedList = jsonDecode(customAzkarJson);
      return decodedList.map((item) => Zikr.fromMap(item)).toList();
    } catch (e) {
      AppLogger.error('خطأ في استرجاع الأذكار الخاصة: $e');
      return [];
    }
  }

  /// إضافة ذكر خاص جديد
  Future<int> addCustomZikr(Zikr zikr) async {
    try {
      AppLogger.info(
        'LocalStorageService: بدء إضافة ذكر خاص جديد: ${zikr.text}',
      );
      final prefs = await SharedPreferences.getInstance();

      // استخدام المعرف المقدم إذا كان أكبر من الصفر، وإلا إنشاء معرف جديد
      int newId = zikr.id > 0 ? zikr.id : DateTime.now().millisecondsSinceEpoch;

      // تحديث آخر معرف مستخدم
      await prefs.setInt(_lastIdKey, newId);
      AppLogger.info('LocalStorageService: تم تعيين المعرف الجديد: $newId');

      // الحصول على الأذكار الخاصة الحالية
      List<Zikr> customAzkar = await getCustomAzkar();
      AppLogger.info(
        'LocalStorageService: تم الحصول على ${customAzkar.length} ذكر خاص',
      );

      // التحقق مما إذا كان الذكر موجوداً بالفعل (للتحديث)
      int existingIndex = customAzkar.indexWhere((item) => item.id == zikr.id);

      if (existingIndex >= 0) {
        // تحديث الذكر الموجود
        customAzkar[existingIndex] = zikr;
        AppLogger.info(
          'LocalStorageService: تم تحديث ذكر موجود بمعرف: ${zikr.id}',
        );
      } else {
        // إضافة الذكر الجديد مع المعرف الجديد
        final newZikr = zikr.copyWith(id: newId, isCustom: true);
        customAzkar.insert(0, newZikr); // إضافة في بداية القائمة
        AppLogger.info('LocalStorageService: تم إضافة ذكر جديد بمعرف: $newId');
      }

      // حفظ الأذكار الخاصة المحدثة
      final List<Map<String, dynamic>> azkarMaps =
          customAzkar.map((zikr) => zikr.toMap()).toList();
      bool saveSuccess = await prefs.setString(
        _customAzkarKey,
        jsonEncode(azkarMaps),
      );

      if (!saveSuccess) {
        AppLogger.error('LocalStorageService: فشل في حفظ الأذكار الخاصة');
        return -1;
      }

      // حفظ آخر معرف مستخدم
      await prefs.setInt(_lastIdKey, newId);

      AppLogger.info(
        'LocalStorageService: تم حفظ الذكر الخاص بنجاح بمعرف: $newId',
      );
      return newId;
    } catch (e) {
      AppLogger.error('LocalStorageService: خطأ في إضافة ذكر خاص: $e');
      return -1;
    }
  }

  /// تحديث ذكر خاص
  Future<bool> updateCustomZikr(Zikr zikr) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // الحصول على الأذكار الخاصة الحالية
      List<Zikr> customAzkar = await getCustomAzkar();

      // البحث عن الذكر المراد تحديثه
      int index = customAzkar.indexWhere((item) => item.id == zikr.id);
      if (index == -1) {
        return false; // الذكر غير موجود
      }

      // تحديث الذكر
      customAzkar[index] = zikr;

      // حفظ الأذكار الخاصة المحدثة
      final List<Map<String, dynamic>> azkarMaps =
          customAzkar.map((zikr) => zikr.toMap()).toList();
      await prefs.setString(_customAzkarKey, jsonEncode(azkarMaps));

      return true;
    } catch (e) {
      AppLogger.error('خطأ في تحديث ذكر خاص: $e');
      return false;
    }
  }

  /// حذف ذكر خاص
  Future<bool> deleteCustomZikr(int id) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // الحصول على الأذكار الخاصة الحالية
      List<Zikr> customAzkar = await getCustomAzkar();

      // حذف الذكر
      customAzkar.removeWhere((zikr) => zikr.id == id);

      // حفظ الأذكار الخاصة المحدثة
      final List<Map<String, dynamic>> azkarMaps =
          customAzkar.map((zikr) => zikr.toMap()).toList();
      await prefs.setString(_customAzkarKey, jsonEncode(azkarMaps));

      return true;
    } catch (e) {
      AppLogger.error('خطأ في حذف ذكر خاص: $e');
      return false;
    }
  }

  /// تحديث عداد الذكر
  Future<bool> updateZikrCount(Zikr zikr, int count) async {
    try {
      final updatedZikr = zikr.copyWith(currentCount: count);
      return await updateCustomZikr(updatedZikr);
    } catch (e) {
      AppLogger.error('خطأ في تحديث عداد الذكر: $e');
      return false;
    }
  }

  /// تبديل حالة المفضلة للذكر
  Future<bool> toggleFavorite(Zikr zikr) async {
    try {
      final updatedZikr = zikr.copyWith(isFavorite: !zikr.isFavorite);
      return await updateCustomZikr(updatedZikr);
    } catch (e) {
      AppLogger.error('خطأ في تبديل حالة المفضلة للذكر: $e');
      return false;
    }
  }

  /// الحصول على الأذكار المفضلة
  Future<List<int>> getFavoriteIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? favoriteIdsJson = prefs.getString(_favoriteAzkarKey);

      if (favoriteIdsJson == null || favoriteIdsJson.isEmpty) {
        return [];
      }

      final List<dynamic> decodedList = jsonDecode(favoriteIdsJson);
      return decodedList.cast<int>();
    } catch (e) {
      AppLogger.error('خطأ في استرجاع معرفات الأذكار المفضلة: $e');
      return [];
    }
  }

  /// إضافة ذكر إلى المفضلة
  Future<bool> addToFavorites(int id) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // الحصول على معرفات الأذكار المفضلة الحالية
      List<int> favoriteIds = await getFavoriteIds();

      // إضافة المعرف إذا لم يكن موجوداً بالفعل
      if (!favoriteIds.contains(id)) {
        favoriteIds.add(id);
        await prefs.setString(_favoriteAzkarKey, jsonEncode(favoriteIds));
      }

      return true;
    } catch (e) {
      AppLogger.error('خطأ في إضافة ذكر إلى المفضلة: $e');
      return false;
    }
  }

  /// إزالة ذكر من المفضلة
  Future<bool> removeFromFavorites(int id) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // الحصول على معرفات الأذكار المفضلة الحالية
      List<int> favoriteIds = await getFavoriteIds();

      // إزالة المعرف إذا كان موجوداً
      favoriteIds.remove(id);
      await prefs.setString(_favoriteAzkarKey, jsonEncode(favoriteIds));

      return true;
    } catch (e) {
      AppLogger.error('خطأ في إزالة ذكر من المفضلة: $e');
      return false;
    }
  }
}
