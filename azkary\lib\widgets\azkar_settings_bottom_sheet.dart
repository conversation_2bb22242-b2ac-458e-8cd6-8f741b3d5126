import 'package:flutter/material.dart';
import '../services/azkar_settings_service.dart';

/// قائمة منبثقة لإعدادات الأذكار
class AzkarSettingsBottomSheet extends StatefulWidget {
  final VoidCallback? onSettingsChanged;

  const AzkarSettingsBottomSheet({super.key, this.onSettingsChanged});

  @override
  State<AzkarSettingsBottomSheet> createState() =>
      _AzkarSettingsBottomSheetState();
}

class _AzkarSettingsBottomSheetState extends State<AzkarSettingsBottomSheet> {
  bool _hideCompletedAzkar = false;
  bool _vibrationEnabled = true;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// تحميل الإعدادات الحالية
  Future<void> _loadSettings() async {
    try {
      final settings = await AzkarSettingsService.getAllSettings();
      setState(() {
        _hideCompletedAzkar = settings['hideCompleted'] ?? false;
        _vibrationEnabled = settings['vibrationEnabled'] ?? true;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// حفظ إعداد إخفاء الأذكار المكتملة
  Future<void> _toggleHideCompleted(bool value) async {
    setState(() {
      _hideCompletedAzkar = value;
    });

    await AzkarSettingsService.setHideCompletedAzkar(value);

    // تنفيذ اهتزاز خفيف عند التغيير
    await AzkarSettingsService.performVibration();

    // إشعار الصفحة الأساسية بالتغيير
    widget.onSettingsChanged?.call();
  }

  /// حفظ إعداد الاهتزاز
  Future<void> _toggleVibration(bool value) async {
    setState(() {
      _vibrationEnabled = value;
    });

    await AzkarSettingsService.setVibrationEnabled(value);

    // تنفيذ اهتزاز خفيف عند التفعيل فقط
    if (value) {
      await AzkarSettingsService.performVibration();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مقبض السحب
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // العنوان
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Icon(
                  Icons.settings,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'إعدادات الأذكار',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),

          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(20),
              child: CircularProgressIndicator(),
            )
          else ...[
            // إعداد إخفاء الأذكار المكتملة
            _buildSettingItem(
              context: context,
              icon: Icons.visibility_off,
              title: 'إخفاء الذكر عند الاكتمال',
              subtitle: 'إخفاء الذكر تلقائياً عند اكتمال العدد المطلوب',
              value: _hideCompletedAzkar,
              onChanged: _toggleHideCompleted,
              theme: theme,
            ),

            // فاصل
            Divider(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.1),
              height: 1,
              indent: 20,
              endIndent: 20,
            ),

            // إعداد الاهتزاز
            _buildSettingItem(
              context: context,
              icon: Icons.vibration,
              title: 'الاهتزاز',
              subtitle: 'اهتزاز الجهاز عند النقر على زر العد',
              value: _vibrationEnabled,
              onChanged: _toggleVibration,
              theme: theme,
            ),

            // مساحة إضافية في الأسفل
            const SizedBox(height: 20),
          ],
        ],
      ),
    );
  }

  /// بناء عنصر إعداد واحد
  Widget _buildSettingItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
    required ThemeData theme,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: theme.colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: theme.colorScheme.primary, size: 24),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: theme.colorScheme.onSurface,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: theme.colorScheme.primary,
        activeTrackColor: theme.colorScheme.primary.withValues(alpha: 0.3),
        inactiveThumbColor: theme.colorScheme.onSurface.withValues(alpha: 0.5),
        inactiveTrackColor: theme.colorScheme.onSurface.withValues(alpha: 0.1),
      ),
    );
  }
}

/// دالة مساعدة لعرض القائمة المنبثقة
Future<void> showAzkarSettingsBottomSheet({
  required BuildContext context,
  VoidCallback? onSettingsChanged,
}) {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder:
        (context) =>
            AzkarSettingsBottomSheet(onSettingsChanged: onSettingsChanged),
  );
}
