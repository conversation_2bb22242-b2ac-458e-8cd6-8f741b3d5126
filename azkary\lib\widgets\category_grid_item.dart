import 'package:flutter/material.dart';
import '../models/azkar_model.dart';
import '../screens/azkar_list_screen.dart';

class CategoryGridItem extends StatelessWidget {
  final Category category;

  const CategoryGridItem({super.key, required this.category});

  IconData _getCategoryIcon() {
    switch (category.icon) {
      case 'sun':
        return Icons.wb_sunny_rounded;
      case 'moon':
        return Icons.nightlight_round;
      case 'bed':
        return Icons.bed;
      case 'alarm':
        return Icons.alarm;
      case 'prayer':
        return Icons.mosque;
      case 'heart':
        return Icons.favorite;
      case 'travel':
        return Icons.directions_car;
      case 'food':
        return Icons.restaurant;
      case 'mosque':
        return Icons.mosque;
      case 'misc':
        return Icons.more_horiz;
      default:
        return Icons.auto_awesome;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 4, // ارتفاع الظل
      color: theme.cardColor, // استخدام لون البطاقة من الثيم
      shadowColor:
          theme.brightness == Brightness.dark
              ? Colors.black.withAlpha(40) // ظل مثل تويتر
              : Colors.black.withAlpha(40),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color:
              theme.brightness == Brightness.dark
                  ? const Color(0x4D9E9E9E) // حدود مثل تويتر
                  : Colors.grey.withAlpha(30),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AzkarListScreen(category: category.name),
            ),
          );
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20), // نفس حشو قوائم الأذكار
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة التصنيف
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withAlpha(
                    25,
                  ), // 0.1 opacity ≈ 25 alpha
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getCategoryIcon(),
                  color: theme.colorScheme.primary,
                  size: 32,
                ),
              ),
              const SizedBox(height: 16),
              // اسم التصنيف
              Text(
                category.name,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18, // زيادة حجم الخط
                  color:
                      theme.colorScheme.onSurface, // استخدام لون النص من الثيم
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              // عدد الأذكار
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withAlpha(30),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${category.count} ذكر',
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
