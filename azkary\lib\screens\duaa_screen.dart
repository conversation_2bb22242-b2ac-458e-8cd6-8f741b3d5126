import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../constants/duaa_data.dart';
import '../widgets/islamic_pattern.dart';
import '../widgets/animated_widgets.dart';
import '../services/feedback_provider.dart';
import '../widgets/custom_app_bar.dart';

/// شاشة الأدعية
class DuaaScreen extends StatefulWidget {
  const DuaaScreen({super.key});

  @override
  State<DuaaScreen> createState() => _DuaaScreenState();
}

class _DuaaScreenState extends State<DuaaScreen>
    with SingleTickerProviderStateMixin {
  // متحكم الحركة للتأثيرات
  late AnimationController _animationController;

  // قائمة الأدعية
  final List<Duaa> _duaaList = duaaData;

  // فلتر الأدعية حسب المناسبة
  String? _selectedOccasion;

  // قائمة المناسبات المتاحة
  late List<String?> _occasions;

  @override
  void initState() {
    super.initState();

    // إعداد متحكم الحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // بدء الحركة عند تحميل الصفحة
    _animationController.forward();

    // استخراج المناسبات الفريدة من قائمة الأدعية
    _occasions = ['الكل', ..._duaaList.map((duaa) => duaa.occasion).toSet()];
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // الحصول على قائمة الأدعية المفلترة
  List<Duaa> get filteredDuaaList {
    if (_selectedOccasion == null || _selectedOccasion == 'الكل') {
      return _duaaList;
    }
    return _duaaList
        .where((duaa) => duaa.occasion == _selectedOccasion)
        .toList();
  }

  // نسخ نص الدعاء
  void _copyDuaa(BuildContext context, Duaa duaa) {
    Clipboard.setData(ClipboardData(text: duaa.text));

    // تنفيذ اهتزاز خفيف
    final feedbackProvider = Provider.of<FeedbackProvider>(
      context,
      listen: false,
    );
    feedbackProvider.lightHapticFeedback();

    // عرض رسالة تأكيد
    feedbackProvider.showInfoSnackBar(context, 'تم نسخ الدعاء');
  }

  // مشاركة الدعاء
  void _shareDuaa(BuildContext context, Duaa duaa) {
    final text = '${duaa.text}\n\nالمصدر: ${duaa.source}\n\nمن تطبيق أذكاري';

    // استخدام SharePlus بالطريقة الصحيحة
    final box = context.findRenderObject() as RenderBox?;
    SharePlus.instance.share(
      ShareParams(
        text: text,
        sharePositionOrigin:
            box != null ? box.localToGlobal(Offset.zero) & box.size : null,
      ),
    );

    // تنفيذ اهتزاز خفيف
    final feedbackProvider = Provider.of<FeedbackProvider>(
      context,
      listen: false,
    );
    feedbackProvider.lightHapticFeedback();

    // عرض رسالة تأكيد
    feedbackProvider.showInfoSnackBar(context, 'تمت مشاركة الدعاء');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: CustomAppBar(title: 'الأدعية'),
      body: Stack(
        children: [
          // خلفية إسلامية
          Positioned.fill(
            child: Opacity(
              opacity: 0.05,
              child: IslamicPattern(color: theme.colorScheme.primary),
            ),
          ),

          // محتوى الصفحة
          SafeArea(
            child: Column(
              children: [
                // شريط الفلترة
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: _buildFilterBar(theme),
                ),

                // قائمة الأدعية
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    itemCount: filteredDuaaList.length,
                    itemBuilder: (context, index) {
                      final duaa = filteredDuaaList[index];
                      return AnimatedListItem(
                        index: index,
                        animate: true,
                        child: _buildDuaaCard(context, duaa, theme, isDarkMode),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء شريط الفلترة
  Widget _buildFilterBar(ThemeData theme) {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(
              red: 0,
              green: 0,
              blue: 0,
              alpha: 0.05,
            ),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _occasions.length,
        itemBuilder: (context, index) {
          final occasion = _occasions[index];
          final isSelected =
              _selectedOccasion == occasion ||
              (occasion == 'الكل' && _selectedOccasion == null);

          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: ChoiceChip(
              label: Text(occasion ?? 'الكل'),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedOccasion = selected ? occasion : null;
                });

                // تنفيذ اهتزاز خفيف
                HapticFeedback.selectionClick();
              },
              backgroundColor: theme.colorScheme.surface,
              selectedColor: theme.colorScheme.primary.withValues(
                red: theme.colorScheme.primary.r.toDouble(),
                green: theme.colorScheme.primary.g.toDouble(),
                blue: theme.colorScheme.primary.b.toDouble(),
                alpha: 0.2,
              ),
              labelStyle: TextStyle(
                color:
                    isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          );
        },
      ),
    );
  }

  // بناء بطاقة الدعاء
  Widget _buildDuaaCard(
    BuildContext context,
    Duaa duaa,
    ThemeData theme,
    bool isDarkMode,
  ) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: isDarkMode ? const Color(0xFF1E2732) : theme.colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // نص الدعاء
            Text(
              duaa.text,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),

            const SizedBox(height: 16),

            // المصدر
            Text(
              duaa.source,
              style: TextStyle(
                fontSize: 14,
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),

            // المناسبة
            if (duaa.occasion != null) ...[
              const SizedBox(height: 8),
              Text(
                duaa.occasion!,
                style: TextStyle(
                  fontSize: 14,
                  color: theme.colorScheme.secondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],

            // الفضل
            if (duaa.fadl != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF8B4513).withValues(
                    red: 139,
                    green: 69,
                    blue: 19,
                    alpha: isDarkMode ? 0.3 : 0.1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  duaa.fadl!,
                  style: TextStyle(
                    fontSize: 14,
                    color:
                        isDarkMode
                            ? const Color(0xFFD2B48C)
                            : const Color(
                              0xFF3E2723,
                            ), // لون بني داكن أكثر للوضع الفاتح
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],

            const SizedBox(height: 16),

            // أزرار النسخ والمشاركة
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // زر النسخ
                IconButton(
                  icon: Icon(
                    Icons.content_copy,
                    color: theme.colorScheme.primary,
                  ),
                  onPressed: () => _copyDuaa(context, duaa),
                  tooltip: 'نسخ',
                ),

                const SizedBox(width: 16),

                // زر المشاركة
                IconButton(
                  icon: Icon(Icons.share, color: theme.colorScheme.primary),
                  onPressed: () => _shareDuaa(context, duaa),
                  tooltip: 'مشاركة',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
