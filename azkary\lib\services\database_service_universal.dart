import 'package:flutter/foundation.dart' hide Category;
import '../models/azkar_model.dart';
import '../utils/logger.dart';
import 'database_service_web.dart';

/// خدمة قاعدة البيانات الموحدة التي تعمل على جميع المنصات
class DatabaseServiceUniversal {
  static final DatabaseServiceUniversal _instance =
      DatabaseServiceUniversal._internal();

  // الخدمات المختلفة
  dynamic _mobileService;
  DatabaseServiceWeb? _webService;

  factory DatabaseServiceUniversal() {
    return _instance;
  }

  DatabaseServiceUniversal._internal();

  /// الحصول على قاعدة البيانات (للتوافق مع الكود القديم)
  Future<dynamic> get database async {
    // نستخدم خدمة الويب دائماً
    _webService ??= DatabaseServiceWeb();
    await _webService!.initDatabase();
    return null; // نعيد null لأننا نستخدم SharedPreferences
  }

  /// تهيئة الخدمة المناسبة حسب المنصة
  Future<void> initDatabase() async {
    try {
      AppLogger.info('بدء تهيئة قاعدة البيانات...');
      // استخدام خدمة الويب دائماً
      _webService = DatabaseServiceWeb();
      await _webService!.initDatabase();
      AppLogger.info('تم تهيئة قاعدة البيانات');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة قاعدة البيانات: $e');
      // استخدام الخدمة البديلة
      _webService = DatabaseServiceWeb();
      await _webService!.initDatabase();
    }
  }

  /// الحصول على جميع التصنيفات
  Future<List<Category>> getCategories() async {
    try {
      _webService ??= DatabaseServiceWeb();
      await _webService!.initDatabase();
      return await _webService!.getCategories();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على التصنيفات: $e');
      return [];
    }
  }

  /// إضافة تصنيف جديد
  Future<int> addCategory(Category category) async {
    try {
      _webService ??= DatabaseServiceWeb();
      await _webService!.initDatabase();
      await _webService!.addCategory(category);
      return category.id;
    } catch (e) {
      AppLogger.error('خطأ في إضافة التصنيف: $e');
      return -1;
    }
  }

  /// إضافة ذكر إلى تصنيف
  Future<int> addZikrToCategory(Zikr zikr, String categoryName) async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        await _webService!.addZikrToCategory(zikr, categoryName);
        return zikr.id;
      } else if (_mobileService != null) {
        return await _mobileService!.addZikrToCategory(zikr, categoryName);
      } else {
        await initDatabase();
        return await addZikrToCategory(zikr, categoryName);
      }
    } catch (e) {
      AppLogger.error('خطأ في إضافة الذكر: $e');
      return -1;
    }
  }

  /// البحث في الأذكار
  Future<List<Zikr>> searchAzkar(String query) async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        return await _webService!.searchAzkar(query);
      } else if (_mobileService != null) {
        return await _mobileService!.searchAzkar(query);
      } else {
        await initDatabase();
        return await searchAzkar(query);
      }
    } catch (e) {
      AppLogger.error('خطأ في البحث: $e');
      return [];
    }
  }

  /// الحصول على ذكر عشوائي
  Future<Zikr?> getRandomZikr() async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        return await _webService!.getRandomZikr();
      } else if (_mobileService != null) {
        return await _mobileService!.getRandomZikr();
      } else {
        await initDatabase();
        return await getRandomZikr();
      }
    } catch (e) {
      AppLogger.error('خطأ في الحصول على ذكر عشوائي: $e');
      return null;
    }
  }

  /// تحديث العداد الحالي للذكر
  Future<void> updateCurrentCount(int zikrId, int count) async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        await _webService!.updateZikrCounter(zikrId, count);
      } else if (_mobileService != null) {
        await _mobileService!.updateCurrentCount(zikrId, count);
      } else {
        await initDatabase();
        await updateCurrentCount(zikrId, count);
      }
    } catch (e) {
      AppLogger.error('خطأ في تحديث العداد: $e');
    }
  }

  /// إعادة تعيين جميع العدادات
  Future<void> resetAllCurrentCounts() async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        await _webService!.resetAllCurrentCounts();
      } else if (_mobileService != null) {
        await _mobileService!.resetAllCurrentCounts();
      } else {
        await initDatabase();
        await resetAllCurrentCounts();
      }
    } catch (e) {
      AppLogger.error('خطأ في إعادة تعيين العدادات: $e');
    }
  }

  /// الحصول على أذكار تصنيف معين
  Future<List<Zikr>> getAzkarByCategory(String category) async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        return await _webService!.getAzkarByCategory(category);
      } else if (_mobileService != null) {
        return await _mobileService!.getAzkarByCategory(category);
      } else {
        // إعادة التهيئة
        await initDatabase();
        return await getAzkarByCategory(category);
      }
    } catch (e) {
      AppLogger.error('خطأ في الحصول على أذكار التصنيف $category: $e');
      return [];
    }
  }

  /// الحصول على الأذكار المفضلة
  Future<List<Zikr>> getFavoriteAzkar() async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        return await _webService!.getFavoriteAzkar();
      } else if (_mobileService != null) {
        return await _mobileService!.getFavoriteAzkar();
      } else {
        // إعادة التهيئة
        await initDatabase();
        return await getFavoriteAzkar();
      }
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الأذكار المفضلة: $e');
      return [];
    }
  }

  /// إضافة/إزالة ذكر من المفضلة
  Future<void> toggleFavorite(int zikrId) async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        await _webService!.toggleFavorite(zikrId);
      } else if (_mobileService != null) {
        await _mobileService!.toggleFavorite(zikrId, true);
      } else {
        // إعادة التهيئة
        await initDatabase();
        await toggleFavorite(zikrId);
      }
    } catch (e) {
      AppLogger.error('خطأ في تحديث المفضلة: $e');
    }
  }

  /// التحقق من كون الذكر مفضل
  Future<bool> isFavorite(int zikrId) async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        return await _webService!.isFavorite(zikrId);
      } else if (_mobileService != null) {
        // للموبايل، نحتاج إلى تنفيذ منطق مختلف
        final favorites = await _mobileService!.getFavoriteAzkar();
        return favorites.any((zikr) => zikr.id == zikrId);
      } else {
        // إعادة التهيئة
        await initDatabase();
        return await isFavorite(zikrId);
      }
    } catch (e) {
      AppLogger.error('خطأ في فحص المفضلة: $e');
      return false;
    }
  }

  /// تحديث عداد الذكر
  Future<void> updateZikrCounter(int zikrId, int count) async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        await _webService!.updateZikrCounter(zikrId, count);
      } else if (_mobileService != null) {
        await _mobileService!.updateCurrentCount(zikrId, count);
      } else {
        // إعادة التهيئة
        await initDatabase();
        await updateZikrCounter(zikrId, count);
      }
    } catch (e) {
      AppLogger.error('خطأ في تحديث العداد: $e');
    }
  }

  /// الحصول على عداد الذكر
  Future<int> getZikrCounter(int zikrId) async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        return await _webService!.getZikrCounter(zikrId);
      } else if (_mobileService != null) {
        // للموبايل، نعيد 0 كقيمة افتراضية
        return 0;
      } else {
        // إعادة التهيئة
        await initDatabase();
        return await getZikrCounter(zikrId);
      }
    } catch (e) {
      AppLogger.error('خطأ في الحصول على العداد: $e');
      return 0;
    }
  }

  /// الحصول على الأذكار المخصصة
  Future<List<Zikr>> getCustomAzkar() async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        return await _webService!.getCustomAzkar();
      } else if (_mobileService != null) {
        return await _mobileService!.getCustomAzkar();
      } else {
        // إعادة التهيئة
        await initDatabase();
        return await getCustomAzkar();
      }
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الأذكار المخصصة: $e');
      return [];
    }
  }

  /// إضافة ذكر مخصص
  Future<void> addCustomZikr(Zikr zikr) async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        await _webService!.addCustomZikr(zikr);
      } else if (_mobileService != null) {
        await _mobileService!.addCustomZikr(zikr);
      } else {
        // إعادة التهيئة
        await initDatabase();
        await addCustomZikr(zikr);
      }
    } catch (e) {
      AppLogger.error('خطأ في إضافة الذكر المخصص: $e');
    }
  }

  /// حذف ذكر مخصص
  Future<void> deleteCustomZikr(int zikrId) async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        await _webService!.deleteCustomZikr(zikrId);
      } else if (_mobileService != null) {
        await _mobileService!.deleteCustomZikr(zikrId);
      } else {
        // إعادة التهيئة
        await initDatabase();
        await deleteCustomZikr(zikrId);
      }
    } catch (e) {
      AppLogger.error('خطأ في حذف الذكر المخصص: $e');
    }
  }

  /// التحقق من حالة قاعدة البيانات وإعادة تهيئتها إذا لزم الأمر
  Future<bool> checkAndReinitializeDatabase() async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        return await _webService!.checkAndReinitializeDatabase();
      } else if (_mobileService != null) {
        return await _mobileService!.checkAndReinitializeDatabase();
      } else {
        // إعادة التهيئة
        await initDatabase();
        return true;
      }
    } catch (e) {
      AppLogger.error('خطأ في فحص قاعدة البيانات: $e');
      // استخدام الخدمة البديلة
      _webService = DatabaseServiceWeb();
      await _webService!.initDatabase();
      return true;
    }
  }

  /// إعادة تعيين جميع البيانات
  Future<void> resetAllData() async {
    try {
      if (kIsWeb || _webService != null) {
        _webService ??= DatabaseServiceWeb();
        await _webService!.resetAllData();
      } else if (_mobileService != null) {
        // للموبايل، نحتاج إلى تنفيذ دالة مشابهة
        await _mobileService!.checkAndReinitializeDatabase();
      } else {
        // إعادة التهيئة
        await initDatabase();
      }
    } catch (e) {
      AppLogger.error('خطأ في إعادة تعيين البيانات: $e');
    }
  }
}
