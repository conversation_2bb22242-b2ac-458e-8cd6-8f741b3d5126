import 'package:flutter/material.dart';

/// نموذج لتتبع حالة إكمال أذكار الصنف
class AzkarCompletionModel {
  /// اسم الصنف (الصباح أو المساء)
  final String category;

  /// عدد الأذكار الكلي في الصنف
  final int totalCount;

  /// عدد الأذكار المكتملة
  final int completedCount;

  /// تاريخ آخر إكمال
  final DateTime lastCompletionDate;

  /// ما إذا كان الصنف مكتملاً بالكامل
  bool get isFullyCompleted => completedCount >= totalCount;

  /// نسبة الإكمال (0.0 إلى 1.0)
  double get completionPercentage =>
      totalCount > 0 ? completedCount / totalCount : 0.0;

  AzkarCompletionModel({
    required this.category,
    required this.totalCount,
    required this.completedCount,
    DateTime? lastCompletionDate,
  }) : lastCompletionDate = lastCompletionDate ?? DateTime.now();

  /// إنشاء نسخة جديدة مع تعديل بعض الخصائص
  AzkarCompletionModel copyWith({
    String? category,
    int? totalCount,
    int? completedCount,
    DateTime? lastCompletionDate,
  }) {
    return AzkarCompletionModel(
      category: category ?? this.category,
      totalCount: totalCount ?? this.totalCount,
      completedCount: completedCount ?? this.completedCount,
      lastCompletionDate: lastCompletionDate ?? this.lastCompletionDate,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'category': category,
      'totalCount': totalCount,
      'completedCount': completedCount,
      'lastCompletionDate': lastCompletionDate.toIso8601String(),
    };
  }

  /// إنشاء نموذج من Map
  factory AzkarCompletionModel.fromMap(Map<String, dynamic> map) {
    return AzkarCompletionModel(
      category: map['category'] ?? '',
      totalCount: map['totalCount'] ?? 0,
      completedCount: map['completedCount'] ?? 0,
      lastCompletionDate:
          map['lastCompletionDate'] != null
              ? DateTime.parse(map['lastCompletionDate'])
              : null,
    );
  }

  /// التحقق مما إذا كان الصنف مكتملاً في اليوم الحالي
  bool isCompletedToday() {
    final now = DateTime.now();
    return lastCompletionDate.year == now.year &&
        lastCompletionDate.month == now.month &&
        lastCompletionDate.day == now.day;
  }

  /// التحقق مما إذا كان الوقت الحالي هو وقت الصباح
  static bool isMorningTime() {
    final now = TimeOfDay.now();
    // اعتبار وقت الصباح من الساعة 3 صباحاً حتى 11:59 صباحاً
    return now.hour >= 3 && now.hour < 12;
  }

  /// التحقق مما إذا كان الوقت الحالي هو وقت المساء
  static bool isEveningTime() {
    final now = TimeOfDay.now();
    // اعتبار وقت المساء من الساعة 12 ظهراً حتى 2:59 صباحاً
    return now.hour >= 12 || now.hour < 3;
  }

  /// التحقق مما إذا كان يجب إعادة ضبط أذكار الصباح
  static bool shouldResetMorningAzkar() {
    // إذا كان الوقت الحالي هو وقت الصباح، تحقق مما إذا كان يوم جديد
    if (isMorningTime()) {
      final now = DateTime.now();
      final lastReset = _getLastResetDate('morning_azkar_reset');

      // إعادة ضبط إذا كان يوم جديد أو لم يتم إعادة الضبط من قبل
      if (lastReset == null ||
          lastReset.year != now.year ||
          lastReset.month != now.month ||
          lastReset.day != now.day) {
        _saveLastResetDate('morning_azkar_reset', now);
        return true;
      }
    }

    return false;
  }

  /// التحقق مما إذا كان يجب إعادة ضبط أذكار المساء
  static bool shouldResetEveningAzkar() {
    // إذا كان الوقت الحالي هو وقت المساء، تحقق مما إذا كان يوم جديد أو فترة جديدة
    if (isEveningTime()) {
      final now = DateTime.now();
      final lastReset = _getLastResetDate('evening_azkar_reset');

      // إعادة ضبط إذا كان يوم جديد أو لم يتم إعادة الضبط من قبل
      if (lastReset == null ||
          lastReset.year != now.year ||
          lastReset.month != now.month ||
          lastReset.day != now.day) {
        _saveLastResetDate('evening_azkar_reset', now);
        return true;
      }
    }

    return false;
  }

  /// الحصول على تاريخ آخر إعادة ضبط
  static DateTime? _getLastResetDate(String key) {
    // هذه الدالة تحتاج إلى تنفيذ باستخدام SharedPreferences
    // سيتم تنفيذها في AzkarProvider
    return null;
  }

  /// حفظ تاريخ آخر إعادة ضبط
  static void _saveLastResetDate(String key, DateTime date) {
    // هذه الدالة تحتاج إلى تنفيذ باستخدام SharedPreferences
    // سيتم تنفيذها في AzkarProvider
  }
}
