import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart' as share_plus;
import '../models/azkar_model.dart';
import '../services/azkar_provider.dart';
import '../widgets/zikr_counter.dart';
import '../utils/logger.dart';

class ZikrDetailScreen extends StatefulWidget {
  final Zikr zikr;

  const ZikrDetailScreen({super.key, required this.zikr});

  @override
  State<ZikrDetailScreen> createState() => _ZikrDetailScreenState();
}

class _ZikrDetailScreenState extends State<ZikrDetailScreen> {
  late Zikr _zikr;

  @override
  void initState() {
    super.initState();
    _zikr = widget.zikr;
  }

  void _incrementCounter() {
    if (_zikr.currentCount < _zikr.count) {
      setState(() {
        _zikr = _zikr.copyWith(currentCount: _zikr.currentCount + 1);
      });

      // Update in provider
      Provider.of<AzkarProvider>(
        context,
        listen: false,
      ).updateZikrCount(_zikr, _zikr.currentCount, context);

      // Vibrate when counter is incremented
      HapticFeedback.lightImpact();

      // Show completion message when counter reaches target
      if (_zikr.currentCount == _zikr.count) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('أحسنت! تم إكمال الذكر'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _resetCounter() {
    setState(() {
      _zikr = _zikr.copyWith(currentCount: 0);
    });

    // Update in provider
    Provider.of<AzkarProvider>(
      context,
      listen: false,
    ).updateZikrCount(_zikr, 0, context);
  }

  void _copyZikr() {
    Clipboard.setData(ClipboardData(text: _zikr.text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم نسخ الذكر'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _shareZikr() {
    share_plus.SharePlus.instance.share(
      share_plus.ShareParams(
        text: '${_zikr.text}\n\nالمصدر: ${_zikr.source}\n\nمن تطبيق أذكاري',
      ),
    );
    AppLogger.info(
      'تمت مشاركة الذكر: ${_zikr.text.substring(0, _zikr.text.length > 30 ? 30 : _zikr.text.length)}...',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _zikr.category,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(
              _zikr.isFavorite ? Icons.favorite : Icons.favorite_border,
              color: _zikr.isFavorite ? Colors.red : null,
            ),
            onPressed: () {
              Provider.of<AzkarProvider>(
                context,
                listen: false,
              ).toggleFavorite(_zikr);
              setState(() {
                _zikr = _zikr.copyWith(isFavorite: !_zikr.isFavorite);
              });
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Source and reference
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(
                  red: Theme.of(context).colorScheme.primary.r.toDouble(),
                  green: Theme.of(context).colorScheme.primary.g.toDouble(),
                  blue: Theme.of(context).colorScheme.primary.b.toDouble(),
                  alpha: 0.1,
                ), // 0.1 opacity
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'المصدر: ${_zikr.source}',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize:
                      Theme.of(context)
                          .textTheme
                          .bodyMedium
                          ?.fontSize, // استخدام حجم الخط من السمة
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Zikr text
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(
                      red: 0,
                      green: 0,
                      blue: 0,
                      alpha: 0.05,
                    ), // 0.05 opacity
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Text(
                _zikr.text,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize:
                      Theme.of(context)
                          .textTheme
                          .headlineSmall
                          ?.fontSize, // استخدام حجم الخط من السمة
                  height: 1.8,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Fadl (if available)
            if (_zikr.fadl != null && _zikr.fadl!.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.secondary.withValues(
                    red: Theme.of(context).colorScheme.secondary.r.toDouble(),
                    green: Theme.of(context).colorScheme.secondary.g.toDouble(),
                    blue: Theme.of(context).colorScheme.secondary.b.toDouble(),
                    alpha: 0.1,
                  ), // 0.1 opacity
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'فضل الذكر:',
                      style: TextStyle(
                        fontSize:
                            Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.fontSize, // استخدام حجم الخط من السمة
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.secondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _zikr.fadl!,
                      style: TextStyle(
                        fontSize:
                            Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.fontSize, // استخدام حجم الخط من السمة
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.white
                                : Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 24),

            // Counter
            Text(
              'عدد التكرار: ${_zikr.count} مرة',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize:
                    Theme.of(context)
                        .textTheme
                        .titleMedium
                        ?.fontSize, // استخدام حجم الخط من السمة
              ),
            ),

            const SizedBox(height: 16),

            // Counter widget
            ZikrCounter(
              currentCount: _zikr.currentCount,
              totalCount: _zikr.count,
              onIncrement: _incrementCounter,
              onReset: _resetCounter,
            ),

            const SizedBox(height: 24),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: const Icon(Icons.copy),
                  onPressed: _copyZikr,
                  tooltip: 'نسخ',
                ),
                const SizedBox(width: 16),
                IconButton(
                  icon: const Icon(Icons.share),
                  onPressed: _shareZikr,
                  tooltip: 'مشاركة',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
