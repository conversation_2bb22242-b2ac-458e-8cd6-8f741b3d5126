import 'package:flutter/material.dart';

/// نموذج معلومات شهر رمضان
class RamadanInfo {
  final DateTime startDate;
  final DateTime endDate;
  final int year;
  final int totalDays;
  final bool isCurrentlyRamadan;
  final int daysRemaining;
  final Duration timeUntilRamadan;

  const RamadanInfo({
    required this.startDate,
    required this.endDate,
    required this.year,
    required this.totalDays,
    required this.isCurrentlyRamadan,
    required this.daysRemaining,
    required this.timeUntilRamadan,
  });

  /// حساب ساعات الصيام لليوم الحالي
  Duration get fastingHours {
    // تقدير تقريبي لساعات الصيام (من الفجر إلى المغرب)
    // يمكن تحسينه لاحقاً بحساب أوقات الصلاة الفعلية
    return const Duration(hours: 14, minutes: 30);
  }

  /// التحقق من كون اليوم في العشر الأواخر
  bool get isLastTenDays {
    if (!isCurrentlyRamadan) return false;
    return daysRemaining <= 10;
  }

  /// التحقق من كون الليلة ليلة وترية (محتملة لليلة القدر)
  bool get isPotentialLaylatAlQadr {
    if (!isLastTenDays) return false;
    final daysPassed = totalDays - daysRemaining;
    return daysPassed % 2 == 1; // الليالي الوترية
  }
}

/// نموذج الدعاء الرمضاني
class RamadanDua {
  final String id;
  final String title;
  final String arabicText;
  final String transliteration;
  final String translation;
  final String category;
  final String? source;
  final String? benefits;
  final IconData icon;
  final Color color;

  const RamadanDua({
    required this.id,
    required this.title,
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    required this.category,
    this.source,
    this.benefits,
    required this.icon,
    required this.color,
  });
}

/// نموذج التسبيحة الرمضانية
class RamadanTasbih {
  final String id;
  final String title;
  final String arabicText;
  final String transliteration;
  final String translation;
  final int recommendedCount;
  final String category;
  final String? source;
  final String? benefits;
  final IconData icon;
  final Color color;

  const RamadanTasbih({
    required this.id,
    required this.title,
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    required this.recommendedCount,
    required this.category,
    this.source,
    this.benefits,
    required this.icon,
    required this.color,
  });
}

/// فئات الأدعية الرمضانية
enum RamadanDuaCategory {
  iftar('دعاء الإفطار'),
  suhoor('دعاء السحور'),
  laylatAlQadr('أدعية ليلة القدر'),
  lastTenDays('أدعية العشر الأواخر'),
  accepted('الأدعية المستجابة');

  const RamadanDuaCategory(this.displayName);
  final String displayName;
}

/// فئات التسبيحات الرمضانية
enum RamadanTasbihCategory {
  afterIftar('أذكار ما بعد الإفطار'),
  tarawih('تسبيحات التراويح'),
  itikaf('أذكار الاعتكاف'),
  general('تسبيحات رمضانية عامة');

  const RamadanTasbihCategory(this.displayName);
  final String displayName;
}

/// ألوان رمضان المميزة
class RamadanColors {
  static const Color gold = Color(0xFFFFD700);
  static const Color darkGreen = Color(0xFF006400);
  static const Color lightGold = Color(0xFFFFF8DC);
  static const Color bronze = Color(0xFFCD7F32);
  static const Color emerald = Color(0xFF50C878);
  static const Color cream = Color(0xFFFFFDD0);

  /// الحصول على تدرج رمضاني
  static LinearGradient get ramadanGradient => const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [gold, darkGreen],
  );

  /// الحصول على تدرج رمضاني فاتح
  static LinearGradient get lightRamadanGradient => const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [lightGold, emerald],
  );
}

/// أيقونات رمضان
class RamadanIcons {
  static const IconData crescent = Icons.brightness_3;
  static const IconData lantern = Icons.lightbulb_outline;
  static const IconData mosque = Icons.mosque;
  static const IconData prayer = Icons.self_improvement;
  static const IconData book = Icons.menu_book;
  static const IconData star = Icons.star;
  static const IconData calendar = Icons.calendar_today;
  static const IconData timer = Icons.timer;
  static const IconData favorite = Icons.favorite;
  static const IconData peace = Icons.spa;
}
