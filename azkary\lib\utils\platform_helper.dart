import 'package:flutter/foundation.dart' show kIsWeb;

/// مساعد للتعامل مع المنصات المختلفة بطريقة آمنة
class PlatformHelper {
  /// تحقق مما إذا كان التطبيق يعمل على الويب
  static bool get isWeb => kIsWeb;

  /// تحقق مما إذا كان التطبيق يعمل على أندرويد
  static bool get isAndroid {
    if (kIsWeb) return false;
    return false; // للتبسيط، نفترض أننا لسنا على أندرويد في هذه النسخة
  }

  /// تحقق مما إذا كان التطبيق يعمل على iOS
  static bool get isIOS {
    if (kIsWeb) return false;
    return false; // للتبسيط، نفترض أننا لسنا على iOS في هذه النسخة
  }

  /// تحقق مما إذا كان التطبيق يعمل على ويندوز
  static bool get isWindows {
    if (kIsWeb) return false;
    return false; // للتبسيط، نفترض أننا لسنا على ويندوز في هذه النسخة
  }

  /// تحقق مما إذا كان التطبيق يعمل على لينكس
  static bool get isLinux {
    if (kIsWeb) return false;
    return false; // للتبسيط، نفترض أننا لسنا على لينكس في هذه النسخة
  }

  /// تحقق مما إذا كان التطبيق يعمل على ماك
  static bool get isMacOS {
    if (kIsWeb) return false;
    return false; // للتبسيط، نفترض أننا لسنا على ماك في هذه النسخة
  }

  /// تحقق مما إذا كان التطبيق يعمل على جهاز محمول (أندرويد أو iOS)
  static bool get isMobile => isAndroid || isIOS;

  /// تحقق مما إذا كان التطبيق يعمل على سطح المكتب (ويندوز أو لينكس أو ماك)
  static bool get isDesktop => isWindows || isLinux || isMacOS;
}

