/// نموذج بيانات الإحصائيات اليومية
class DailyStatistics {
  final DateTime date;
  final int completedAzkar;
  final int totalAzkar;
  final int completedCategories;
  final int totalCategories;
  final int points;
  final Duration timeSpent;

  DailyStatistics({
    required this.date,
    required this.completedAzkar,
    required this.totalAzkar,
    required this.completedCategories,
    required this.totalCategories,
    required this.points,
    required this.timeSpent,
  });

  /// نسبة الإكمال (0.0 إلى 1.0)
  double get completionPercentage =>
      totalAzkar > 0 ? completedAzkar / totalAzkar : 0.0;

  /// ما إذا كان اليوم مكتملاً بالكامل
  bool get isFullyCompleted => completedAzkar >= totalAzkar;

  /// تحويل إلى Map لحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'date': date.toIso8601String(),
      'completedAzkar': completedAzkar,
      'totalAzkar': totalAzkar,
      'completedCategories': completedCategories,
      'totalCategories': totalCategories,
      'points': points,
      'timeSpent': timeSpent.inSeconds,
    };
  }

  /// إنشاء من Map
  factory DailyStatistics.fromMap(Map<String, dynamic> map) {
    return DailyStatistics(
      date: DateTime.parse(map['date']),
      completedAzkar: map['completedAzkar'] ?? 0,
      totalAzkar: map['totalAzkar'] ?? 0,
      completedCategories: map['completedCategories'] ?? 0,
      totalCategories: map['totalCategories'] ?? 0,
      points: map['points'] ?? 0,
      timeSpent: Duration(seconds: map['timeSpent'] ?? 0),
    );
  }

  /// نسخ مع تعديل بعض القيم
  DailyStatistics copyWith({
    DateTime? date,
    int? completedAzkar,
    int? totalAzkar,
    int? completedCategories,
    int? totalCategories,
    int? points,
    Duration? timeSpent,
  }) {
    return DailyStatistics(
      date: date ?? this.date,
      completedAzkar: completedAzkar ?? this.completedAzkar,
      totalAzkar: totalAzkar ?? this.totalAzkar,
      completedCategories: completedCategories ?? this.completedCategories,
      totalCategories: totalCategories ?? this.totalCategories,
      points: points ?? this.points,
      timeSpent: timeSpent ?? this.timeSpent,
    );
  }
}

/// نموذج بيانات الإحصائيات الأسبوعية
class WeeklyStatistics {
  final DateTime weekStart;
  final List<DailyStatistics> dailyStats;
  final int totalPoints;
  final int streakDays;

  WeeklyStatistics({
    required this.weekStart,
    required this.dailyStats,
    required this.totalPoints,
    required this.streakDays,
  });

  /// متوسط نسبة الإكمال للأسبوع
  double get averageCompletion {
    if (dailyStats.isEmpty) return 0.0;
    final total = dailyStats.fold<double>(
      0.0,
      (sum, stat) => sum + stat.completionPercentage,
    );
    return total / dailyStats.length;
  }

  /// عدد الأيام المكتملة في الأسبوع
  int get completedDays =>
      dailyStats.where((stat) => stat.isFullyCompleted).length;

  /// إجمالي الأذكار المكتملة في الأسبوع
  int get totalCompletedAzkar =>
      dailyStats.fold(0, (sum, stat) => sum + stat.completedAzkar);
}

/// نموذج بيانات الإحصائيات الشهرية
class MonthlyStatistics {
  final DateTime month;
  final List<DailyStatistics> dailyStats;
  final int totalPoints;
  final int longestStreak;
  final int currentStreak;

  MonthlyStatistics({
    required this.month,
    required this.dailyStats,
    required this.totalPoints,
    required this.longestStreak,
    required this.currentStreak,
  });

  /// متوسط نسبة الإكمال للشهر
  double get averageCompletion {
    if (dailyStats.isEmpty) return 0.0;
    final total = dailyStats.fold<double>(
      0.0,
      (sum, stat) => sum + stat.completionPercentage,
    );
    return total / dailyStats.length;
  }

  /// عدد الأيام المكتملة في الشهر
  int get completedDays =>
      dailyStats.where((stat) => stat.isFullyCompleted).length;

  /// إجمالي الأذكار المكتملة في الشهر
  int get totalCompletedAzkar =>
      dailyStats.fold(0, (sum, stat) => sum + stat.completedAzkar);
}

/// نموذج بيانات السلسلة المتتالية
class StreakData {
  final int currentStreak;
  final int longestStreak;
  final DateTime? lastActiveDate;
  final DateTime? streakStartDate;

  StreakData({
    required this.currentStreak,
    required this.longestStreak,
    this.lastActiveDate,
    this.streakStartDate,
  });

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'lastActiveDate': lastActiveDate?.toIso8601String(),
      'streakStartDate': streakStartDate?.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory StreakData.fromMap(Map<String, dynamic> map) {
    return StreakData(
      currentStreak: map['currentStreak'] ?? 0,
      longestStreak: map['longestStreak'] ?? 0,
      lastActiveDate: map['lastActiveDate'] != null
          ? DateTime.parse(map['lastActiveDate'])
          : null,
      streakStartDate: map['streakStartDate'] != null
          ? DateTime.parse(map['streakStartDate'])
          : null,
    );
  }

  /// نسخ مع تعديل بعض القيم
  StreakData copyWith({
    int? currentStreak,
    int? longestStreak,
    DateTime? lastActiveDate,
    DateTime? streakStartDate,
  }) {
    return StreakData(
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      lastActiveDate: lastActiveDate ?? this.lastActiveDate,
      streakStartDate: streakStartDate ?? this.streakStartDate,
    );
  }
}
