import 'package:flutter/material.dart';

/// ويدجت لرسم أيقونة التطبيق بالضبط كما في الصورة المرفقة
class ExactAppIcon extends StatelessWidget {
  final double size;
  final Color backgroundColor;
  final Color foregroundColor;
  
  const ExactAppIcon({
    super.key,
    this.size = 150,
    this.backgroundColor = const Color(0xFF2E7D32), // اللون الأخضر الرئيسي
    this.foregroundColor = Colors.white,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(size / 8),
      ),
      child: CustomPaint(
        size: Size(size, size),
        painter: ExactIconPainter(
          foregroundColor: foregroundColor,
        ),
      ),
    );
  }
}

/// رسام الأيقونة بالضبط كما في الصورة المرفقة
class ExactIconPainter extends CustomPainter {
  final Color foregroundColor;
  
  ExactIconPainter({
    required this.foregroundColor,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = foregroundColor
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // رسم الحمامة
    _drawDove(canvas, size, paint);
    
    // رسم المسجد
    _drawMosque(canvas, size, paint);
    
    // رسم كلمة "أذكار"
    _drawAzkarText(canvas, size, paint);
  }
  
  /// رسم الحمامة البيضاء
  void _drawDove(Canvas canvas, Size size, Paint paint) {
    final path = Path();
    
    // الجسم الرئيسي للحمامة
    path.moveTo(size.width * 0.3, size.height * 0.3);
    path.lineTo(size.width * 0.35, size.height * 0.25);
    path.lineTo(size.width * 0.4, size.height * 0.2);
    path.lineTo(size.width * 0.45, size.height * 0.25);
    path.lineTo(size.width * 0.5, size.height * 0.3);
    
    // الرأس
    path.lineTo(size.width * 0.55, size.height * 0.25);
    path.lineTo(size.width * 0.6, size.height * 0.27);
    
    // المنقار
    path.lineTo(size.width * 0.63, size.height * 0.3);
    path.lineTo(size.width * 0.6, size.height * 0.32);
    
    // الجزء السفلي من الجسم
    path.lineTo(size.width * 0.55, size.height * 0.35);
    path.lineTo(size.width * 0.5, size.height * 0.4);
    path.lineTo(size.width * 0.45, size.height * 0.38);
    path.lineTo(size.width * 0.4, size.height * 0.35);
    
    // الذيل
    path.lineTo(size.width * 0.35, size.height * 0.38);
    path.lineTo(size.width * 0.3, size.height * 0.35);
    path.lineTo(size.width * 0.25, size.height * 0.3);
    
    // إغلاق المسار
    path.close();
    
    // الجناح
    final wingPath = Path();
    wingPath.moveTo(size.width * 0.35, size.height * 0.28);
    wingPath.lineTo(size.width * 0.25, size.height * 0.35);
    wingPath.lineTo(size.width * 0.35, size.height * 0.33);
    wingPath.lineTo(size.width * 0.45, size.height * 0.35);
    wingPath.close();
    
    // رسم الحمامة
    canvas.save();
    // تحريك الحمامة إلى الموقع الصحيح
    canvas.translate(size.width * 0.05, size.height * 0.05);
    canvas.scale(1.2); // تكبير الحمامة قليلاً
    
    canvas.drawPath(path, paint);
    canvas.drawPath(wingPath, paint);
    
    // رسم العين
    final eyePaint = Paint()
      ..color = const Color(0xFF2E7D32) // لون العين بنفس لون الخلفية
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      Offset(size.width * 0.58, size.height * 0.28),
      size.width * 0.015,
      eyePaint,
    );
    
    canvas.restore();
  }
  
  /// رسم المسجد
  void _drawMosque(Canvas canvas, Size size, Paint paint) {
    // القبة الرئيسية
    final mainDomePath = Path();
    mainDomePath.addArc(
      Rect.fromCenter(
        center: Offset(size.width * 0.7, size.height * 0.48),
        width: size.width * 0.2,
        height: size.height * 0.2,
      ),
      3.14, // بداية القوس (π)
      3.14, // نهاية القوس (π)
    );
    
    // الهلال فوق القبة الرئيسية
    final crescentPath = Path();
    crescentPath.addArc(
      Rect.fromCenter(
        center: Offset(size.width * 0.7, size.height * 0.42),
        width: size.width * 0.05,
        height: size.height * 0.05,
      ),
      0, // بداية القوس
      4.7, // نهاية القوس
    );
    
    // القبة الثانوية
    final secondaryDomePath = Path();
    secondaryDomePath.addArc(
      Rect.fromCenter(
        center: Offset(size.width * 0.55, size.height * 0.52),
        width: size.width * 0.15,
        height: size.height * 0.15,
      ),
      3.14, // بداية القوس (π)
      3.14, // نهاية القوس (π)
    );
    
    // رسم المسجد
    canvas.drawPath(mainDomePath, paint);
    canvas.drawPath(crescentPath, paint);
    canvas.drawPath(secondaryDomePath, paint);
    
    // رسم الخط الأفقي تحت القباب
    canvas.drawRect(
      Rect.fromLTWH(
        size.width * 0.45,
        size.height * 0.55,
        size.width * 0.35,
        size.height * 0.02,
      ),
      paint,
    );
  }
  
  /// رسم كلمة "أذكار"
  void _drawAzkarText(Canvas canvas, Size size, Paint paint) {
    // استخدام مسار لرسم كلمة "أذكار" بالضبط كما في الصورة
    final textPath = Path();
    
    // حرف الألف
    textPath.moveTo(size.width * 0.35, size.height * 0.75);
    textPath.lineTo(size.width * 0.35, size.height * 0.65);
    textPath.lineTo(size.width * 0.38, size.height * 0.65);
    textPath.lineTo(size.width * 0.38, size.height * 0.75);
    textPath.close();
    
    // حرف الذال
    textPath.moveTo(size.width * 0.45, size.height * 0.7);
    textPath.lineTo(size.width * 0.55, size.height * 0.7);
    textPath.lineTo(size.width * 0.55, size.height * 0.65);
    textPath.lineTo(size.width * 0.5, size.height * 0.65);
    textPath.lineTo(size.width * 0.45, size.height * 0.68);
    textPath.close();
    
    // النقطة فوق حرف الذال
    textPath.addOval(
      Rect.fromCenter(
        center: Offset(size.width * 0.5, size.height * 0.62),
        width: size.width * 0.03,
        height: size.height * 0.03,
      ),
    );
    
    // حرف الكاف
    textPath.moveTo(size.width * 0.6, size.height * 0.75);
    textPath.lineTo(size.width * 0.6, size.height * 0.65);
    textPath.lineTo(size.width * 0.65, size.height * 0.65);
    textPath.lineTo(size.width * 0.7, size.height * 0.7);
    textPath.lineTo(size.width * 0.65, size.height * 0.7);
    textPath.lineTo(size.width * 0.63, size.height * 0.75);
    textPath.close();
    
    // حرف الراء
    textPath.moveTo(size.width * 0.75, size.height * 0.75);
    textPath.lineTo(size.width * 0.75, size.height * 0.7);
    textPath.lineTo(size.width * 0.8, size.height * 0.65);
    textPath.lineTo(size.width * 0.8, size.height * 0.7);
    textPath.lineTo(size.width * 0.78, size.height * 0.75);
    textPath.close();
    
    canvas.drawPath(textPath, paint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
