/// نموذج بيانات الإحصائيات اليومية المحسن
class DailyStatistics {
  final int id;
  final DateTime date;
  final int completedAzkar;
  final int totalAzkar;
  final int completedCategories;
  final int totalCategories;
  final int points;
  final Duration timeSpent;
  final int appOpenCount;
  final int tasbihCount;
  final int favoritesAdded;
  final int quranReadingTimeMinutes;
  final Map<String, int> categoryCompletions;
  final DateTime createdAt;
  final DateTime updatedAt;

  DailyStatistics({
    required this.id,
    required this.date,
    required this.completedAzkar,
    required this.totalAzkar,
    required this.completedCategories,
    required this.totalCategories,
    required this.points,
    required this.timeSpent,
    required this.appOpenCount,
    required this.tasbihCount,
    required this.favoritesAdded,
    required this.quranReadingTimeMinutes,
    required this.categoryCompletions,
    required this.createdAt,
    required this.updatedAt,
  });

  /// إنشاء إحصائيات يومية فارغة
  factory DailyStatistics.empty(DateTime date) {
    return DailyStatistics(
      id: 0,
      date: date,
      completedAzkar: 0,
      totalAzkar: 0,
      completedCategories: 0,
      totalCategories: 0,
      points: 0,
      timeSpent: Duration.zero,
      appOpenCount: 0,
      tasbihCount: 0,
      favoritesAdded: 0,
      quranReadingTimeMinutes: 0,
      categoryCompletions: {},
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// نسبة الإكمال (0.0 إلى 1.0)
  double get completionPercentage =>
      totalAzkar > 0 ? completedAzkar / totalAzkar : 0.0;

  /// ما إذا كان اليوم مكتملاً بالكامل
  bool get isFullyCompleted => completedAzkar >= totalAzkar;

  /// تحويل إلى Map لحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': date.toIso8601String().split('T')[0], // YYYY-MM-DD format
      'completedAzkar': completedAzkar,
      'totalAzkar': totalAzkar,
      'completedCategories': completedCategories,
      'totalCategories': totalCategories,
      'points': points,
      'timeSpent': timeSpent.inSeconds,
      'appOpenCount': appOpenCount,
      'tasbihCount': tasbihCount,
      'favoritesAdded': favoritesAdded,
      'quranReadingTimeMinutes': quranReadingTimeMinutes,
      'categoryCompletions': _mapToJson(categoryCompletions),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// تحويل Map إلى JSON string
  static String _mapToJson(Map<String, int> map) {
    if (map.isEmpty) return '{}';
    final entries = map.entries.map((e) => '"${e.key}":${e.value}').join(',');
    return '{$entries}';
  }

  /// تحويل JSON string إلى Map
  static Map<String, int> _jsonToMap(String json) {
    if (json.isEmpty || json == '{}') return {};

    try {
      final content = json.substring(1, json.length - 1);
      if (content.isEmpty) return {};

      final pairs = content.split(',');
      final result = <String, int>{};

      for (final pair in pairs) {
        final parts = pair.split(':');
        if (parts.length == 2) {
          final key = parts[0].replaceAll('"', '').trim();
          final value = int.tryParse(parts[1].trim()) ?? 0;
          result[key] = value;
        }
      }

      return result;
    } catch (e) {
      return {};
    }
  }

  /// إنشاء من Map
  factory DailyStatistics.fromMap(Map<String, dynamic> map) {
    return DailyStatistics(
      id: map['id'] ?? 0,
      date: DateTime.parse(map['date']),
      completedAzkar: map['completedAzkar'] ?? 0,
      totalAzkar: map['totalAzkar'] ?? 0,
      completedCategories: map['completedCategories'] ?? 0,
      totalCategories: map['totalCategories'] ?? 0,
      points: map['points'] ?? 0,
      timeSpent: Duration(seconds: map['timeSpent'] ?? 0),
      appOpenCount: map['appOpenCount'] ?? 0,
      tasbihCount: map['tasbihCount'] ?? 0,
      favoritesAdded: map['favoritesAdded'] ?? 0,
      quranReadingTimeMinutes: map['quranReadingTimeMinutes'] ?? 0,
      categoryCompletions: _jsonToMap(map['categoryCompletions'] ?? '{}'),
      createdAt: DateTime.parse(
        map['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        map['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  /// نسخ مع تعديل بعض القيم
  DailyStatistics copyWith({
    int? id,
    DateTime? date,
    int? completedAzkar,
    int? totalAzkar,
    int? completedCategories,
    int? totalCategories,
    int? points,
    Duration? timeSpent,
    int? appOpenCount,
    int? tasbihCount,
    int? favoritesAdded,
    int? quranReadingTimeMinutes,
    Map<String, int>? categoryCompletions,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DailyStatistics(
      id: id ?? this.id,
      date: date ?? this.date,
      completedAzkar: completedAzkar ?? this.completedAzkar,
      totalAzkar: totalAzkar ?? this.totalAzkar,
      completedCategories: completedCategories ?? this.completedCategories,
      totalCategories: totalCategories ?? this.totalCategories,
      points: points ?? this.points,
      timeSpent: timeSpent ?? this.timeSpent,
      appOpenCount: appOpenCount ?? this.appOpenCount,
      tasbihCount: tasbihCount ?? this.tasbihCount,
      favoritesAdded: favoritesAdded ?? this.favoritesAdded,
      quranReadingTimeMinutes:
          quranReadingTimeMinutes ?? this.quranReadingTimeMinutes,
      categoryCompletions: categoryCompletions ?? this.categoryCompletions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }
}

/// نموذج بيانات الإحصائيات الأسبوعية
class WeeklyStatistics {
  final DateTime weekStart;
  final List<DailyStatistics> dailyStats;
  final int totalPoints;
  final int streakDays;

  WeeklyStatistics({
    required this.weekStart,
    required this.dailyStats,
    required this.totalPoints,
    required this.streakDays,
  });

  /// متوسط نسبة الإكمال للأسبوع
  double get averageCompletion {
    if (dailyStats.isEmpty) return 0.0;
    final total = dailyStats.fold<double>(
      0.0,
      (sum, stat) => sum + stat.completionPercentage,
    );
    return total / dailyStats.length;
  }

  /// عدد الأيام المكتملة في الأسبوع
  int get completedDays =>
      dailyStats.where((stat) => stat.isFullyCompleted).length;

  /// إجمالي الأذكار المكتملة في الأسبوع
  int get totalCompletedAzkar =>
      dailyStats.fold(0, (sum, stat) => sum + stat.completedAzkar);
}

/// نموذج بيانات الإحصائيات الشهرية
class MonthlyStatistics {
  final DateTime month;
  final List<DailyStatistics> dailyStats;
  final int totalPoints;
  final int longestStreak;
  final int currentStreak;

  MonthlyStatistics({
    required this.month,
    required this.dailyStats,
    required this.totalPoints,
    required this.longestStreak,
    required this.currentStreak,
  });

  /// متوسط نسبة الإكمال للشهر
  double get averageCompletion {
    if (dailyStats.isEmpty) return 0.0;
    final total = dailyStats.fold<double>(
      0.0,
      (sum, stat) => sum + stat.completionPercentage,
    );
    return total / dailyStats.length;
  }

  /// عدد الأيام المكتملة في الشهر
  int get completedDays =>
      dailyStats.where((stat) => stat.isFullyCompleted).length;

  /// إجمالي الأذكار المكتملة في الشهر
  int get totalCompletedAzkar =>
      dailyStats.fold(0, (sum, stat) => sum + stat.completedAzkar);
}

/// نموذج بيانات السلسلة المتتالية
class StreakData {
  final int currentStreak;
  final int longestStreak;
  final DateTime? lastActiveDate;
  final DateTime? streakStartDate;

  StreakData({
    required this.currentStreak,
    required this.longestStreak,
    this.lastActiveDate,
    this.streakStartDate,
  });

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'lastActiveDate': lastActiveDate?.toIso8601String(),
      'streakStartDate': streakStartDate?.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory StreakData.fromMap(Map<String, dynamic> map) {
    return StreakData(
      currentStreak: map['currentStreak'] ?? 0,
      longestStreak: map['longestStreak'] ?? 0,
      lastActiveDate:
          map['lastActiveDate'] != null
              ? DateTime.parse(map['lastActiveDate'])
              : null,
      streakStartDate:
          map['streakStartDate'] != null
              ? DateTime.parse(map['streakStartDate'])
              : null,
    );
  }

  /// نسخ مع تعديل بعض القيم
  StreakData copyWith({
    int? currentStreak,
    int? longestStreak,
    DateTime? lastActiveDate,
    DateTime? streakStartDate,
  }) {
    return StreakData(
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      lastActiveDate: lastActiveDate ?? this.lastActiveDate,
      streakStartDate: streakStartDate ?? this.streakStartDate,
    );
  }
}
