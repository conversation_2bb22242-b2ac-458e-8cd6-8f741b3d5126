/// نموذج لطرق عرض القرآن الكريم
enum QuranViewMode {
  /// عرض السور كقائمة
  list,

  /// عرض السور كصفحات
  pages,

  /// عرض السور كمصحف
  mushaf,

  /// عرض السور بالخط العثماني مع التمرير المستمر
  uthmaniContinuous,
}

/// امتداد لتحويل طريقة العرض إلى نص
extension QuranViewModeExtension on QuranViewMode {
  /// الحصول على اسم طريقة العرض بالعربية
  String get arabicName {
    switch (this) {
      case QuranViewMode.list:
        return 'قائمة';
      case QuranViewMode.pages:
        return 'صفحات';
      case QuranViewMode.mushaf:
        return 'مصحف';
      case QuranViewMode.uthmaniContinuous:
        return 'عثماني مستمر';
    }
  }

  /// الحصول على وصف طريقة العرض بالعربية
  String get description {
    switch (this) {
      case QuranViewMode.list:
        return 'عرض السور كقائمة مع إمكانية التمرير';
      case QuranViewMode.pages:
        return 'عرض السور كصفحات منفصلة';
      case QuranViewMode.mushaf:
        return 'عرض السور كمصحف متكامل';
      case QuranViewMode.uthmaniContinuous:
        return 'عرض السور بالخط العثماني مع التمرير المستمر';
    }
  }
}
