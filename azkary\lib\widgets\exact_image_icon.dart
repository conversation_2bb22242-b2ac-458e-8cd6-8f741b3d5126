import 'package:flutter/material.dart';

/// ويدجت لعرض أيقونة التطبيق باستخدام الصورة المرفقة بالضبط
class ExactImageIcon extends StatelessWidget {
  final double size;
  final BoxFit fit;
  
  const ExactImageIcon({
    super.key,
    this.size = 150,
    this.fit = BoxFit.contain,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(size / 8),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF2E7D32).withAlpha(76), // 0.3 * 255 = 76
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      // استخدام الصورة المرفقة بالضبط
      child: ClipRRect(
        borderRadius: BorderRadius.circular(size / 8),
        child: Image.asset(
          'assets/images/app_icon.png',
          width: size,
          height: size,
          fit: fit,
        ),
      ),
    );
  }
}
