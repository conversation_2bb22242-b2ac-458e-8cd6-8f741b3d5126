import 'package:flutter/material.dart';

/// عنصر قائمة متحرك
class AnimatedListItem extends StatelessWidget {
  final int index;
  final Widget child;
  final bool animate;

  const AnimatedListItem({
    super.key,
    required this.index,
    required this.child,
    required this.animate,
  });

  @override
  Widget build(BuildContext context) {
    // إذا كانت الحركة غير مفعلة، أعرض العنصر مباشرة
    if (!animate) {
      return child;
    }

    // حساب تأخير الحركة بناءً على الفهرس - محسن للأداء
    final delay = Duration(milliseconds: 50 * index); // تقليل التأخير

    return FutureBuilder(
      future: Future.delayed(delay),
      builder: (context, snapshot) {
        return AnimatedOpacity(
          opacity: 1.0,
          duration: const Duration(milliseconds: 250), // تحسين الأداء
          curve: Curves.easeOut, // منحنى أبسط
          child: AnimatedSlide(
            offset: Offset.zero,
            duration: const Duration(milliseconds: 250), // تحسين الأداء
            curve: Curves.easeOut, // منحنى أبسط
            child: child,
          ),
        );
      },
    );
  }
}
