import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/azkar_model.dart';
import '../constants/azkar_data.dart';
import '../utils/logger.dart';

/// خدمة قاعدة البيانات للويب باستخدام SharedPreferences
class DatabaseServiceWeb {
  static final DatabaseServiceWeb _instance = DatabaseServiceWeb._internal();
  static SharedPreferences? _prefs;

  factory DatabaseServiceWeb() {
    return _instance;
  }

  DatabaseServiceWeb._internal();

  Future<SharedPreferences> get prefs async {
    if (_prefs != null) return _prefs!;
    _prefs = await SharedPreferences.getInstance();
    return _prefs!;
  }

  // مفاتيح التخزين
  static const String _categoriesKey = 'categories_data';
  static const String _azkarKey = 'azkar_data';
  static const String _customAzkarKey = 'custom_azkar_data';
  static const String _favoritesKey = 'favorites_data';
  static const String _countersKey = 'counters_data';
  static const String _isInitializedKey = 'database_initialized';

  /// تهيئة قاعدة البيانات
  Future<void> initDatabase() async {
    try {
      AppLogger.info('بدء تهيئة قاعدة البيانات للويب...');
      final prefs = await this.prefs;

      final isInitialized = prefs.getBool(_isInitializedKey) ?? false;

      if (!isInitialized) {
        AppLogger.info(
          'قاعدة البيانات غير مهيأة، جاري إدخال البيانات الأولية...',
        );
        await _insertInitialData();
        await prefs.setBool(_isInitializedKey, true);
        AppLogger.info('تم تهيئة قاعدة البيانات بنجاح');
      } else {
        AppLogger.info('قاعدة البيانات مهيأة مسبقاً');
        // التحقق من وجود البيانات
        await _checkAndRepairData();
      }
    } catch (e) {
      AppLogger.error('خطأ في تهيئة قاعدة البيانات: $e');
      // إعادة المحاولة
      await _insertInitialData();
    }
  }

  /// التحقق من البيانات وإصلاحها إذا لزم الأمر
  Future<void> _checkAndRepairData() async {
    try {
      final categories = await getCategories();
      final azkar = await getAllAzkar();

      if (categories.isEmpty || azkar.isEmpty) {
        AppLogger.info('البيانات ناقصة، جاري إعادة إدخال البيانات الأولية...');
        await _insertInitialData();
      }
    } catch (e) {
      AppLogger.error('خطأ في فحص البيانات: $e');
      await _insertInitialData();
    }
  }

  /// إدخال البيانات الأولية
  Future<void> _insertInitialData() async {
    try {
      final prefs = await this.prefs;

      // إدخال التصنيفات
      final categories = [
        Category(
          id: 1,
          name: 'أذكار الصباح',
          icon: 'sun',
          count: 31,
          description: 'أذكار الصباح',
        ),
        Category(
          id: 2,
          name: 'أذكار المساء',
          icon: 'moon',
          count: 31,
          description: 'أذكار المساء',
        ),
        Category(
          id: 3,
          name: 'أذكار النوم',
          icon: 'bed',
          count: 15,
          description: 'أذكار النوم',
        ),
        Category(
          id: 4,
          name: 'أذكار الاستيقاظ',
          icon: 'alarm',
          count: 8,
          description: 'أذكار الاستيقاظ',
        ),
        Category(
          id: 5,
          name: 'أذكار الصلاة',
          icon: 'prayer',
          count: 12,
          description: 'أذكار الصلاة',
        ),
        Category(
          id: 6,
          name: 'أذكار الاستغفار',
          icon: 'heart',
          count: 10,
          description: 'أذكار الاستغفار',
        ),
        Category(
          id: 7,
          name: 'أذكار السفر',
          icon: 'travel',
          count: 10,
          description: 'أذكار السفر',
        ),
        Category(
          id: 8,
          name: 'أذكار الطعام',
          icon: 'food',
          count: 8,
          description: 'أذكار الطعام',
        ),
        Category(
          id: 9,
          name: 'أذكار المسجد',
          icon: 'mosque',
          count: 6,
          description: 'أذكار المسجد',
        ),
        Category(
          id: 10,
          name: 'أذكار متنوعة',
          icon: 'misc',
          count: 20,
          description: 'أذكار متنوعة',
        ),
      ];

      final categoriesJson = categories.map((c) => c.toMap()).toList();
      await prefs.setString(_categoriesKey, json.encode(categoriesJson));

      // إدخال الأذكار
      final azkarJson = azkarData.map((z) => z.toMap()).toList();
      await prefs.setString(_azkarKey, json.encode(azkarJson));

      // تهيئة قوائم فارغة للبيانات الأخرى
      await prefs.setString(_customAzkarKey, json.encode([]));
      await prefs.setString(_favoritesKey, json.encode([]));
      await prefs.setString(_countersKey, json.encode({}));

      AppLogger.info(
        'تم إدخال ${categories.length} تصنيف و ${azkarData.length} ذكر',
      );
    } catch (e) {
      AppLogger.error('خطأ في إدخال البيانات الأولية: $e');
    }
  }

  /// الحصول على جميع التصنيفات
  Future<List<Category>> getCategories() async {
    try {
      final prefs = await this.prefs;
      final categoriesString = prefs.getString(_categoriesKey);

      if (categoriesString == null) {
        AppLogger.warning('لا توجد تصنيفات محفوظة');
        return [];
      }

      final List<dynamic> categoriesJson = json.decode(categoriesString);
      return categoriesJson.map((json) => Category.fromMap(json)).toList();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على التصنيفات: $e');
      return [];
    }
  }

  /// الحصول على جميع الأذكار
  Future<List<Zikr>> getAllAzkar() async {
    try {
      final prefs = await this.prefs;
      final azkarString = prefs.getString(_azkarKey);

      if (azkarString == null) {
        AppLogger.warning('لا توجد أذكار محفوظة');
        return [];
      }

      final List<dynamic> azkarJson = json.decode(azkarString);
      return azkarJson.map((json) => Zikr.fromMap(json)).toList();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الأذكار: $e');
      return [];
    }
  }

  /// الحصول على أذكار تصنيف معين
  Future<List<Zikr>> getAzkarByCategory(String category) async {
    try {
      final allAzkar = await getAllAzkar();
      return allAzkar.where((zikr) => zikr.category == category).toList();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على أذكار التصنيف $category: $e');
      return [];
    }
  }

  /// الحصول على الأذكار المفضلة
  Future<List<Zikr>> getFavoriteAzkar() async {
    try {
      final prefs = await this.prefs;
      final favoritesString = prefs.getString(_favoritesKey);

      if (favoritesString == null) {
        return [];
      }

      final List<dynamic> favoriteIds = json.decode(favoritesString);
      final allAzkar = await getAllAzkar();

      return allAzkar.where((zikr) => favoriteIds.contains(zikr.id)).toList();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الأذكار المفضلة: $e');
      return [];
    }
  }

  /// إضافة/إزالة ذكر من المفضلة
  Future<void> toggleFavorite(int zikrId) async {
    try {
      final prefs = await this.prefs;
      final favoritesString = prefs.getString(_favoritesKey) ?? '[]';
      final List<dynamic> favoriteIds = json.decode(favoritesString);

      if (favoriteIds.contains(zikrId)) {
        favoriteIds.remove(zikrId);
      } else {
        favoriteIds.add(zikrId);
      }

      await prefs.setString(_favoritesKey, json.encode(favoriteIds));
      AppLogger.info('تم تحديث حالة المفضلة للذكر $zikrId');
    } catch (e) {
      AppLogger.error('خطأ في تحديث المفضلة: $e');
    }
  }

  /// التحقق من كون الذكر مفضل
  Future<bool> isFavorite(int zikrId) async {
    try {
      final prefs = await this.prefs;
      final favoritesString = prefs.getString(_favoritesKey) ?? '[]';
      final List<dynamic> favoriteIds = json.decode(favoritesString);

      return favoriteIds.contains(zikrId);
    } catch (e) {
      AppLogger.error('خطأ في فحص المفضلة: $e');
      return false;
    }
  }

  /// تحديث عداد الذكر
  Future<void> updateZikrCounter(int zikrId, int count) async {
    try {
      final prefs = await this.prefs;
      final countersString = prefs.getString(_countersKey) ?? '{}';
      final Map<String, dynamic> counters = json.decode(countersString);

      counters[zikrId.toString()] = count;

      await prefs.setString(_countersKey, json.encode(counters));
      AppLogger.info('تم تحديث عداد الذكر $zikrId إلى $count');
    } catch (e) {
      AppLogger.error('خطأ في تحديث العداد: $e');
    }
  }

  /// الحصول على عداد الذكر
  Future<int> getZikrCounter(int zikrId) async {
    try {
      final prefs = await this.prefs;
      final countersString = prefs.getString(_countersKey) ?? '{}';
      final Map<String, dynamic> counters = json.decode(countersString);

      return counters[zikrId.toString()] ?? 0;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على العداد: $e');
      return 0;
    }
  }

  /// الحصول على الأذكار المخصصة
  Future<List<Zikr>> getCustomAzkar() async {
    try {
      final prefs = await this.prefs;
      final customAzkarString = prefs.getString(_customAzkarKey) ?? '[]';
      final List<dynamic> customAzkarJson = json.decode(customAzkarString);

      return customAzkarJson.map((json) => Zikr.fromMap(json)).toList();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الأذكار المخصصة: $e');
      return [];
    }
  }

  /// إضافة ذكر مخصص
  Future<void> addCustomZikr(Zikr zikr) async {
    try {
      final customAzkar = await getCustomAzkar();

      // إنشاء ID جديد
      final newId =
          customAzkar.isEmpty
              ? 1000
              : customAzkar.map((z) => z.id).reduce((a, b) => a > b ? a : b) +
                  1;
      final newZikr = Zikr(
        id: newId,
        text: zikr.text,
        count: zikr.count,
        source: zikr.source.isNotEmpty ? zikr.source : 'مخصص',
        category: 'أذكاري',
        reference: zikr.reference,
        fadl: zikr.fadl,
      );

      customAzkar.add(newZikr);

      final prefs = await this.prefs;
      final customAzkarJson = customAzkar.map((z) => z.toMap()).toList();
      await prefs.setString(_customAzkarKey, json.encode(customAzkarJson));

      AppLogger.info('تم إضافة ذكر مخصص جديد بالمعرف $newId');
    } catch (e) {
      AppLogger.error('خطأ في إضافة الذكر المخصص: $e');
    }
  }

  /// إضافة تصنيف جديد
  Future<void> addCategory(Category category) async {
    try {
      final categories = await getCategories();

      // التحقق من عدم وجود تصنيف بنفس الاسم
      if (categories.any((c) => c.name == category.name)) {
        AppLogger.warning('التصنيف ${category.name} موجود مسبقاً');
        return;
      }

      // إنشاء ID جديد
      final newId =
          categories.isEmpty
              ? 1
              : categories.map((c) => c.id).reduce((a, b) => a > b ? a : b) + 1;
      final newCategory = Category(
        id: newId,
        name: category.name,
        icon: category.icon,
        count: 0,
        description: category.description,
      );

      categories.add(newCategory);

      final prefs = await this.prefs;
      final categoriesJson = categories.map((c) => c.toMap()).toList();
      await prefs.setString(_categoriesKey, json.encode(categoriesJson));

      AppLogger.info('تم إضافة تصنيف جديد: ${category.name} بالمعرف $newId');
    } catch (e) {
      AppLogger.error('خطأ في إضافة التصنيف: $e');
    }
  }

  /// إضافة ذكر إلى تصنيف
  Future<void> addZikrToCategory(Zikr zikr, String categoryName) async {
    try {
      final allAzkar = await getAllAzkar();

      // إنشاء ID جديد
      final newId =
          allAzkar.isEmpty
              ? 1
              : allAzkar.map((z) => z.id).reduce((a, b) => a > b ? a : b) + 1;
      final newZikr = Zikr(
        id: newId,
        text: zikr.text,
        count: zikr.count,
        source: zikr.source.isNotEmpty ? zikr.source : 'مخصص',
        category: categoryName,
        reference: zikr.reference,
        fadl: zikr.fadl,
      );

      allAzkar.add(newZikr);

      final prefs = await this.prefs;
      final azkarJson = allAzkar.map((z) => z.toMap()).toList();
      await prefs.setString(_azkarKey, json.encode(azkarJson));

      // تحديث عدد الأذكار في التصنيف
      final categories = await getCategories();
      final categoryIndex = categories.indexWhere(
        (c) => c.name == categoryName,
      );
      if (categoryIndex != -1) {
        categories[categoryIndex] = categories[categoryIndex].copyWith(
          count: categories[categoryIndex].count + 1,
        );
        final categoriesJson = categories.map((c) => c.toMap()).toList();
        await prefs.setString(_categoriesKey, json.encode(categoriesJson));
      }

      AppLogger.info('تم إضافة ذكر جديد للتصنيف $categoryName بالمعرف $newId');
    } catch (e) {
      AppLogger.error('خطأ في إضافة الذكر للتصنيف: $e');
    }
  }

  /// البحث في الأذكار
  Future<List<Zikr>> searchAzkar(String query) async {
    try {
      final allAzkar = await getAllAzkar();
      final customAzkar = await getCustomAzkar();

      // دمج جميع الأذكار
      final combinedAzkar = [...allAzkar, ...customAzkar];

      // البحث في النص
      final results =
          combinedAzkar
              .where(
                (zikr) =>
                    zikr.text.contains(query) ||
                    zikr.category.contains(query) ||
                    zikr.source.contains(query),
              )
              .toList();

      AppLogger.info('تم العثور على ${results.length} نتيجة للبحث: $query');
      return results;
    } catch (e) {
      AppLogger.error('خطأ في البحث: $e');
      return [];
    }
  }

  /// الحصول على ذكر عشوائي
  Future<Zikr?> getRandomZikr() async {
    try {
      final allAzkar = await getAllAzkar();

      if (allAzkar.isEmpty) {
        AppLogger.warning('لا توجد أذكار للاختيار منها');
        return null;
      }

      final random = DateTime.now().millisecondsSinceEpoch % allAzkar.length;
      return allAzkar[random];
    } catch (e) {
      AppLogger.error('خطأ في الحصول على ذكر عشوائي: $e');
      return null;
    }
  }

  /// إعادة تعيين جميع العدادات
  Future<void> resetAllCurrentCounts() async {
    try {
      final prefs = await this.prefs;
      await prefs.setString(_countersKey, json.encode({}));
      AppLogger.info('تم إعادة تعيين جميع العدادات');
    } catch (e) {
      AppLogger.error('خطأ في إعادة تعيين العدادات: $e');
    }
  }

  /// حذف ذكر مخصص
  Future<void> deleteCustomZikr(int zikrId) async {
    try {
      final customAzkar = await getCustomAzkar();
      customAzkar.removeWhere((zikr) => zikr.id == zikrId);

      final prefs = await this.prefs;
      final customAzkarJson = customAzkar.map((z) => z.toMap()).toList();
      await prefs.setString(_customAzkarKey, json.encode(customAzkarJson));

      AppLogger.info('تم حذف الذكر المخصص $zikrId');
    } catch (e) {
      AppLogger.error('خطأ في حذف الذكر المخصص: $e');
    }
  }

  /// إعادة تعيين جميع البيانات
  Future<void> resetAllData() async {
    try {
      final prefs = await this.prefs;

      await prefs.remove(_categoriesKey);
      await prefs.remove(_azkarKey);
      await prefs.remove(_customAzkarKey);
      await prefs.remove(_favoritesKey);
      await prefs.remove(_countersKey);
      await prefs.remove(_isInitializedKey);

      AppLogger.info('تم إعادة تعيين جميع البيانات');

      // إعادة تهيئة البيانات
      await initDatabase();
    } catch (e) {
      AppLogger.error('خطأ في إعادة تعيين البيانات: $e');
    }
  }

  /// التحقق من حالة قاعدة البيانات
  Future<bool> checkAndReinitializeDatabase() async {
    try {
      final categories = await getCategories();
      final azkar = await getAllAzkar();

      if (categories.isEmpty || azkar.isEmpty) {
        AppLogger.info('البيانات ناقصة، جاري إعادة التهيئة...');
        await _insertInitialData();
        return true;
      }

      return false;
    } catch (e) {
      AppLogger.error('خطأ في فحص قاعدة البيانات: $e');
      await _insertInitialData();
      return true;
    }
  }
}
