class Zikr {
  final int id;
  final String text;
  final int count;
  final String source;
  final String category;
  final String? reference;
  final String? fadl; // فضل الذكر
  bool isFavorite;
  int currentCount;
  final bool isCustom; // علامة لتحديد ما إذا كان الذكر مخصصاً (أضافه المستخدم)

  Zikr({
    required this.id,
    required this.text,
    required this.count,
    required this.source,
    required this.category,
    this.reference,
    this.fadl,
    this.isFavorite = false,
    this.currentCount = 0,
    this.isCustom = false, // افتراضياً ليس ذكراً مخصصاً
  });

  // إنشاء كائن Zikr فارغ
  factory Zikr.empty() {
    return Zikr(id: 0, text: '', count: 0, source: '', category: '');
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'text': text,
      'count': count,
      'source': source,
      'category': category,
      'reference': reference,
      'fadl': fadl,
      'isFavorite': isFavorite ? 1 : 0,
      'currentCount': currentCount,
      'isCustom': isCustom ? 1 : 0, // تخزين كقيمة رقمية (0 أو 1)
    };
  }

  factory Zikr.fromMap(Map<String, dynamic> map) {
    return Zikr(
      id: map['id'],
      text: map['text'],
      count: map['count'],
      source: map['source'],
      category: map['category'],
      reference: map['reference'],
      fadl: map['fadl'],
      isFavorite: map['isFavorite'] == 1,
      currentCount: map['currentCount'] ?? 0,
      isCustom: map['isCustom'] == 1, // قراءة القيمة كمنطقية (true/false)
    );
  }

  Zikr copyWith({
    int? id,
    String? text,
    int? count,
    String? source,
    String? category,
    String? reference,
    String? fadl,
    bool? isFavorite,
    int? currentCount,
    bool? isCustom,
  }) {
    return Zikr(
      id: id ?? this.id,
      text: text ?? this.text,
      count: count ?? this.count,
      source: source ?? this.source,
      category: category ?? this.category,
      reference: reference ?? this.reference,
      fadl: fadl ?? this.fadl,
      isFavorite: isFavorite ?? this.isFavorite,
      currentCount: currentCount ?? this.currentCount,
      isCustom: isCustom ?? this.isCustom,
    );
  }
}

// نموذج بيانات للأذكار الخاصة
class CustomZikr {
  final int? id; // قد يكون null عند إنشاء ذكر جديد
  final String text;
  final int count;
  final String? fadl; // فضل الذكر (اختياري)
  bool isFavorite;
  int currentCount;
  final DateTime createdAt;

  CustomZikr({
    this.id,
    required this.text,
    required this.count,
    this.fadl,
    this.isFavorite = false,
    this.currentCount = 0,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  // تحويل الكائن إلى Map لتخزينه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'text': text,
      'count': count,
      'fadl': fadl,
      'isFavorite': isFavorite ? 1 : 0,
      'currentCount': currentCount,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // إنشاء كائن من Map مستخرج من قاعدة البيانات
  factory CustomZikr.fromMap(Map<String, dynamic> map) {
    return CustomZikr(
      id: map['id'],
      text: map['text'],
      count: map['count'],
      fadl: map['fadl'],
      isFavorite: map['isFavorite'] == 1,
      currentCount: map['currentCount'] ?? 0,
      createdAt:
          map['createdAt'] != null
              ? DateTime.parse(map['createdAt'])
              : DateTime.now(),
    );
  }

  // تحويل CustomZikr إلى Zikr للتوافق مع بقية التطبيق
  Zikr toZikr() {
    return Zikr(
      id: id ?? 0,
      text: text,
      count: count,
      source: 'أذكاري الخاصة', // قيمة افتراضية
      category: 'أذكاري الخاصة',
      fadl: fadl,
      isFavorite: isFavorite,
      currentCount: currentCount,
    );
  }

  // إنشاء نسخة معدلة من الكائن
  CustomZikr copyWith({
    int? id,
    String? text,
    int? count,
    String? fadl,
    bool? isFavorite,
    int? currentCount,
    DateTime? createdAt,
  }) {
    return CustomZikr(
      id: id ?? this.id,
      text: text ?? this.text,
      count: count ?? this.count,
      fadl: fadl ?? this.fadl,
      isFavorite: isFavorite ?? this.isFavorite,
      currentCount: currentCount ?? this.currentCount,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class Category {
  final int id;
  final String name;
  final String icon;
  final int count;
  final String description;

  Category({
    required this.id,
    required this.name,
    required this.icon,
    required this.count,
    required this.description,
  });

  // إنشاء نسخة جديدة من الكائن مع تغيير بعض الخصائص
  Category copyWith({
    int? id,
    String? name,
    String? icon,
    int? count,
    String? description,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      count: count ?? this.count,
      description: description ?? this.description,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'icon': icon,
      'count': count,
      'description': description,
    };
  }

  factory Category.fromMap(Map<String, dynamic> map) {
    return Category(
      id: map['id'],
      name: map['name'],
      icon: map['icon'],
      count: map['count'],
      description: map['description'],
    );
  }
}
