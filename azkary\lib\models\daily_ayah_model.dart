import 'package:azkary/models/quran_model.dart';

/// نموذج بيانات للآية اليومية
class DailyAyah {
  final Surah surah;
  final Ayah ayah;
  final DateTime date;
  final String? tafsir;
  final bool isRead; // إضافة حقل لتتبع حالة قراءة الآية

  DailyAyah({
    required this.surah,
    required this.ayah,
    required this.date,
    this.tafsir,
    this.isRead = false, // القيمة الافتراضية هي عدم القراءة
  });

  /// إنشاء نموذج من بيانات JSON
  factory DailyAyah.fromJson(Map<String, dynamic> json) {
    return DailyAyah(
      surah: Surah.fromJson(json['surah']),
      ayah: Ayah.fromJson(json['ayah']),
      date: DateTime.parse(json['date']),
      tafsir: json['tafsir'],
      isRead: json['isRead'] ?? false, // استرجاع حالة القراءة
    );
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'surah': surah.toJson(),
      'ayah': ayah.toJson(),
      'date': date.toIso8601String(),
      'tafsir': tafsir,
      'isRead': isRead, // حفظ حالة القراءة
    };
  }

  /// إنشاء نسخة جديدة من الآية مع تغيير حالة القراءة
  DailyAyah copyWith({bool? isRead}) {
    return DailyAyah(
      surah: surah,
      ayah: ayah,
      date: date,
      tafsir: tafsir,
      isRead: isRead ?? this.isRead,
    );
  }
}
