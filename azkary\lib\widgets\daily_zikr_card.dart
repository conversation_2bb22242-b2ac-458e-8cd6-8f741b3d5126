import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart' as share_plus;
import '../models/azkar_model.dart';
import '../screens/zikr_detail_screen.dart';

class DailyZikrCard extends StatelessWidget {
  final Zikr zikr;

  const DailyZikrCard({super.key, required this.zikr});

  void _copyZikr(BuildContext context) {
    Clipboard.setData(ClipboardData(text: zikr.text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم نسخ الذكر'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _shareZikr() {
    share_plus.SharePlus.instance.share(
      share_plus.ShareParams(
        text: '${zikr.text}\n\nالمصدر: ${zikr.source}\n\nمن تطبيق أذكاري',
      ),
    );
  }

  // دالة لتنسيق التاريخ باللغة العربية
  String _getFormattedDate() {
    DateTime now = DateTime.now();

    // قائمة بأسماء أيام الأسبوع بالعربية
    List<String> arabicDays = [
      'الأحد',
      'الإثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];

    // قائمة بأسماء الأشهر بالعربية
    List<String> arabicMonths = [
      'يناير',
      'فبراير',
      'مارس',
      'إبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    // تحويل الأرقام إلى أرقام عربية
    String toArabicNumber(int number) {
      String result = '';
      String numberStr = number.toString();

      Map<String, String> arabicNumbers = {
        '0': '٠',
        '1': '١',
        '2': '٢',
        '3': '٣',
        '4': '٤',
        '5': '٥',
        '6': '٦',
        '7': '٧',
        '8': '٨',
        '9': '٩',
      };

      for (int i = 0; i < numberStr.length; i++) {
        result += arabicNumbers[numberStr[i]] ?? numberStr[i];
      }

      return result;
    }

    String dayName = arabicDays[now.weekday % 7]; // الأحد هو 0 في قائمتنا
    String monthName = arabicMonths[now.month - 1]; // الشهور تبدأ من 1
    String day = toArabicNumber(now.day);

    return '$dayName، $day $monthName ${toArabicNumber(now.year)}';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => ZikrDetailScreen(zikr: zikr)),
        );
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color:
              theme.brightness == Brightness.dark
                  ? Colors.grey.shade800.withAlpha(50)
                  : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color:
                theme.brightness == Brightness.dark
                    ? Colors.grey.shade600.withAlpha(30)
                    : Colors.grey.shade300.withAlpha(50),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(8),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'ذِكري اليومي',
                  style: TextStyle(
                    color:
                        theme.brightness == Brightness.dark
                            ? Colors.grey.shade300
                            : Colors.grey.shade700,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                Text(
                  _getFormattedDate(),
                  style: TextStyle(
                    color: theme.colorScheme.onSurface.withAlpha(
                      153,
                    ), // استخدام withAlpha بدلاً من withOpacity
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              zikr.text,
              style: const TextStyle(fontSize: 16, height: 1.6),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
              softWrap: true,
            ),
            const SizedBox(height: 16),
            Text(
              'رواه ${zikr.source}',
              style: TextStyle(
                color: theme.colorScheme.onSurface.withAlpha(
                  153,
                ), // استخدام withAlpha بدلاً من withOpacity
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  icon: const Icon(Icons.copy_outlined, size: 20),
                  onPressed: () => _copyZikr(context),
                  tooltip: 'نسخ',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
                const SizedBox(width: 16),
                IconButton(
                  icon: const Icon(Icons.share_outlined, size: 20),
                  onPressed: _shareZikr,
                  tooltip: 'مشاركة',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
