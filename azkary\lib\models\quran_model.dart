/// نموذج بيانات للسورة القرآنية
class Surah {
  final int number;
  final String name;
  final String englishName;
  final String englishNameTranslation;
  final int numberOfAyahs;
  final String revelationType;

  Surah({
    required this.number,
    required this.name,
    required this.englishName,
    required this.englishNameTranslation,
    required this.numberOfAyahs,
    required this.revelationType,
  });

  /// إنشاء نموذج من بيانات JSON
  factory Surah.fromJson(Map<String, dynamic> json) {
    return Surah(
      number: json['number'],
      name: json['name'],
      englishName: json['englishName'],
      englishNameTranslation: json['englishNameTranslation'],
      numberOfAyahs: json['numberOfAyahs'],
      revelationType: json['revelationType'],
    );
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name': name,
      'englishName': englishName,
      'englishNameTranslation': englishNameTranslation,
      'numberOfAyahs': numberOfAyahs,
      'revelationType': revelationType,
    };
  }
}

/// نموذج بيانات للآية القرآنية
class Ayah {
  final int number;
  final String text;
  final int numberInSurah;
  final int juz;
  final int page;
  final int hizbQuarter;
  final bool sajda;
  final String? tafsir; // تفسير الآية
  final bool isBismillah; // هل هي البسملة

  Ayah({
    required this.number,
    required this.text,
    required this.numberInSurah,
    required this.juz,
    required this.page,
    required this.hizbQuarter,
    required this.sajda,
    this.tafsir,
    this.isBismillah = false,
  });

  /// إنشاء نموذج من بيانات JSON
  factory Ayah.fromJson(Map<String, dynamic> json) {
    return Ayah(
      number: json['number'],
      text: json['text'],
      numberInSurah: json['numberInSurah'],
      juz: json['juz'],
      page: json['page'],
      hizbQuarter: json['hizbQuarter'],
      sajda: json['sajda'] is bool ? json['sajda'] : false,
      tafsir: json['tafsir'],
      isBismillah: json['isBismillah'] is bool ? json['isBismillah'] : false,
    );
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'text': text,
      'numberInSurah': numberInSurah,
      'juz': juz,
      'page': page,
      'hizbQuarter': hizbQuarter,
      'sajda': sajda,
      'tafsir': tafsir,
      'isBismillah': isBismillah,
    };
  }

  /// إنشاء نسخة جديدة من الآية مع تفسير
  Ayah copyWithTafsir(String tafsir) {
    return Ayah(
      number: number,
      text: text,
      numberInSurah: numberInSurah,
      juz: juz,
      page: page,
      hizbQuarter: hizbQuarter,
      sajda: sajda,
      isBismillah: isBismillah,
      tafsir: tafsir,
    );
  }
}

/// نموذج بيانات للقرآن الكريم كاملاً
class QuranData {
  final List<Surah> surahs;

  QuranData({required this.surahs});

  /// إنشاء نموذج من بيانات JSON
  factory QuranData.fromJson(Map<String, dynamic> json) {
    final List<dynamic> surahsJson = json['data']['surahs'];
    final List<Surah> surahs =
        surahsJson.map((surahJson) => Surah.fromJson(surahJson)).toList();
    return QuranData(surahs: surahs);
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'data': {'surahs': surahs.map((surah) => surah.toJson()).toList()},
    };
  }
}

/// نموذج بيانات لنتيجة البحث في الآيات
class AyahSearchResult {
  final Surah surah;
  final Ayah ayah;
  final String searchQuery;
  final List<int> matchPositions; // مواقع الكلمات المطابقة في النص
  final String highlightedText; // النص مع تمييز الكلمات المطابقة

  AyahSearchResult({
    required this.surah,
    required this.ayah,
    required this.searchQuery,
    this.matchPositions = const [],
    String? highlightedText,
  }) : highlightedText = highlightedText ?? ayah.text;

  /// إنشاء نتيجة بحث مع تحديد مواقع الكلمات المطابقة
  factory AyahSearchResult.withHighlighting(
    Surah surah,
    Ayah ayah,
    String searchQuery,
  ) {
    // تنظيف النص من الفراغات الزائدة
    final cleanQuery = searchQuery.trim();

    // تطبيع النص للبحث
    final normalizedQuery = _normalizeArabicText(cleanQuery.toLowerCase());
    final normalizedText = _normalizeArabicText(ayah.text.toLowerCase());

    // البحث عن جميع مواقع الكلمات المطابقة
    final List<int> positions = [];

    // البحث عن الكلمات المطابقة في النص الأصلي (غير المطبع)
    // هذا يساعد في تحديد المواقع الدقيقة للكلمات في النص الأصلي
    final String originalText = ayah.text;
    final List<String> words = originalText.split(' ');

    // البحث عن الكلمات المطابقة
    int currentPosition = 0;
    for (int i = 0; i < words.length; i++) {
      final String word = words[i];
      final String normalizedWord = _normalizeArabicText(word.toLowerCase());

      // إذا كانت الكلمة تحتوي على النص المطلوب
      if (normalizedWord.contains(normalizedQuery)) {
        positions.add(currentPosition);
      }

      // تحديث الموقع الحالي
      currentPosition += word.length + 1; // +1 للمسافة
    }

    // إذا لم يتم العثور على مواقع باستخدام الطريقة السابقة، نستخدم الطريقة التقليدية
    if (positions.isEmpty) {
      int startIndex = 0;
      while (true) {
        final index = normalizedText.indexOf(normalizedQuery, startIndex);
        if (index == -1) break;
        positions.add(index);
        startIndex = index + normalizedQuery.length;
      }
    }

    // إنشاء النص المميز
    String highlighted = ayah.text;
    if (positions.isNotEmpty) {
      // تمييز الكلمات المطابقة بوضع علامات حولها
      final normalizedOriginal = _normalizeArabicText(ayah.text.toLowerCase());
      final queryIndex = normalizedOriginal.indexOf(normalizedQuery);

      if (queryIndex != -1) {
        // العثور على الموقع الدقيق في النص الأصلي
        final beforeMatch = ayah.text.substring(0, queryIndex);
        final match = ayah.text.substring(
          queryIndex,
          queryIndex + normalizedQuery.length,
        );
        final afterMatch = ayah.text.substring(
          queryIndex + normalizedQuery.length,
        );

        highlighted = '$beforeMatch**$match**$afterMatch';
      }
    }

    return AyahSearchResult(
      surah: surah,
      ayah: ayah,
      searchQuery: cleanQuery,
      matchPositions: positions,
      highlightedText: highlighted,
    );
  }

  /// تطبيع النص العربي (إزالة التشكيل والهمزات)
  static String _normalizeArabicText(String text) {
    // إزالة التشكيل
    final withoutTashkeel = text
        .replaceAll('\u064B', '') // فتحتين
        .replaceAll('\u064C', '') // ضمتين
        .replaceAll('\u064D', '') // كسرتين
        .replaceAll('\u064E', '') // فتحة
        .replaceAll('\u064F', '') // ضمة
        .replaceAll('\u0650', '') // كسرة
        .replaceAll('\u0651', '') // شدة
        .replaceAll('\u0652', '') // سكون
        .replaceAll('\u0653', '') // مدة
        .replaceAll('\u0654', '') // همزة فوق الحرف
        .replaceAll('\u0655', ''); // همزة تحت الحرف

    // توحيد أشكال الهمزات والألف
    return withoutTashkeel
        .replaceAll('أ', 'ا')
        .replaceAll('إ', 'ا')
        .replaceAll('آ', 'ا')
        .replaceAll('ى', 'ي')
        .replaceAll('ة', 'ه');
  }
}
