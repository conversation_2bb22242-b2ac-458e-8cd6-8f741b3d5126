import 'dart:convert';

/// نموذج بيانات التسبيحة
class Tasbih {
  final int id;
  final String text;
  final int count;
  final int targetCount;
  final bool isFavorite;
  final DateTime lastUsed;
  final int totalCount; // إجمالي عدد مرات التسبيح منذ البداية
  final int todayCount; // عدد مرات التسبيح اليوم
  final bool isCompleted; // حالة اكتمال التسبيحة

  Tasbih({
    required this.id,
    required this.text,
    this.count = 0,
    required this.targetCount,
    this.isFavorite = false,
    DateTime? lastUsed,
    this.totalCount = 0,
    this.todayCount = 0,
    this.isCompleted = false,
  }) : lastUsed = lastUsed ?? DateTime.now();

  /// إنشاء نسخة جديدة من التسبيحة مع تعديل بعض الخصائص
  Tasbih copyWith({
    int? id,
    String? text,
    int? count,
    int? targetCount,
    bool? isFavorite,
    DateTime? lastUsed,
    int? totalCount,
    int? todayCount,
    bool? isCompleted,
  }) {
    return Tasbih(
      id: id ?? this.id,
      text: text ?? this.text,
      count: count ?? this.count,
      targetCount: targetCount ?? this.targetCount,
      isFavorite: isFavorite ?? this.isFavorite,
      lastUsed: lastUsed ?? this.lastUsed,
      totalCount: totalCount ?? this.totalCount,
      todayCount: todayCount ?? this.todayCount,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  /// تحويل التسبيحة إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'text': text,
      'count': count,
      'targetCount': targetCount,
      'isFavorite': isFavorite ? 1 : 0,
      'lastUsed': lastUsed.millisecondsSinceEpoch,
      'totalCount': totalCount,
      'todayCount': todayCount,
      'isCompleted': isCompleted ? 1 : 0,
    };
  }

  /// إنشاء تسبيحة من Map
  factory Tasbih.fromMap(Map<String, dynamic> map) {
    return Tasbih(
      id: map['id'] ?? 0,
      text: map['text'] ?? '',
      count: map['count'] ?? 0,
      targetCount: map['targetCount'] ?? 33,
      isFavorite: map['isFavorite'] == 1,
      lastUsed: DateTime.fromMillisecondsSinceEpoch(
        map['lastUsed'] ?? DateTime.now().millisecondsSinceEpoch,
      ),
      totalCount: map['totalCount'] ?? 0,
      todayCount: map['todayCount'] ?? 0,
      isCompleted: map['isCompleted'] == 1,
    );
  }

  /// تحويل التسبيحة إلى JSON
  String toJson() => json.encode(toMap());

  /// إنشاء تسبيحة من JSON
  factory Tasbih.fromJson(String source) => Tasbih.fromMap(json.decode(source));

  /// زيادة عداد التسبيح
  Tasbih incrementCount() {
    final newCount = count + 1;
    final completed = newCount >= targetCount;

    return copyWith(
      count: newCount,
      totalCount: totalCount + 1,
      todayCount: todayCount + 1,
      lastUsed: DateTime.now(),
      isCompleted: completed,
    );
  }

  /// إعادة ضبط العداد
  Tasbih resetCount() {
    return copyWith(count: 0, lastUsed: DateTime.now(), isCompleted: false);
  }

  /// تبديل حالة المفضلة
  Tasbih toggleFavorite() {
    return copyWith(isFavorite: !isFavorite);
  }

  /// التسبيحات الافتراضية
  static List<Tasbih> getDefaultTasbihs() {
    return [
      Tasbih(id: 1, text: 'سبحان الله', targetCount: 33),
      Tasbih(id: 2, text: 'الحمد لله', targetCount: 33),
      Tasbih(id: 3, text: 'الله أكبر', targetCount: 33),
      Tasbih(id: 4, text: 'لا إله إلا الله', targetCount: 33),
      Tasbih(id: 5, text: 'استغفر الله', targetCount: 100),
      Tasbih(
        id: 6,
        text: 'سبحان الله وبحمده سبحان الله العظيم',
        targetCount: 100,
      ),
      Tasbih(id: 7, text: 'لا حول ولا قوة إلا بالله', targetCount: 100),
      Tasbih(id: 8, text: 'اللهم صل على محمد وعلى آل محمد', targetCount: 10),
    ];
  }
}
