import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';

/// فئة تحسين الوسائط والأصوات للأداء
class MediaOptimizer {
  static const double _optimizedImageQuality = 0.8;
  static const int _optimizedImageWidth = 1080; // دقة 1080p كما طلب المستخدم
  static const int _optimizedImageHeight = 1920;
  static const double _optimizedAudioVolume = 0.7;
  
  /// إعدادات محسنة للصور
  static const Map<String, dynamic> optimizedImageSettings = {
    'quality': _optimizedImageQuality,
    'maxWidth': _optimizedImageWidth,
    'maxHeight': _optimizedImageHeight,
    'filterQuality': FilterQuality.medium,
  };

  /// إعدادات محسنة للأصوات
  static const Map<String, dynamic> optimizedAudioSettings = {
    'volume': _optimizedAudioVolume,
    'respectSilence': true,
    'stayAwake': false,
  };

  /// تحسين عرض الصور
  static Widget optimizedImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    bool isAsset = true,
  }) {
    if (isAsset) {
      return Image.asset(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        cacheWidth: width != null ? (width * _optimizedImageQuality).round() : null,
        cacheHeight: height != null ? (height * _optimizedImageQuality).round() : null,
        filterQuality: FilterQuality.medium,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: width,
            height: height,
            color: Colors.grey[300],
            child: const Icon(Icons.error, color: Colors.grey),
          );
        },
      );
    } else {
      return Image.network(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        cacheWidth: width != null ? (width * _optimizedImageQuality).round() : null,
        cacheHeight: height != null ? (height * _optimizedImageQuality).round() : null,
        filterQuality: FilterQuality.medium,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: width,
            height: height,
            alignment: Alignment.center,
            child: CircularProgressIndicator(
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded /
                      loadingProgress.expectedTotalBytes!
                  : null,
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: width,
            height: height,
            color: Colors.grey[300],
            child: const Icon(Icons.error, color: Colors.grey),
          );
        },
      );
    }
  }

  /// تحسين تشغيل الأصوات
  static Future<void> playOptimizedSound({
    required AudioPlayer player,
    required String soundPath,
    double? volume,
    bool respectSilence = true,
  }) async {
    try {
      await player.setVolume(volume ?? _optimizedAudioVolume);
      await player.setReleaseMode(ReleaseMode.release); // تحرير الموارد بعد التشغيل
      
      if (respectSilence) {
        // التحقق من إعدادات النظام للصوت
        await player.setPlayerMode(PlayerMode.lowLatency);
      }
      
      await player.play(AssetSource(soundPath));
    } catch (e) {
      // تجاهل أخطاء تشغيل الصوت لتجنب تعطيل التطبيق
      debugPrint('خطأ في تشغيل الصوت: $e');
    }
  }

  /// تحسين إعدادات الفيديو (إذا كان هناك فيديو)
  static Map<String, dynamic> get optimizedVideoSettings => {
    'quality': 'medium', // جودة متوسطة لتوازن الأداء
    'resolution': '1080p', // دقة 1080p كما طلب المستخدم
    'bitrate': 2000, // معدل بت محسن
    'fps': 30, // 30 إطار في الثانية
    'autoPlay': false, // عدم التشغيل التلقائي لتوفير البيانات
    'preload': false, // عدم التحميل المسبق
  };

  /// تحسين حجم الملفات
  static double getOptimizedFileSize(double originalSize) {
    // تقليل حجم الملفات بنسبة 20% تقريباً
    return originalSize * 0.8;
  }

  /// تحسين جودة الصور بناءً على حجم الشاشة
  static FilterQuality getOptimizedFilterQuality(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final pixelRatio = MediaQuery.of(context).devicePixelRatio;
    final totalPixels = size.width * size.height * pixelRatio;
    
    if (totalPixels > 2000000) { // شاشات عالية الدقة
      return FilterQuality.medium;
    } else if (totalPixels > 1000000) { // شاشات متوسطة الدقة
      return FilterQuality.low;
    } else { // شاشات منخفضة الدقة
      return FilterQuality.none;
    }
  }

  /// تحسين إعدادات التخزين المؤقت للصور
  static int getOptimizedCacheSize(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final pixelRatio = MediaQuery.of(context).devicePixelRatio;
    
    // حساب حجم التخزين المؤقت بناءً على حجم الشاشة
    return ((size.width * pixelRatio) * 0.8).round();
  }

  /// تحسين إعدادات الصوت بناءً على نوع الجهاز
  static Map<String, dynamic> getOptimizedAudioSettings() {
    return {
      'volume': _optimizedAudioVolume,
      'respectSilence': true,
      'stayAwake': false,
      'duckAudio': true, // تقليل مستوى الصوت عند تشغيل أصوات أخرى
      'playInBackground': false, // عدم التشغيل في الخلفية لتوفير البطارية
    };
  }

  /// تحسين إعدادات الرسوم المتحركة للوسائط
  static Duration getOptimizedAnimationDuration(String mediaType) {
    switch (mediaType) {
      case 'image':
        return const Duration(milliseconds: 200);
      case 'audio':
        return const Duration(milliseconds: 150);
      case 'video':
        return const Duration(milliseconds: 300);
      default:
        return const Duration(milliseconds: 200);
    }
  }

  /// تحسين معالجة الأخطاء للوسائط
  static Widget buildErrorWidget({
    double? width,
    double? height,
    IconData icon = Icons.error,
    String? message,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: Colors.grey[600], size: 32),
          if (message != null) ...[
            const SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  /// تحسين معالجة التحميل للوسائط
  static Widget buildLoadingWidget({
    double? width,
    double? height,
    String? message,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          if (message != null) ...[
            const SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  /// تحسين إعدادات ضغط الصور
  static Map<String, dynamic> getCompressionSettings(String imageType) {
    switch (imageType.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        return {
          'quality': 85, // جودة جيدة مع ضغط معقول
          'format': 'jpeg',
        };
      case 'png':
        return {
          'quality': 90, // جودة عالية للشفافية
          'format': 'png',
        };
      case 'webp':
        return {
          'quality': 80, // ضغط ممتاز مع جودة جيدة
          'format': 'webp',
        };
      default:
        return {
          'quality': 80,
          'format': 'jpeg',
        };
    }
  }

  /// تحسين إعدادات التخزين المؤقت
  static const Map<String, int> cacheSettings = {
    'maxCacheSize': 100 * 1024 * 1024, // 100 ميجابايت
    'maxCacheAge': 7 * 24 * 60 * 60, // 7 أيام بالثواني
    'maxCacheObjects': 1000, // حد أقصى 1000 كائن
  };

  /// تنظيف التخزين المؤقت
  static Future<void> clearCache() async {
    try {
      // تنظيف تخزين الصور المؤقت
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();
    } catch (e) {
      debugPrint('خطأ في تنظيف التخزين المؤقت: $e');
    }
  }

  /// تحسين استخدام الذاكرة للوسائط
  static void optimizeMemoryUsage() {
    // تحديد حد أقصى لحجم تخزين الصور المؤقت
    PaintingBinding.instance.imageCache.maximumSize = 100;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 * 1024 * 1024; // 50 ميجابايت
  }
}
