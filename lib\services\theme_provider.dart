import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/quran_view_mode.dart';

class ThemeProvider extends ChangeNotifier {
  // طريقة عرض القرآن الكريم
  QuranViewMode _quranViewMode = QuranViewMode.list; // القيمة الافتراضية

  // الحصول على طريقة عرض القرآن الكريم
  QuranViewMode get quranViewMode => _quranViewMode;

  // المُنشئ - يقوم بتحميل الإعدادات عند إنشاء المزود
  ThemeProvider() {
    _loadThemePreference();
  }

  // تحميل إعدادات السمة من التخزين المحلي
  Future<void> _loadThemePreference() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    // تحميل طريقة عرض القرآن الكريم
    String quranViewModeStr = prefs.getString('quran_view_mode') ?? 'list';
    _quranViewMode = _getQuranViewModeFromString(quranViewModeStr);
  }

  // تحويل النص إلى طريقة عرض القرآن الكريم
  QuranViewMode _getQuranViewModeFromString(String value) {
    switch (value) {
      case 'list':
        return QuranViewMode.list;
      case 'pages':
        return QuranViewMode.pages;
      case 'mushaf':
        return QuranViewMode.mushaf;
      default:
        return QuranViewMode.list;
    }
  }

  // تغيير طريقة عرض القرآن الكريم
  Future<void> setQuranViewMode(QuranViewMode mode) async {
    if (_quranViewMode != mode) {
      _quranViewMode = mode;
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'quran_view_mode',
        _quranViewMode.toString().split('.').last,
      );

      // إخطار المستمعين بالتغيير
      notifyListeners();

      // طباعة معلومات طريقة العرض للتأكد من تغييرها
      debugPrint(
        'Quran view mode changed to: ${_quranViewMode.toString().split('.').last}',
      );
    }
  }
}
