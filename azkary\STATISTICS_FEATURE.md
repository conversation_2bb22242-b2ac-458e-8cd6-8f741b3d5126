# ميزة نظام التتبع والإحصائيات

## نظرة عامة

تم إضافة ميزة شاملة لنظام التتبع والإحصائيات إلى تطبيق الأذكار الإسلامي. هذه الميزة تتيح للمستخدمين تتبع تقدمهم في قراءة الأذكار وتحفيزهم من خلال نظام النقاط والإنجازات.

## الميزات المضافة

### 1. الإحصائيات التفصيلية
- **إحصائيات يومية**: عرض تقدم اليوم الحالي
- **إحصائيات أسبوعية**: ملخص الأسبوع مع متوسط الإكمال
- **إحصائيات شهرية**: إجمالي الشهر والأيام المكتملة
- **السلسلة المتتالية**: تتبع أطول سلسلة أيام متتالية

### 2. الخريطة الحرارية
- عرض النشاط اليومي لآخر 90 يوماً
- ألوان متدرجة تعكس مستوى النشاط
- معلومات تفصيلية عند النقر على أي يوم
- ملخص إحصائي للفترة المعروضة

### 3. نظام الإنجازات والنقاط
- **إنجازات السلسلة المتتالية**: 3، 7، 30 يوماً
- **إنجازات إجمالية**: 100، 500، 1000 ذكر
- **إنجازات يومية**: إكمال جميع الأذكار في يوم واحد
- **إنجازات خاصة**: سيد أذكار الصباح/المساء
- مستويات مختلفة: برونزي، فضي، ذهبي، بلاتيني، ماسي

### 4. مخطط التقدم
- مخطط بياني أسبوعي يعرض التقدم اليومي
- ألوان مختلفة حسب نسبة الإكمال
- إحصائيات سريعة للأسبوع والشهر

## الملفات المضافة

### النماذج (Models)
- `lib/models/statistics_model.dart`: نماذج البيانات للإحصائيات
- `lib/models/achievement_model.dart`: نماذج البيانات للإنجازات

### الخدمات (Services)
- `lib/services/statistics_service.dart`: خدمة قاعدة البيانات للإحصائيات
- `lib/services/statistics_provider.dart`: مزود الإحصائيات باستخدام Provider

### الشاشات (Screens)
- `lib/screens/statistics_screen.dart`: الشاشة الرئيسية للإحصائيات

### الويدجتات (Widgets)
- `lib/widgets/statistics_card.dart`: بطاقة عرض الإحصائيات اليومية
- `lib/widgets/heat_map_widget.dart`: ويدجت الخريطة الحرارية
- `lib/widgets/achievement_widget.dart`: ويدجت عرض الإنجازات
- `lib/widgets/progress_chart.dart`: ويدجت مخطط التقدم

## التكامل مع التطبيق

### 1. إضافة المزود
تم إضافة `StatisticsProvider` إلى قائمة المزودين في `main.dart`:

```dart
ChangeNotifierProvider(create: (context) => StatisticsProvider()),
```

### 2. إضافة الأيقونة في شاشة "المزيد"
تم إضافة أيقونة "الإحصائيات والتتبع" في قسم الميزات الإسلامية في `more_screen.dart`.

### 3. قاعدة البيانات
تم إنشاء جداول جديدة:
- `daily_statistics`: لحفظ الإحصائيات اليومية
- `achievements`: لحفظ الإنجازات وحالتها
- `streak_data`: لحفظ بيانات السلسلة المتتالية

## كيفية الاستخدام

### للمطورين
1. استخدم `StatisticsProvider` لتسجيل إكمال الأذكار:
```dart
context.read<StatisticsProvider>().recordZikrCompletion(
  completedCount: 1,
  totalCount: 10,
  category: 'أذكار الصباح',
  timeSpent: Duration(minutes: 5),
);
```

2. اعرض الإحصائيات باستخدام `Consumer<StatisticsProvider>`:
```dart
Consumer<StatisticsProvider>(
  builder: (context, provider, child) {
    return Text('النقاط: ${provider.totalPoints}');
  },
)
```

### للمستخدمين
1. انتقل إلى شاشة "المزيد"
2. اضغط على "الإحصائيات والتتبع"
3. تصفح التبويبات الثلاثة:
   - الإحصائيات: عرض التقدم والسلسلة المتتالية
   - الخريطة الحرارية: عرض النشاط على مدار 90 يوماً
   - الإنجازات: عرض الإنجازات المحققة والقادمة

## التصميم والواجهة

### الألوان والثيمات
- دعم كامل للوضع الفاتح والمعتم والليلي
- استخدام ألوان التطبيق الحالية
- تصميم متسق مع باقي التطبيق

### الخط والنصوص
- استخدام خط Cairo كما هو محدد في إعدادات التطبيق
- نصوص باللغة العربية مع محاذاة RTL
- أحجام خطوط متناسقة ومقروءة

### الرسوم المتحركة
- انتقالات سلسة بين التبويبات
- تأثيرات بصرية للإنجازات المفتوحة
- تحميل تدريجي للبيانات

## الأداء والتحسين

### قاعدة البيانات
- استعلامات محسنة للبيانات الكبيرة
- فهرسة مناسبة للجداول
- تنظيف دوري للبيانات القديمة

### الذاكرة
- تحميل البيانات عند الحاجة فقط
- إدارة ذكية للذاكرة في الخريطة الحرارية
- تخزين مؤقت للبيانات المستخدمة بكثرة

## المتطلبات التقنية

### الحزم المطلوبة
- `provider`: لإدارة الحالة
- `sqflite`: لقاعدة البيانات المحلية
- `flutter/material.dart`: للواجهة

### إصدار Flutter
- متوافق مع Flutter 3.0+
- يدعم Android و iOS

## الاختبار

### اختبار الوحدة
- اختبار نماذج البيانات
- اختبار خدمات قاعدة البيانات
- اختبار منطق الإحصائيات

### اختبار التكامل
- اختبار تسجيل الإحصائيات
- اختبار عرض البيانات
- اختبار الانتقال بين الشاشات

## المشاكل المعروفة والحلول

### 1. تحذير `color.value` مهجور
- **المشكلة**: استخدام `color.value` في نموذج الإنجازات
- **الحل**: سيتم تحديثه في إصدار لاحق لاستخدام `color.value` أو طريقة أخرى

### 2. أداء الخريطة الحرارية
- **المشكلة**: قد تكون بطيئة مع البيانات الكثيرة
- **الحل**: تحسين الرسم وتحميل البيانات تدريجياً

## التطوير المستقبلي

### ميزات مخططة
- إضافة المزيد من أنواع الإنجازات
- تصدير الإحصائيات
- مقارنة التقدم مع فترات سابقة
- إشعارات للتذكير بالأذكار

### تحسينات مخططة
- تحسين أداء قاعدة البيانات
- إضافة رسوم متحركة أكثر
- دعم المزيد من أنواع المخططات

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع الكود في الملفات المذكورة أعلاه
2. تحقق من سجلات التطبيق باستخدام `AppLogger`
3. تأكد من تهيئة قاعدة البيانات بشكل صحيح

---

تم تطوير هذه الميزة بعناية لتوفير تجربة مستخدم ممتازة وتحفيز المستخدمين على المواظبة على الأذكار اليومية.
