/// تعداد لأوضاع السمة المتاحة في التطبيق
enum AppThemeMode {
  /// الوضع الفاتح - خلفية بيضاء ونصوص داكنة
  light,
  
  /// الوضع المعتم - خلفية رمادية داكنة ونصوص فاتحة
  dim,
  
  /// الوضع الليلي - خلفية سوداء ونصوص فاتحة
  dark;
  
  /// الحصول على اسم الوضع بالعربية
  String get arabicName {
    switch (this) {
      case AppThemeMode.light:
        return 'فاتح';
      case AppThemeMode.dim:
        return 'معتم';
      case AppThemeMode.dark:
        return 'ليلي';
    }
  }
  
  /// الحصول على وضع السمة من قيمة نصية
  static AppThemeMode fromString(String value) {
    switch (value) {
      case 'light':
        return AppThemeMode.light;
      case 'dim':
        return AppThemeMode.dim;
      case 'dark':
        return AppThemeMode.dark;
      default:
        return AppThemeMode.light;
    }
  }
}
