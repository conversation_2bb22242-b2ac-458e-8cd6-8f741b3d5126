import 'package:flutter/material.dart';

/// نموذج لتخزين ألوان السمة المخصصة
class ThemeModel {
  final Color primaryColor;
  final String name;
  final int colorValue;

  const ThemeModel({
    required this.primaryColor,
    required this.name,
    required this.colorValue,
  });

  // إنشاء نسخة من النموذج من قيمة لون
  factory ThemeModel.fromColor(Color color, String name) {
    return ThemeModel(
      primaryColor: color,
      name: name,
      colorValue: color.toARGB32(),
    );
  }

  // إنشاء نسخة من النموذج من قيمة عددية للون
  factory ThemeModel.fromValue(int value, String name) {
    return ThemeModel(
      primaryColor: Color(value),
      name: name,
      colorValue: value,
    );
  }

  // تحويل النموذج إلى خريطة لتخزينه
  Map<String, dynamic> toMap() {
    return {'primaryColor': colorValue, 'name': name};
  }

  // إنشاء نسخة من النموذج من خريطة
  factory ThemeModel.fromMap(Map<String, dynamic> map) {
    return ThemeModel(
      primaryColor: Color(map['primaryColor']),
      name: map['name'],
      colorValue: map['primaryColor'],
    );
  }

  // قائمة الألوان المتاحة - تم تحسين الألوان لتكون أكثر وضوحاً وتناسقاً
  static List<ThemeModel> get availableThemes => [
    ThemeModel.fromColor(
      const Color(0xFF1F8A70),
      'أخضر',
    ), // اللون الافتراضي - أخضر زمردي إسلامي
    ThemeModel.fromColor(const Color(0xFF2196F3), 'أزرق'), // أزرق فاتح
    ThemeModel.fromColor(
      const Color(0xFF673AB7),
      'بنفسجي',
    ), // بنفسجي أكثر وضوحاً
    ThemeModel.fromColor(const Color(0xFFE91E63), 'وردي'), // وردي
    ThemeModel.fromColor(
      const Color(0xFFFF5722),
      'برتقالي',
    ), // برتقالي أكثر وضوحاً
    ThemeModel.fromColor(const Color(0xFFFF0000), 'أحمر'), // أحمر نقي واضح
    ThemeModel.fromColor(const Color(0xFFFFFF00), 'أصفر'), // أصفر نقي واضح
    ThemeModel.fromColor(const Color(0xFF8B4513), 'بني'), // بني واضح
  ];
}
