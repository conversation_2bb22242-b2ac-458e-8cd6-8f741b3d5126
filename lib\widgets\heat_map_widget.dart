import 'package:flutter/material.dart';
import '../models/statistics_model.dart';

/// ويدجت الخريطة الحرارية لعرض النشاط اليومي
class HeatMapWidget extends StatelessWidget {
  final List<DailyStatistics> data;
  final Color primaryColor;

  const HeatMapWidget({
    super.key,
    required this.data,
    required this.primaryColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // أيام الأسبوع
            _buildWeekdayLabels(theme),
            const SizedBox(height: 8),
            
            // الشبكة الحرارية
            _buildHeatMapGrid(theme),
            
            const SizedBox(height: 16),
            
            // معلومات إضافية
            _buildSummaryInfo(theme),
          ],
        ),
      ),
    );
  }

  /// بناء تسميات أيام الأسبوع
  Widget _buildWeekdayLabels(ThemeData theme) {
    const weekdays = ['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'];
    
    return Row(
      children: [
        const SizedBox(width: 40), // مساحة للشهور
        ...weekdays.map((day) => Expanded(
          child: Text(
            day,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface.withAlpha(179),
            ),
          ),
        )),
      ],
    );
  }

  /// بناء شبكة الخريطة الحرارية
  Widget _buildHeatMapGrid(ThemeData theme) {
    if (data.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات للعرض'),
      );
    }

    // تجميع البيانات حسب الأسابيع
    final weeks = _groupDataByWeeks();
    
    return Column(
      children: weeks.asMap().entries.map((entry) {
        final weekIndex = entry.key;
        final weekData = entry.value;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            children: [
              // تسمية الشهر (فقط في بداية كل شهر)
              SizedBox(
                width: 40,
                child: _shouldShowMonthLabel(weekIndex, weekData)
                    ? Text(
                        _getMonthLabel(weekData.first.date),
                        style: TextStyle(
                          fontSize: 10,
                          color: theme.colorScheme.onSurface.withAlpha(179),
                        ),
                      )
                    : null,
              ),
              
              // مربعات الأيام
              ...weekData.map((dayStats) => Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(1),
                  child: _buildDaySquare(dayStats, theme),
                ),
              )),
              
              // ملء المساحة المتبقية إذا كان الأسبوع غير مكتمل
              ...List.generate(
                7 - weekData.length,
                (index) => const Expanded(child: SizedBox()),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// بناء مربع اليوم الواحد
  Widget _buildDaySquare(DailyStatistics dayStats, ThemeData theme) {
    final intensity = _getIntensity(dayStats);
    final color = _getColorForIntensity(intensity);
    
    return Tooltip(
      message: _getTooltipMessage(dayStats),
      child: Container(
        width: 12,
        height: 12,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(2),
          border: Border.all(
            color: theme.colorScheme.outline.withAlpha(51),
            width: 0.5,
          ),
        ),
      ),
    );
  }

  /// بناء معلومات الملخص
  Widget _buildSummaryInfo(ThemeData theme) {
    final totalDays = data.length;
    final activeDays = data.where((d) => d.completedAzkar > 0).length;
    final completedDays = data.where((d) => d.isFullyCompleted).length;
    final totalAzkar = data.fold(0, (sum, d) => sum + d.completedAzkar);
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildSummaryItem(
          'أيام نشطة',
          '$activeDays/$totalDays',
          Icons.calendar_today,
          theme,
        ),
        _buildSummaryItem(
          'أيام مكتملة',
          '$completedDays',
          Icons.check_circle,
          theme,
        ),
        _buildSummaryItem(
          'إجمالي الأذكار',
          '$totalAzkar',
          Icons.format_list_numbered,
          theme,
        ),
      ],
    );
  }

  /// بناء عنصر معلومات الملخص
  Widget _buildSummaryItem(
    String title,
    String value,
    IconData icon,
    ThemeData theme,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          color: primaryColor,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        Text(
          title,
          style: TextStyle(
            fontSize: 10,
            color: theme.colorScheme.onSurface.withAlpha(179),
          ),
        ),
      ],
    );
  }

  /// تجميع البيانات حسب الأسابيع
  List<List<DailyStatistics>> _groupDataByWeeks() {
    final weeks = <List<DailyStatistics>>[];
    
    if (data.isEmpty) return weeks;
    
    // البدء من أول يوم أحد
    final firstDate = data.first.date;
    final firstSunday = firstDate.subtract(Duration(days: firstDate.weekday % 7));
    
    var currentWeek = <DailyStatistics>[];
    var currentDate = firstSunday;
    
    for (final dayStats in data) {
      // إذا كان اليوم في أسبوع جديد
      if (currentDate.weekday == DateTime.sunday && currentWeek.isNotEmpty) {
        weeks.add(List.from(currentWeek));
        currentWeek.clear();
      }
      
      currentWeek.add(dayStats);
      currentDate = currentDate.add(const Duration(days: 1));
    }
    
    // إضافة الأسبوع الأخير
    if (currentWeek.isNotEmpty) {
      weeks.add(currentWeek);
    }
    
    return weeks;
  }

  /// الحصول على شدة النشاط (0.0 إلى 1.0)
  double _getIntensity(DailyStatistics dayStats) {
    if (dayStats.totalAzkar == 0) return 0.0;
    return dayStats.completionPercentage;
  }

  /// الحصول على اللون حسب الشدة
  Color _getColorForIntensity(double intensity) {
    if (intensity == 0.0) {
      return Colors.grey[300]!;
    } else if (intensity <= 0.25) {
      return primaryColor.withAlpha(64);
    } else if (intensity <= 0.5) {
      return primaryColor.withAlpha(128);
    } else if (intensity <= 0.75) {
      return primaryColor.withAlpha(192);
    } else {
      return primaryColor;
    }
  }

  /// الحصول على رسالة التلميح
  String _getTooltipMessage(DailyStatistics dayStats) {
    final dateStr = '${dayStats.date.day}/${dayStats.date.month}/${dayStats.date.year}';
    final completionStr = '${dayStats.completedAzkar}/${dayStats.totalAzkar} أذكار';
    final pointsStr = '${dayStats.points} نقطة';
    
    return '$dateStr\n$completionStr\n$pointsStr';
  }

  /// تحديد ما إذا كان يجب عرض تسمية الشهر
  bool _shouldShowMonthLabel(int weekIndex, List<DailyStatistics> weekData) {
    if (weekIndex == 0) return true;
    if (weekData.isEmpty) return false;
    
    // عرض تسمية الشهر في بداية كل شهر جديد
    final firstDayOfWeek = weekData.first.date;
    return firstDayOfWeek.day <= 7;
  }

  /// الحصول على تسمية الشهر
  String _getMonthLabel(DateTime date) {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    return months[date.month - 1];
  }
}
