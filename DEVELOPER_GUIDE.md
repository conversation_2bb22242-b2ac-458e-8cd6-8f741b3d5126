# دليل المطور - تحسينات الأداء

## 🚀 كيفية استخدام التحسينات الجديدة

### 1. استخدام الانتقالات المحسنة

```dart
import 'package:azkary/utils/page_transitions.dart';

// انتقال سريع ومحسن
Navigator.push(
  context,
  PageTransitions.slideFromRight(NewPage()),
);

// انتقال إسلامي محسن
Navigator.push(
  context,
  PageTransitions.islamicTransition(NewPage()),
);
```

### 2. استخدام إعدادات الأداء

```dart
import 'package:azkary/utils/app_performance_config.dart';

// قائمة محسنة
AppPerformanceConfig.optimizedListView(
  itemCount: items.length,
  itemBuilder: (context, index) => ItemWidget(items[index]),
);

// صورة محسنة
AppPerformanceConfig.optimizedImage(
  imagePath: 'assets/images/image.png',
  width: 200,
  height: 200,
);

// بطاقة محسنة
AppPerformanceConfig.optimizedCard(
  context: context,
  child: YourContent(),
);
```

### 3. استخدام تحسينات الصفحة الرئيسية

```dart
import 'package:azkary/utils/home_screen_optimizer.dart';

// بطاقة السؤال اليومي محسنة
HomeScreenOptimizer.optimizedDailyQuestionCard(
  context: context,
  onTap: () => navigateToQuestions(),
  child: QuestionContent(),
);

// بطاقة تصنيف محسنة
HomeScreenOptimizer.optimizedCategoryCard(
  context: context,
  index: index,
  onTap: () => navigateToCategory(),
  child: CategoryContent(),
);
```

### 4. استخدام تحسينات الوسائط

```dart
import 'package:azkary/utils/media_optimizer.dart';

// صورة محسنة مع معالجة الأخطاء
MediaOptimizer.optimizedImage(
  imagePath: 'path/to/image.jpg',
  width: 300,
  height: 200,
  isAsset: true,
);

// تشغيل صوت محسن
await MediaOptimizer.playOptimizedSound(
  player: audioPlayer,
  soundPath: 'sounds/notification.mp3',
  volume: 0.7,
);
```

### 5. استخدام تحسينات قاعدة البيانات

```dart
import 'package:azkary/utils/database_optimizer.dart';

// استعلام محسن
final query = DatabaseOptimizer.optimizeQuery(
  table: 'azkar',
  columns: ['id', 'text', 'category_id'],
  where: 'category_id = ?',
  whereArgs: [categoryId],
  limit: 50,
);

// تخزين مؤقت
DatabaseOptimizer.cacheData('key', data);
final cachedData = DatabaseOptimizer.getCachedData<List<Zikr>>('key');
```

## 🎨 إرشادات التصميم المحسن

### الألوان والشفافية:
```dart
// استخدم withAlpha بدلاً من withOpacity
Color.red.withAlpha(128)  // بدلاً من Color.red.withOpacity(0.5)

// أو استخدم الدالة المحسنة
AppPerformanceConfig.optimizedColor(Colors.blue, 0.3)
```

### الظلال:
```dart
// ظل واحد مبسط
boxShadow: AppPerformanceConfig.optimizedShadow(context: context)

// بدلاً من ظلال متعددة معقدة
```

### التدرجات:
```dart
// تدرج مبسط
decoration: BoxDecoration(
  gradient: AppPerformanceConfig.optimizedGradient(
    primaryColor: theme.primaryColor,
    isDark: theme.brightness == Brightness.dark,
  ),
)
```

## ⚡ نصائح الأداء

### 1. الرسوم المتحركة:
- استخدم مدد قصيرة (150-250ms)
- استخدم منحنيات بسيطة (`Curves.easeOut`)
- تجنب الرسوم المتحركة المعقدة

### 2. القوائم:
- استخدم `ClampingScrollPhysics`
- قلل `cacheExtent` إلى 250-300
- استخدم `addAutomaticKeepAlives: false`

### 3. الصور:
- حدد أبعاد الصور دائماً
- استخدم `FilterQuality.medium`
- استخدم `cacheWidth` و `cacheHeight`

### 4. الذاكرة:
- تجنب تخزين البيانات الكبيرة في الذاكرة
- استخدم التخزين المؤقت بحكمة
- نظف الموارد غير المستخدمة

## 🔧 إعدادات التطوير

### تفعيل مراقبة الأداء:
```dart
MaterialApp(
  showPerformanceOverlay: true, // للتطوير فقط
  // ...
)
```

### تفعيل التحليل:
```bash
flutter run --profile
flutter run --trace-startup
```

### مراقبة الذاكرة:
```bash
flutter run --enable-software-rendering
```

## 📊 مؤشرات الأداء المطلوبة

### الحد الأدنى:
- **FPS**: 30 إطار في الثانية
- **زمن الاستجابة**: أقل من 100ms
- **استهلاك الذاكرة**: أقل من 150MB

### الهدف المثالي:
- **FPS**: 60 إطار في الثانية
- **زمن الاستجابة**: أقل من 50ms
- **استهلاك الذاكرة**: أقل من 100MB

## 🚫 ما يجب تجنبه

### الرسوم المتحركة:
```dart
// ❌ تجنب
Duration(milliseconds: 1000)
Curves.elasticOut
Transform.rotate() مع قيم كبيرة

// ✅ استخدم
Duration(milliseconds: 200)
Curves.easeOut
Transform.scale() مع قيم صغيرة
```

### القوائم:
```dart
// ❌ تجنب
BouncingScrollPhysics()
cacheExtent: 1000
addAutomaticKeepAlives: true

// ✅ استخدم
ClampingScrollPhysics()
cacheExtent: 250
addAutomaticKeepAlives: false
```

### الصور:
```dart
// ❌ تجنب
Image.asset('image.png') // بدون أبعاد
FilterQuality.high // للصور العادية

// ✅ استخدم
Image.asset('image.png', width: 200, height: 200)
FilterQuality.medium
```

## 🔍 تشخيص المشاكل

### بطء الانتقالات:
1. تحقق من مدة الانتقال
2. تحقق من نوع المنحنى
3. تحقق من تعقيد التأثير

### استهلاك ذاكرة عالي:
1. تحقق من حجم الصور
2. تحقق من التخزين المؤقت
3. تحقق من تسريبات الذاكرة

### تقطيع في التمرير:
1. تحقق من `cacheExtent`
2. تحقق من `physics`
3. تحقق من تعقيد العناصر

## 📚 مراجع مفيدة

### الوثائق:
- [Flutter Performance Best Practices](https://flutter.dev/docs/perf/best-practices)
- [Flutter Performance Profiling](https://flutter.dev/docs/perf/rendering)

### الأدوات:
- Flutter Inspector
- Performance Overlay
- Memory Profiler
- Timeline Tracing

## 🔄 التحديثات المستقبلية

### المخطط:
1. **الإصدار 1.1**: تحسينات قاعدة البيانات
2. **الإصدار 1.2**: تحسينات الشبكة
3. **الإصدار 1.3**: تحسينات الخطوط

### المراقبة:
- مراجعة أسبوعية للأداء
- تحديث شهري للإعدادات
- تحسين مستمر بناءً على البيانات

---

**نصيحة**: ابدأ دائماً بقياس الأداء قبل وبعد التحسين لضمان فعالية التغييرات.
