import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/statistics_provider.dart';
import '../widgets/statistics_card.dart';
import '../widgets/heat_map_widget.dart';
import '../widgets/achievement_widget.dart';
import '../widgets/progress_chart.dart';

/// شاشة الإحصائيات والتتبع
class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // تهيئة مزود الإحصائيات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<StatisticsProvider>().initialize();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'الإحصائيات والتتبع',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        centerTitle: true,
        backgroundColor: theme.colorScheme.surface,
        elevation: 2,
        leading: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: theme.colorScheme.primary.withAlpha(20),
          ),
          child: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: theme.colorScheme.primary,
              size: 22,
            ),
            onPressed: () => Navigator.of(context).pop(),
            tooltip: 'رجوع',
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الإحصائيات'),
            Tab(text: 'الخريطة الحرارية'),
            Tab(text: 'الإنجازات'),
          ],
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurface.withAlpha(153),
          indicatorColor: theme.colorScheme.primary,
        ),
      ),
      body: Consumer<StatisticsProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildStatisticsTab(provider, theme),
              _buildHeatMapTab(provider, theme),
              _buildAchievementsTab(provider, theme),
            ],
          );
        },
      ),
    );
  }

  /// بناء تبويب الإحصائيات
  Widget _buildStatisticsTab(StatisticsProvider provider, ThemeData theme) {
    return RefreshIndicator(
      onRefresh: provider.refresh,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // إحصائيات اليوم
            Text(
              'إحصائيات اليوم',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            if (provider.todayStats != null)
              StatisticsCard(
                title: 'اليوم',
                stats: provider.todayStats!,
                icon: Icons.today,
                color: theme.colorScheme.primary,
              ),

            const SizedBox(height: 24),

            // السلسلة المتتالية
            Text(
              'السلسلة المتتالية',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            _buildStreakCard(provider.streakData, theme),

            const SizedBox(height: 24),

            // الإحصائيات الأسبوعية
            Text(
              'إحصائيات الأسبوع',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            if (provider.weeklyStats != null)
              _buildWeeklyStatsCard(provider.weeklyStats!, theme),

            const SizedBox(height: 24),

            // الإحصائيات الشهرية
            Text(
              'إحصائيات الشهر',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            if (provider.monthlyStats != null)
              _buildMonthlyStatsCard(provider.monthlyStats!, theme),

            const SizedBox(height: 24),

            // مخطط التقدم
            Text(
              'مخطط التقدم',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            ProgressChart(
              weeklyStats: provider.weeklyStats,
              monthlyStats: provider.monthlyStats,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء تبويب الخريطة الحرارية
  Widget _buildHeatMapTab(StatisticsProvider provider, ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الخريطة الحرارية - آخر 90 يوماً',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 16),

          HeatMapWidget(
            data: provider.heatMapData,
            primaryColor: theme.colorScheme.primary,
          ),

          const SizedBox(height: 24),

          // شرح الألوان
          _buildHeatMapLegend(theme),
        ],
      ),
    );
  }

  /// بناء تبويب الإنجازات
  Widget _buildAchievementsTab(StatisticsProvider provider, ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إجمالي النقاط
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    Icons.stars,
                    color: theme.colorScheme.primary,
                    size: 32,
                  ),
                  const SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إجمالي النقاط',
                        style: TextStyle(
                          fontSize: 16,
                          color: theme.colorScheme.onSurface.withAlpha(179),
                        ),
                      ),
                      Text(
                        '${provider.totalPoints}',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // الإنجازات المفتوحة
          if (provider.unlockedAchievements.isNotEmpty) ...[
            Text(
              'الإنجازات المحققة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            ...provider.unlockedAchievements.map(
              (achievement) => AchievementWidget(
                achievement: achievement,
                isUnlocked: true,
              ),
            ),
            const SizedBox(height: 24),
          ],

          // الإنجازات المقفلة
          if (provider.lockedAchievements.isNotEmpty) ...[
            Text(
              'الإنجازات القادمة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            ...provider.lockedAchievements.map(
              (achievement) => AchievementWidget(
                achievement: achievement,
                isUnlocked: false,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء بطاقة السلسلة المتتالية
  Widget _buildStreakCard(streakData, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.local_fire_department,
              color: Colors.orange,
              size: 32,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'السلسلة الحالية',
                    style: TextStyle(
                      fontSize: 16,
                      color: theme.colorScheme.onSurface.withAlpha(179),
                    ),
                  ),
                  Text(
                    '${streakData.currentStreak} يوم',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'أطول سلسلة',
                  style: TextStyle(
                    fontSize: 14,
                    color: theme.colorScheme.onSurface.withAlpha(179),
                  ),
                ),
                Text(
                  '${streakData.longestStreak} يوم',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.secondary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الإحصائيات الأسبوعية
  Widget _buildWeeklyStatsCard(weeklyStats, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  'الأيام المكتملة',
                  '${weeklyStats.completedDays}/7',
                  Icons.check_circle,
                  theme.colorScheme.primary,
                ),
                _buildStatItem(
                  'متوسط الإكمال',
                  '${(weeklyStats.averageCompletion * 100).toInt()}%',
                  Icons.trending_up,
                  theme.colorScheme.secondary,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الإحصائيات الشهرية
  Widget _buildMonthlyStatsCard(monthlyStats, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  'الأيام المكتملة',
                  '${monthlyStats.completedDays}',
                  Icons.calendar_today,
                  theme.colorScheme.primary,
                ),
                _buildStatItem(
                  'إجمالي الأذكار',
                  '${monthlyStats.totalCompletedAzkar}',
                  Icons.format_list_numbered,
                  theme.colorScheme.secondary,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(
      String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurface.withAlpha(179),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// بناء شرح الخريطة الحرارية
  Widget _buildHeatMapLegend(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'شرح الألوان',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildLegendItem('لا يوجد', Colors.grey[300]!),
                _buildLegendItem(
                    'قليل', theme.colorScheme.primary.withAlpha(64)),
                _buildLegendItem(
                    'متوسط', theme.colorScheme.primary.withAlpha(128)),
                _buildLegendItem('كثير', theme.colorScheme.primary),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر شرح اللون
  Widget _buildLegendItem(String label, Color color) {
    return Column(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }
}
