import 'package:flutter/material.dart';
import '../models/quran_model.dart';
import 'quran_service.dart';
import '../utils/logger.dart';

/// مزود بيانات القرآن الكريم
class QuranProvider extends ChangeNotifier {
  final QuranService _quranService = QuranService();
  List<Surah> _surahs = [];
  List<Ayah> _currentAyahs = [];
  bool _isLoading = false;
  String _error = '';
  bool _isInitialized = false;

  /// قائمة السور
  List<Surah> get surahs => _surahs;

  /// قائمة الآيات الحالية
  List<Ayah> get currentAyahs => _currentAyahs;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String get error => _error;

  /// تهيئة مزود البيانات
  Future<void> initialize() async {
    if (_isInitialized) return;
    await loadSurahs();
    _isInitialized = true;
  }

  /// تحميل قائمة السور - محسن للأداء
  Future<void> loadSurahs() async {
    // تحقق من وجود السور مسبقاً
    if (_surahs.isNotEmpty) {
      return; // لا حاجة لإعادة التحميل
    }

    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      _surahs = await _quranService.getSurahs();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      AppLogger.error('خطأ في تحميل قائمة السور: $e');
      _error = 'حدث خطأ أثناء تحميل قائمة السور';
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحميل آيات سورة محددة
  Future<void> loadAyahs(int surahNumber) async {
    _isLoading = true;
    _error = '';
    _currentAyahs = [];
    notifyListeners();

    try {
      _currentAyahs = await _quranService.getAyahs(surahNumber);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      AppLogger.error('خطأ في تحميل آيات السورة: $e');
      _error = 'حدث خطأ أثناء تحميل آيات السورة';
      _isLoading = false;
      notifyListeners();
    }
  }

  /// إعادة تعيين الحالة
  void reset() {
    _surahs = [];
    _currentAyahs = [];
    _isLoading = false;
    _error = '';
    _isInitialized = false;
    notifyListeners();
  }
}
