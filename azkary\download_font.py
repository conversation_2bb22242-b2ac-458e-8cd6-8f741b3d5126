#!/usr/bin/env python3
"""
Script to download Noto Sans Arabic font from Google Fonts
"""
import requests
import os

def download_font():
    # URLs for different weights of Noto Sans Arabic
    font_urls = {
        'NotoSansArabic-Regular.ttf': 'https://fonts.gstatic.com/s/notosansarabic/v18/nwpxtLGrOAZMl5nJ_wfgRg3DrWFZWsnVBJ_sS6tlqHHFlhQ5l3sQWIHPqzCfyGyvu3CBFQLaig.ttf',
        'NotoSansArabic-Bold.ttf': 'https://fonts.gstatic.com/s/notosansarabic/v18/nwpxtLGrOAZMl5nJ_wfgRg3DrWFZWsnVBJ_sS6tlqHHFlhQ5l3sQWIHPqzCfyGyvuyhBFQLaig.ttf',
        'NotoSansArabic-Light.ttf': 'https://fonts.gstatic.com/s/notosansarabic/v18/nwpxtLGrOAZMl5nJ_wfgRg3DrWFZWsnVBJ_sS6tlqHHFlhQ5l3sQWIHPqzCfyGyvu0pAFQLaig.ttf',
        'NotoSansArabic-Medium.ttf': 'https://fonts.gstatic.com/s/notosansarabic/v18/nwpxtLGrOAZMl5nJ_wfgRg3DrWFZWsnVBJ_sS6tlqHHFlhQ5l3sQWIHPqzCfyGyvuyRBFQLaig.ttf'
    }
    
    # Create fonts directory if it doesn't exist
    fonts_dir = 'assets/fonts'
    os.makedirs(fonts_dir, exist_ok=True)
    
    for filename, url in font_urls.items():
        print(f"Downloading {filename}...")
        try:
            response = requests.get(url)
            response.raise_for_status()
            
            filepath = os.path.join(fonts_dir, filename)
            with open(filepath, 'wb') as f:
                f.write(response.content)
            print(f"✓ Downloaded {filename}")
        except Exception as e:
            print(f"✗ Failed to download {filename}: {e}")

if __name__ == "__main__":
    download_font()
