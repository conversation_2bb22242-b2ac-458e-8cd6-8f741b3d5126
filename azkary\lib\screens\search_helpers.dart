import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/quran_model.dart';
import '../services/quran_provider.dart';
import 'surah_detail_screen.dart';
import '../widgets/enhanced_animations.dart';
import '../utils/logger.dart';

/// بناء زر العودة العائم
Widget buildFloatingBackButton(BuildContext context, Color color) {
  final theme = Theme.of(context);
  final isDarkMode = theme.brightness == Brightness.dark;

  // استخدام لون السمة الرئيسي إذا لم يتم تحديد لون
  final buttonColor = color;
  final backgroundColor = isDarkMode ? theme.colorScheme.surface : Colors.white;

  return Container(
    decoration: BoxDecoration(
      shape: BoxShape.circle,
      color: buttonColor.withAlpha(40),
      boxShadow: [
        BoxShadow(
          color: buttonColor.withAlpha(isDarkMode ? 60 : 100),
          blurRadius: 10,
          spreadRadius: 1,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: FloatingActionButton(
      onPressed: () {
        // إلغاء البحث والعودة للقائمة الرئيسية
        final quranProvider = Provider.of<QuranProvider>(
          context,
          listen: false,
        );
        quranProvider.cancelSearch();

        // إضافة تأثير اهتزاز خفيف
        HapticFeedback.lightImpact();
      },
      backgroundColor: backgroundColor,
      foregroundColor: buttonColor,
      elevation: 0,
      mini: true,
      child: Icon(Icons.arrow_back, color: buttonColor, size: 20),
    ),
  );
}

/// بناء بطاقة خيار البحث
Widget buildSearchOptionCard({
  required BuildContext context,
  required String title,
  required String subtitle,
  required IconData icon,
  required Color color,
  required VoidCallback onTap,
  bool isSelected = false,
}) {
  return Card(
    elevation: isSelected ? 3 : 2,
    color: isSelected ? color.withAlpha(20) : null,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
      side:
          isSelected
              ? BorderSide(color: color.withAlpha(100), width: 1.5)
              : BorderSide.none,
    ),
    child: InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // أيقونة الخيار
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withAlpha(isSelected ? 50 : 30),
                shape: BoxShape.circle,
                border:
                    isSelected ? Border.all(color: color, width: 1.5) : null,
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Icon(icon, color: color, size: 24),
                  if (isSelected)
                    Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: color,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 1.5),
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 10,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(width: 16),

            // نص الخيار
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isSelected ? color : null,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color:
                          isSelected
                              ? color.withAlpha(200)
                              : Theme.of(
                                context,
                              ).colorScheme.onSurface.withAlpha(180),
                    ),
                  ),
                ],
              ),
            ),

            // سهم أو علامة اختيار
            isSelected
                ? Icon(Icons.check_circle, size: 20, color: color)
                : Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(128),
                ),
          ],
        ),
      ),
    ),
  );
}

/// بناء قائمة نتائج البحث في الآيات
Widget buildAyahSearchResults(
  BuildContext context,
  List<AyahSearchResult> results,
) {
  final theme = Theme.of(context);

  // طباعة معلومات تشخيصية
  AppLogger.info('buildAyahSearchResults: عدد النتائج = ${results.length}');

  // عرض رسالة عندما لا توجد نتائج بحث
  if (results.isEmpty) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: theme.colorScheme.primary.withAlpha(128),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد نتائج للبحث',
            style: theme.textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'حاول البحث بكلمات أخرى',
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  return ListView.builder(
    padding: const EdgeInsets.all(16),
    itemCount: results.length,
    itemBuilder: (context, index) {
      final result = results[index];
      AppLogger.info(
        'عرض نتيجة البحث ${index + 1}: ${result.surah.name} - الآية ${result.ayah.numberInSurah}',
      );
      return buildAyahResultCard(context, result, index);
    },
  );
}

/// بناء بطاقة نتيجة البحث في الآيات
Widget buildAyahResultCard(
  BuildContext context,
  AyahSearchResult result,
  int index,
) {
  final theme = Theme.of(context);
  final surah = result.surah;
  final ayah = result.ayah;
  final searchQuery = result.searchQuery;

  return FadeInAnimation(
    delay: Duration(milliseconds: index * 100),
    child: Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => SurahDetailScreen(
                    surah: surah,
                    initialAyahNumber: ayah.numberInSurah,
                  ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // معلومات السورة ورقم الآية
              Row(
                children: [
                  // رقم السورة
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withAlpha(25),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: theme.colorScheme.primary.withAlpha(75),
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        '${surah.number}',
                        style: TextStyle(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // اسم السورة ورقم الآية
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          surah.name,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          'الآية ${ayah.numberInSurah}',
                          style: theme.textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),

                  // نوع السورة
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color:
                          surah.revelationType == 'Meccan'
                              ? Colors.amber.withAlpha(50)
                              : Colors.green.withAlpha(50),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      surah.revelationType == 'Meccan' ? 'مكية' : 'مدنية',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color:
                            surah.revelationType == 'Meccan'
                                ? Colors.amber.shade800
                                : Colors.green.shade800,
                      ),
                    ),
                  ),
                ],
              ),

              const Divider(height: 24),

              // نص الآية مع تمييز الكلمات المطابقة
              buildHighlightedAyahText(
                context,
                ayah.text,
                searchQuery,
                result.matchPositions,
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

/// بناء نص الآية مع تمييز الكلمات المطابقة
Widget buildHighlightedAyahText(
  BuildContext context,
  String text,
  String query,
  List<int> matchPositions,
) {
  final theme = Theme.of(context);

  // إذا لم تكن هناك مواقع مطابقة، نعرض النص كاملاً
  if (matchPositions.isEmpty) {
    return Text(
      text,
      textDirection: TextDirection.rtl,
      textAlign: TextAlign.right,
      style: theme.textTheme.titleMedium?.copyWith(
        height: 1.8,
        fontFamily: 'Uthmani', // استخدام خط عثماني للآيات
      ),
    );
  }

  // إنشاء قائمة من TextSpan لتمييز الكلمات المطابقة
  List<TextSpan> spans = [];
  int lastIndex = 0;

  // التأكد من أن matchPositions تحتوي على أزواج من الفهارس
  for (int i = 0; i < matchPositions.length - 1; i += 2) {
    // التأكد من وجود فهرس البداية والنهاية
    if (i + 1 >= matchPositions.length) break;

    final startIndex = matchPositions[i];
    final endIndex = matchPositions[i + 1];

    // التأكد من صحة الفهارس
    if (startIndex < 0 || endIndex > text.length || startIndex >= endIndex) {
      continue;
    }

    // إضافة النص قبل الكلمة المطابقة
    if (startIndex > lastIndex) {
      spans.add(
        TextSpan(
          text: text.substring(lastIndex, startIndex),
          style: theme.textTheme.titleMedium?.copyWith(
            height: 1.8,
            fontFamily: 'Uthmani',
          ),
        ),
      );
    }

    // إضافة الكلمة المطابقة مع تمييزها
    spans.add(
      TextSpan(
        text: text.substring(startIndex, endIndex),
        style: theme.textTheme.titleMedium?.copyWith(
          height: 1.8,
          fontFamily: 'Uthmani',
          color: Colors.green,
          fontWeight: FontWeight.bold,
          backgroundColor: Colors.green.withAlpha(30),
        ),
      ),
    );

    lastIndex = endIndex;
  }

  // إضافة النص المتبقي بعد آخر كلمة مطابقة
  if (lastIndex < text.length) {
    spans.add(
      TextSpan(
        text: text.substring(lastIndex),
        style: theme.textTheme.titleMedium?.copyWith(
          height: 1.8,
          fontFamily: 'Uthmani',
        ),
      ),
    );
  }

  return RichText(
    textDirection: TextDirection.rtl,
    textAlign: TextAlign.right,
    text: TextSpan(children: spans),
  );
}
