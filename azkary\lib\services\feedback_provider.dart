import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:audioplayers/audioplayers.dart';

/// مزود التعليقات البصرية والصوتية
class FeedbackProvider extends ChangeNotifier {
  // حالة تفعيل الاهتزاز
  bool _isHapticEnabled = true;

  // حالة تفعيل التأثيرات الصوتية
  bool _isSoundEnabled = false;

  // حالة تفعيل التأثيرات البصرية
  bool _isVisualEnabled = true;

  // مشغل الصوت
  final AudioPlayer _audioPlayer = AudioPlayer();

  // الحصول على حالة تفعيل الاهتزاز
  bool get isHapticEnabled => _isHapticEnabled;

  // الحصول على حالة تفعيل التأثيرات الصوتية
  bool get isSoundEnabled => _isSoundEnabled;

  // الحصول على حالة تفعيل التأثيرات البصرية
  bool get isVisualEnabled => _isVisualEnabled;

  // تهيئة المزود
  FeedbackProvider() {
    _loadPreferences();
  }

  // تحميل التفضيلات
  Future<void> _loadPreferences() async {
    // سيتم تنفيذه لاحقاً باستخدام SharedPreferences
    notifyListeners();
  }

  // تشغيل الصوت
  Future<void> playSound(String soundName) async {
    if (!_isSoundEnabled) return;
    try {
      await _audioPlayer.play(AssetSource('sounds/$soundName'));
    } catch (e) {
      debugPrint('Error playing sound: $e');
    }
  }

  // تبديل حالة تفعيل الاهتزاز
  Future<void> toggleHaptic() async {
    _isHapticEnabled = !_isHapticEnabled;
    // سيتم حفظ التفضيلات لاحقاً
    notifyListeners();
  }

  // تبديل حالة تفعيل التأثيرات الصوتية
  Future<void> toggleSound() async {
    _isSoundEnabled = !_isSoundEnabled;
    // سيتم حفظ التفضيلات لاحقاً
    notifyListeners();
  }

  // تبديل حالة تفعيل التأثيرات البصرية
  Future<void> toggleVisual() async {
    _isVisualEnabled = !_isVisualEnabled;
    // سيتم حفظ التفضيلات لاحقاً
    notifyListeners();
  }

  // تنفيذ اهتزاز خفيف
  void lightHapticFeedback() {
    if (_isHapticEnabled) {
      HapticFeedback.lightImpact();
    }
    playSound('click.mp3');
  }

  // تنفيذ اهتزاز متوسط
  void mediumHapticFeedback() {
    if (_isHapticEnabled) {
      HapticFeedback.mediumImpact();
    }
    playSound('click.mp3');
  }

  // تنفيذ اهتزاز قوي
  void heavyHapticFeedback() {
    if (_isHapticEnabled) {
      HapticFeedback.heavyImpact();
    }
    playSound('click.mp3');
  }

  // تنفيذ اهتزاز النقر
  void clickHapticFeedback() {
    if (_isHapticEnabled) {
      HapticFeedback.selectionClick();
    }
    playSound('click.mp3');
  }

  // عرض رسالة تأكيد
  void showSuccessSnackBar(
    BuildContext context,
    String message, {
    IconData? icon,
  }) {
    if (!_isVisualEnabled) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon ?? Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        duration: const Duration(seconds: 2),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  // عرض رسالة معلومات
  void showInfoSnackBar(
    BuildContext context,
    String message, {
    IconData? icon,
  }) {
    if (!_isVisualEnabled) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon ?? Icons.info_outline, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        duration: const Duration(seconds: 2),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
}
