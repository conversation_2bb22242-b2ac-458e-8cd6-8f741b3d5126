import 'package:flutter/material.dart';

/// امتدادات للألوان لتجنب استخدام withOpacity المهجور
extension ColorExtensions on Color {
  /// بديل لـ withOpacity
  Color withAlpha(int alpha) {
    return Color.fromARGB(alpha, r.round(), g.round(), b.round());
  }

  /// بديل لـ withOpacity مع قيمة عشرية
  Color withOpacityValue(double opacity) {
    return Color.fromARGB(
      (opacity * 255).round(),
      r.round(),
      g.round(),
      b.round(),
    );
  }
}

/// فئة مساعدة للألوان الشائعة
class AppColors {
  static Color primaryWithOpacity(Color primary, double opacity) {
    return Color.fromARGB(
      (opacity * 255).round(),
      primary.r.round(),
      primary.g.round(),
      primary.b.round(),
    );
  }

  static Color blackWithOpacity(double opacity) {
    return Color.fromARGB((opacity * 255).round(), 0, 0, 0);
  }

  static Color whiteWithOpacity(double opacity) {
    return Color.fromARGB((opacity * 255).round(), 255, 255, 255);
  }

  static Color greyWithOpacity(double opacity) {
    return Color.fromARGB((opacity * 255).round(), 158, 158, 158);
  }

  static Color greenWithOpacity(double opacity) {
    return Color.fromARGB((opacity * 255).round(), 76, 175, 80);
  }

  static Color redWithOpacity(double opacity) {
    return Color.fromARGB((opacity * 255).round(), 244, 67, 54);
  }

  static Color orangeWithOpacity(double opacity) {
    return Color.fromARGB((opacity * 255).round(), 255, 152, 0);
  }
}
