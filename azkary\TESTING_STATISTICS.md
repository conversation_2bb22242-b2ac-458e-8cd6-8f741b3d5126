# دليل اختبار ميزة الإحصائيات والتتبع

## خطوات الاختبار الأساسية

### 1. اختبار الوصول إلى الميزة

#### الخطوات:
1. افتح التطبيق
2. انتقل إلى شاشة "المزيد" من التنقل السفلي
3. ابحث عن أيقونة "الإحصائيات والتتبع" في قسم الميزات الإسلامية
4. اضغط على الأيقونة

#### النتيجة المتوقعة:
- يجب أن تفتح شاشة الإحصائيات مع ثلاثة تبويبات
- يجب أن يظهر شريط التبويبات بوضوح
- يجب أن يكون التبويب الأول (الإحصائيات) مفعلاً افتراضياً

### 2. اختبار تبويب الإحصائيات

#### الخطوات:
1. تأكد من أنك في تبويب "الإحصائيات"
2. تحقق من وجود الأقسام التالية:
   - إحصائيات اليوم
   - السلسلة المتتالية
   - إحصائيات الأسبوع
   - إحصائيات الشهر
   - مخطط التقدم

#### النتيجة المتوقعة:
- يجب أن تظهر جميع الأقسام حتى لو كانت فارغة
- يجب أن تكون النصوص باللغة العربية ومحاذاة بشكل صحيح
- يجب أن تتطابق الألوان مع ثيم التطبيق

### 3. اختبار الخريطة الحرارية

#### الخطوات:
1. انتقل إلى تبويب "الخريطة الحرارية"
2. تحقق من وجود:
   - شبكة الأيام (90 يوماً)
   - تسميات أيام الأسبوع
   - تسميات الشهور
   - شرح الألوان
   - معلومات الملخص

#### النتيجة المتوقعة:
- يجب أن تظهر شبكة منظمة للأيام
- يجب أن تكون الألوان متدرجة حسب النشاط
- يجب أن تظهر معلومات إضافية عند النقر على أي يوم

### 4. اختبار تبويب الإنجازات

#### الخطوات:
1. انتقل إلى تبويب "الإنجازات"
2. تحقق من وجود:
   - إجمالي النقاط في الأعلى
   - قسم "الإنجازات المحققة" (إذا وجدت)
   - قسم "الإنجازات القادمة"

#### النتيجة المتوقعة:
- يجب أن تظهر الإنجازات الافتراضية
- يجب أن تكون الإنجازات المقفلة مميزة بصرياً
- يجب أن تظهر أشرطة التقدم بشكل صحيح

## اختبار الوظائف المتقدمة

### 1. اختبار تسجيل الإحصائيات

#### الخطوات:
1. اذهب إلى أي فئة من فئات الأذكار
2. أكمل بعض الأذكار
3. ارجع إلى شاشة الإحصائيات
4. تحقق من تحديث البيانات

#### النتيجة المتوقعة:
- يجب أن تتحدث إحصائيات اليوم
- يجب أن تزيد النقاط
- يجب أن تتحدث نسبة الإكمال

### 2. اختبار السلسلة المتتالية

#### الخطوات:
1. أكمل جميع الأذكار في يوم واحد
2. تحقق من السلسلة المتتالية
3. كرر العملية في اليوم التالي (محاكاة)

#### النتيجة المتوقعة:
- يجب أن تزيد السلسلة الحالية
- يجب أن تتحدث أطول سلسلة إذا لزم الأمر

### 3. اختبار الإنجازات

#### الخطوات:
1. أكمل الأذكار بما يكفي لفتح إنجاز
2. تحقق من تحديث حالة الإنجاز
3. تحقق من زيادة النقاط الإجمالية

#### النتيجة المتوقعة:
- يجب أن ينتقل الإنجاز من "مقفل" إلى "مفتوح"
- يجب أن تظهر تأثيرات بصرية للإنجاز المفتوح
- يجب أن تزيد النقاط الإجمالية

## اختبار الثيمات والألوان

### 1. اختبار الوضع الفاتح

#### الخطوات:
1. تأكد من أن التطبيق في الوضع الفاتح
2. افتح شاشة الإحصائيات
3. تصفح جميع التبويبات

#### النتيجة المتوقعة:
- يجب أن تكون الخلفيات فاتحة
- يجب أن تكون النصوص مقروءة
- يجب أن تكون الألوان متناسقة

### 2. اختبار الوضع المعتم

#### الخطوات:
1. غير إلى الوضع المعتم من الإعدادات
2. افتح شاشة الإحصائيات
3. تصفح جميع التبويبات

#### النتيجة المتوقعة:
- يجب أن تكون الخلفيات معتمة
- يجب أن تكون النصوص مقروءة
- يجب أن تتطابق الألوان مع ثيم Twitter المعتم

### 3. اختبار الوضع الليلي

#### الخطوات:
1. غير إلى الوضع الليلي من الإعدادات
2. افتح شاشة الإحصائيات
3. تصفح جميع التبويبات

#### النتيجة المتوقعة:
- يجب أن تكون الخلفيات سوداء
- يجب أن تكون النصوص بيضاء ومقروءة
- يجب أن تكون الألوان متناسقة

## اختبار الأداء

### 1. اختبار سرعة التحميل

#### الخطوات:
1. افتح شاشة الإحصائيات
2. قس الوقت المستغرق للتحميل
3. انتقل بين التبويبات

#### النتيجة المتوقعة:
- يجب أن تحمل الشاشة في أقل من 3 ثوانٍ
- يجب أن يكون الانتقال بين التبويبات سلساً
- يجب أن تظهر مؤشرات التحميل عند الحاجة

### 2. اختبار استهلاك الذاكرة

#### الخطوات:
1. افتح شاشة الإحصائيات
2. تصفح جميع التبويبات عدة مرات
3. راقب استهلاك الذاكرة

#### النتيجة المتوقعة:
- يجب ألا يزيد استهلاك الذاكرة بشكل مفرط
- يجب ألا تحدث تسريبات في الذاكرة

## اختبار الأخطاء والحالات الاستثنائية

### 1. اختبار البيانات الفارغة

#### الخطوات:
1. افتح التطبيق لأول مرة (بيانات فارغة)
2. افتح شاشة الإحصائيات
3. تصفح جميع التبويبات

#### النتيجة المتوقعة:
- يجب أن تظهر رسائل مناسبة للبيانات الفارغة
- يجب ألا تحدث أخطاء أو crashes
- يجب أن تكون الواجهة مستقرة

### 2. اختبار قاعدة البيانات المعطلة

#### الخطوات:
1. محاكاة عطل في قاعدة البيانات
2. افتح شاشة الإحصائيات

#### النتيجة المتوقعة:
- يجب أن تظهر رسائل خطأ مناسبة
- يجب ألا يتوقف التطبيق
- يجب أن تعمل باقي الميزات

### 3. اختبار الاتصال البطيء

#### الخطوات:
1. محاكاة اتصال بطيء
2. افتح شاشة الإحصائيات

#### النتيجة المتوقعة:
- يجب أن تظهر مؤشرات التحميل
- يجب أن تحمل البيانات تدريجياً
- يجب ألا تتجمد الواجهة

## اختبار التوافق

### 1. اختبار أحجام الشاشات المختلفة

#### الخطوات:
1. اختبر على هواتف بأحجام مختلفة
2. اختبر في الوضع العمودي والأفقي

#### النتيجة المتوقعة:
- يجب أن تتكيف الواجهة مع جميع الأحجام
- يجب أن تبقى النصوص مقروءة
- يجب أن تبقى الأزرار قابلة للنقر

### 2. اختبار إصدارات Android المختلفة

#### الخطوات:
1. اختبر على Android 7.0+
2. تحقق من عمل جميع الميزات

#### النتيجة المتوقعة:
- يجب أن تعمل جميع الميزات على الإصدارات المدعومة
- يجب ألا تحدث أخطاء متعلقة بالإصدار

## تقرير الأخطاء

### معلومات مطلوبة عند الإبلاغ عن خطأ:
1. وصف مفصل للخطأ
2. خطوات إعادة إنتاج الخطأ
3. الجهاز المستخدم ونظام التشغيل
4. إصدار التطبيق
5. لقطات شاشة إن أمكن
6. سجلات الأخطاء من AppLogger

### أماكن البحث عن السجلات:
- تحقق من وحدة التحكم في Android Studio
- ابحث عن رسائل AppLogger
- تحقق من سجلات قاعدة البيانات

---

هذا الدليل يغطي الجوانب الأساسية لاختبار ميزة الإحصائيات. يُنصح بإجراء هذه الاختبارات بانتظام للتأكد من استقرار الميزة.
